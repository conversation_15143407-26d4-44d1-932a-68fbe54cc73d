---
output:
  html_document: default
  pdf_document: default
---

# Metadatos ICFES
icfes:
  competencia:
    - interpretacion_representacion
  nivel_dificultad: 2
  contenido:
    categoria: estadistica
    tipo: generico
  contexto: laboral
  eje_axial: eje4
  componente: aleatorio
```{r data generation, echo = FALSE, results = "hide"}
# Load necessary libraries
library(exams)
library(testthat)

# Data Generation
# set.seed(123) # Example seed for testing
set.seed(sample(1:1000, 1))

# Generate random mode and median
generar_datos_validos <- function() {
  # Generate random mode between 85 and 95
  moda <- sample(85:95, 1)
  # Generate random median strictly greater than mode
  mediana <- moda + sample(1:5, 1)

  # Validate that the values make sense for the problem
  expect_true(moda < mediana, "La moda debe ser menor que la mediana")
  expect_true(moda >= 85 && moda <= 95, "La moda debe estar entre 85 y 95")
  expect_true(mediana >= 86 && mediana <= 100, "La mediana debe estar entre 86 y 100")

  return(list(moda = moda, mediana = mediana))
}

# Generate valid data
datos <- generar_datos_validos()
moda <- datos$moda
mediana <- datos$mediana

# --- Define Answer Options ---
# Opciones basadas en la imagen proporcionada
opciones <- c(
  sprintf("Sí, porque la mediana es el resultado de uno de los estudios y la moda el de todos los demás, entonces el menor es %d%%.", moda),
  sprintf("No, porque la mediana podría ser esa, por dos estudios muy cercanos a 100%%, dos iguales y uno menor que %d%%.", moda),
  sprintf("Sí, porque la mediana debe tener dos valores mayores y dos menores, y la moda garantiza que los dos menores son %d%%.", moda),
  sprintf("No, porque la mediana es resultado de un estudio y la moda el de otros dos, luego alguno de los restantes podría ser menor que %d%%.", moda)
)

# Identificar la respuesta correcta (opción C en la imagen)
correct_index <- 3

# Mezclar las opciones de respuesta
set.seed(sample(1:1000, 1))
shuffled_indices <- sample(1:4)
shuffled_options <- opciones[shuffled_indices]

# Encontrar la nueva posición de la respuesta correcta después de mezclar
solution_position <- which(shuffled_indices == correct_index)

# Crear string de solución para R-exams
solution_string <- mchoice2string(1:4 == solution_position)

# --- Solution Explanation ---
# Explicación detallada de la solución con formato mejorado
solution_explanation <- sprintf(
"La afirmación es **CORRECTA**. Analicemos por qué:

1.  **Datos y Orden:** Tenemos 5 porcentajes de efectividad. Si los ordenamos de menor a mayor, los llamamos $x_1$, $x_2$, $x_3$, $x_4$, $x_5$.

2.  **Mediana:** Al ser 5 datos (un número impar), la mediana es el valor central (el 3º). Se nos dice que la mediana es %d%%. Por lo tanto, $x_3 = %d%%$. La lista ordenada es: $x_1$, $x_2$, $%d%%$, $x_4$, $x_5$. Esto implica que $x_1$ es menor o igual que $x_2$, que a su vez es menor o igual que $%d%%$, y que $%d%%$ es menor o igual que $x_4$, que a su vez es menor o igual que $x_5$.

3.  **Moda:** La moda es el valor que más se repite. Se nos dice que la moda es %d%% y es *única*.
    *   Para que %d%% sea la moda en 5 datos, debe aparecer al menos dos veces.
    *   ¿Podría aparecer 3 o más veces? No, porque si apareciera 3 veces, al ordenar los datos, el valor central (la mediana, $x_3$) sería %d%%, lo cual contradice que la mediana es %d%% (ya que %d%% es diferente de %d%%).
    *   Por lo tanto, %d%% aparece exactamente *dos* veces.
    *   Al ser la *única* moda, ningún otro valor puede repetirse dos o más veces. Esto significa que $x_4$ es diferente de %d%%, $x_5$ es diferente de %d%%, y $x_4$ es diferente de $x_5$.
    *   Es importante destacar que no puede haber otra moda con mayor frecuencia (como por ejemplo, tres valores iguales), ya que en ese caso, ese valor sería la moda y no %d%%.

4.  **Combinando Mediana y Moda:** Sabemos que la lista ordenada es $x_1$, $x_2$, $%d%%$, $x_4$, $x_5$ y que dos de estos valores son %d%%.
    *   Dado que %d%% (la moda) es menor que %d%% (la mediana), los dos valores de %d%% deben estar necesariamente en las posiciones por debajo de la mediana. Es decir, deben ser $x_1$ y $x_2$.
    *   La única estructura posible para la lista ordenada es: $%d%%$, $%d%%$, $%d%%$, $x_4$, $x_5$, donde además se cumple que %d%% es menor que $x_4$, que a su vez es menor que $x_5$.

5.  **Conclusión sobre el Mínimo:** En la lista ordenada ($%d%%$, $%d%%$, $%d%%$, $x_4$, $x_5$), el valor mínimo es $x_1$, que hemos determinado que debe ser %d%%.

**Tabla de datos ordenados:**

| Posición | Valor | Descripción |
|:--------:|:-----:|:-----------:|
| $x_1$ | %d%% | Moda (valor mínimo) |
| $x_2$ | %d%% | Moda (repetido) |
| $x_3$ | %d%% | Mediana (valor central) |
| $x_4$ | > %d%% | Mayor que la mediana |
| $x_5$ | > %d%% | Mayor que la mediana |

Por lo tanto, es correcto afirmar que la efectividad mínima mostrada fue %d%%.",
mediana, mediana, mediana, mediana, mediana, # Step 2 args
moda, moda, moda, mediana, moda, mediana, # Step 3 args
moda, mediana, mediana, moda, # Step 3 continuation
mediana, moda, # Step 4 args
moda, mediana, moda, # Step 4 continuation
moda, moda, mediana, mediana, # Step 4 conclusion
moda, moda, mediana, moda, # Step 5 args
moda, moda, mediana, mediana, mediana, # Tabla args
moda # Final conclusion
)

# --- Validación final ---
# Verificar que la solución es correcta
expect_true(opciones[correct_index] == opciones[3], "La respuesta correcta debe ser la opción C")
expect_true(grepl("Sí, porque la mediana debe tener dos valores mayores y dos menores", opciones[correct_index]), "La respuesta correcta debe contener el texto esperado")

# Verificar explícitamente el escenario de los datos
# Construimos un conjunto de datos que cumple con las condiciones:
# - La moda (valor que aparece exactamente 2 veces) es el valor mínimo
# - La mediana es el valor central
# - No hay otra moda con mayor frecuencia
datos_ejemplo <- c(moda, moda, mediana, mediana + 1, mediana + 2)
expect_true(median(datos_ejemplo) == mediana, "La mediana del conjunto de datos debe ser correcta")
expect_true(as.numeric(names(sort(table(datos_ejemplo), decreasing = TRUE)[1])) == moda, "La moda del conjunto de datos debe ser correcta")
expect_true(min(datos_ejemplo) == moda, "El valor mínimo debe ser la moda")
expect_true(sum(datos_ejemplo == moda) == 2, "La moda debe aparecer exactamente 2 veces")
expect_true(sum(datos_ejemplo == mediana) == 1, "La mediana debe aparecer exactamente 1 vez")
expect_true(sum(datos_ejemplo > mediana) == 2, "Debe haber exactamente 2 valores mayores que la mediana")
```

Question
========
Una empresa farmacéutica calculó el porcentaje de efectividad de un tratamiento para una enfermedad. Para ello, hizo cinco estudios y, de cada estudio, se conoció el porcentaje de efectividad que tuvo el tratamiento. De los cinco porcentajes, solo hay una moda que es `r moda`% y la mediana es `r mediana`%.

¿Es CORRECTO afirmar que la efectividad mínima mostrada en los estudios fue de `r moda`%?

Answerlist
----------
* `r shuffled_options[1]`
* `r shuffled_options[2]`
* `r shuffled_options[3]`
* `r shuffled_options[4]`

Solution
========

`r solution_explanation`

Meta-information
================
exname: Efectividad tratamiento farmaceutico ICFES
extype: schoice
exsolution: `r solution_string`
exshuffle: TRUE
exnumeric: FALSE
extol: 0.01
expoints: 1
