# Walkthrough: Fracciones Reparto Premio v4

Este documento proporciona un análisis detallado del código del archivo
`fracciones_reparto_premio_v4.Rmd`, explicando cada sección y su funcionalidad.

## Cambios Principales en la Versión 4

### 🔄 **Nueva Lógica Matemática**
- **Cambio fundamental**: El segundo puesto ahora se calcula como fracción del dinero restante después del primer puesto
- **Ventaja**: Garantiza matemáticamente que primer puesto > segundo puesto > tercer puesto
- **Impacto**: Elimina la necesidad de validaciones complejas de orden

### 📊 **Pregunta Modificada**
- **Antes**: ¿Qué cantidad recibirá el tercer puesto?
- **Ahora**: ¿Qué cantidad recibirá el segundo puesto?
- **Razón**: Mayor variabilidad en las respuestas y mejor distribución de dificultad

### 🎯 **Estrategia de Distractores Renovada**
- **Antes**: Patrones predecibles (5%, 20%, 30% del premio)
- **Ahora**: Generación completamente aleatoria sin patrones
- **Beneficio**: Respuesta correcta puede aparecer en cualquier posición

### 📈 **Rango de Premios Ampliado**
- **Antes**: 30-150 millones (valores específicos)
- **Ahora**: 30-400 millones (todos los múltiplos de 2)
- **Resultado**: Mayor variabilidad y realismo en los problemas

## Tabla de Contenidos

1. [Metadatos y Configuración](#1-metadatos-y-configuración)
2. [Configuración del Entorno R](#2-configuración-del-entorno-r)
3. [Aleatorización de Variables](#3-aleatorización-de-variables)
4. [Generación de Fracciones](#4-generación-de-fracciones)
5. [Cálculos Matemáticos](#5-cálculos-matemáticos)
6. [Generación de Opciones](#6-generación-de-opciones)
7. [Visualización](#7-visualización)
8. [Estructura del Ejercicio](#8-estructura-del-ejercicio)
9. [Solución Detallada](#9-solución-detallada)
10. [Metainformación](#10-metainformación)

---

## 1. Metadatos y Configuración

```yaml
---
output:
  html_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float", "tikz", "xcolor"]
  word_document: default
icfes:
  competencia: Resolución de problemas
  componente: Numérico-variacional
  afirmacion: Resuelve problemas que requieren el uso de fracciones y porcentajes
  evidencia: Utiliza fracciones para resolver problemas de reparto proporcional
  nivel: Medio
  tematica: Fracciones y operaciones con fracciones
---
```

**Propósito**: Define los formatos de salida y los metadatos educativos según el marco ICFES.

**Elementos clave**:

- **Múltiples formatos**: HTML, PDF y Word
- **Dependencias LaTeX**: Para gráficos y colores
- **Clasificación ICFES**: Competencia, componente, afirmación, evidencia, nivel y temática

---

## 2. Configuración del Entorno R

```r
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{xcolor}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

**Propósito**: Establece el entorno de trabajo para generar ejercicios consistentes.

**Elementos clave**:

- **Localización numérica**: Asegura el uso de punto decimal
- **Configuración LaTeX**: Para gráficos TikZ
- **Librerías**: exams, reticulate, digest, testthat, knitr
- **Configuración de figuras**: PNG y PDF con alta resolución
- **Integración Python**: Para generar visualizaciones

---

## 3. Aleatorización de Variables

### 3.1 Sistema de Concordancia de Género para Contextos

```r
# Aleatorización del contexto del problema con concordancia de género
contextos_data <- data.frame(
  nombre = c("ciudad", "localidad", "comunidad", "comarca", "provincia", "zona",
             "municipio", "distrito", "territorio", "sector", "barrio", "pueblo"),
  genero = c("f", "f", "f", "f", "f", "f",
             "m", "m", "m", "m", "m", "m"),
  articulo = c("una", "una", "una", "una", "una", "una",
               "un", "un", "un", "un", "un", "un"),
  stringsAsFactors = FALSE
)
contexto_seleccionado <- contextos_data[sample(nrow(contextos_data), 1), ]
contexto <- contexto_seleccionado$nombre
articulo_contexto <- contexto_seleccionado$articulo
```

**Propósito**: Garantiza concordancia de género entre artículos y sustantivos.

**Innovación**:

- **Data frame estructurado**: Cada término tiene su género y artículo correspondiente
- **Términos balanceados**: 6 femeninos y 6 masculinos
- **Concordancia automática**: Elimina errores como "un ciudad" o "una municipio"

### 3.2 Sistema de Concordancia para Competencias

```r
# Aleatorización del tipo de competencia con concordancia de género
competencias_data <- data.frame(
  nombre = c("carrera", "competencia atlética", "olimpiada deportiva",
             "justa deportiva", "prueba atlética", "competencia deportiva",
             "maratón", "torneo deportivo", "evento deportivo",
             "campeonato", "concurso deportivo", "certamen deportivo"),
  genero = c("f", "f", "f", "f", "f", "f",
             "m", "m", "m", "m", "m", "m"),
  articulo = c("una", "una", "una", "una", "una", "una",
               "un", "un", "un", "un", "un", "un"),
  stringsAsFactors = FALSE
)
competencia_seleccionada <- competencias_data[sample(nrow(competencias_data), 1), ]
competencia <- competencia_seleccionada$nombre
articulo_competencia <- competencia_seleccionada$articulo
```

**Propósito**: Asegura coherencia semántica en tipos de competencia.

**Beneficios**:

- **Variedad temática**: Desde carreras hasta certámenes
- **Coherencia gramatical**: Evita "una maratón" o "un carrera"
- **Escalabilidad**: Fácil agregar nuevos términos

### 3.3 Otros Términos Aleatorios

```r
# Aleatorización del grupo de edad
edades <- c(
  "menores de 15 años", "menores de 16 años", "menores de 14 años",
  "niños y niñas de 10 a 15 años", "jóvenes de 12 a 15 años",
  "estudiantes de primaria y secundaria", "categoría infantil",
  "categoría juvenil", "niños y adolescentes", "estudiantes menores de edad"
)
grupo_edad <- sample(edades, 1)

# Aleatorización del premio total (en millones)
# Todos los múltiplos de 2 desde 30 hasta 400 para mayor variabilidad
premios_posibles <- seq(30, 400, by = 2)
premio_total <- sample(premios_posibles, 1)
```

**Propósito**: Crear contextos diversos y realistas.

**Características**:

- **Grupos de edad**: Variedad de descripciones para el público objetivo
- **Premios ampliados**: Rango de 30-400 millones (múltiplos de 2) para mayor variabilidad
- **Realismo mejorado**: Valores más diversos y representativos

### 3.4 Términos del Enunciado

```r
# Aleatorización de términos para el enunciado
terminos_premiar <- c("premiar", "recompensar", "reconocer", "galardonar", "incentivar")
termino_premiar <- sample(terminos_premiar, 1)

terminos_participantes <- c("participantes", "competidores", "concursantes", "deportistas")
termino_participantes <- sample(terminos_participantes, 1)

terminos_cuenta <- c("cuenta con", "dispone de", "tiene asignados", "ha destinado", "ha reservado")
termino_cuenta <- sample(terminos_cuenta, 1)

terminos_repartiran <- c("repartirán", "distribuirán", "dividirán", "asignarán", "otorgarán")
termino_repartiran <- sample(terminos_repartiran, 1)

terminos_puestos <- c("primeros puestos", "ganadores", "mejores lugares", "primeros lugares", "mejores posiciones")
termino_puestos <- sample(terminos_puestos, 1)
```

**Propósito**: Enriquecer el vocabulario y evitar repetición.

**Mejoras implementadas**:

- **Coherencia de género**: Eliminados términos problemáticos como "atletas"
- **Concordancia numérica**: "tiene asignados" concuerda con "millones"
- **Variedad semántica**: Múltiples formas de expresar la misma idea

### 3.5 Sistema de Concordancia para Términos de Dinero

```r
# Aleatorización de términos para dinero con concordancia de género
terminos_dinero_data <- data.frame(
  nombre = c("dinero", "premio", "incentivo", "monto"),
  genero = c("m", "m", "m", "m"),
  articulo_este = c("Este", "Este", "Este", "Este"),
  stringsAsFactors = FALSE
)
termino_dinero_seleccionado <- terminos_dinero_data[sample(nrow(terminos_dinero_data), 1), ]
termino_dinero <- termino_dinero_seleccionado$nombre
articulo_este_dinero <- termino_dinero_seleccionado$articulo_este
```

**Propósito**: Manejar la concordancia de "Este/Esta" con términos monetarios.

**Funcionalidad**:

- **Todos masculinos**: Los términos de dinero son masculinos en español
- **Preparado para expansión**: Estructura permite agregar términos femeninos
- **Concordancia automática**: Evita errores como "Esta dinero"

---

## 4. Generación de Fracciones

```r
# Aleatorización de fracciones para los puestos
# NUEVA LÓGICA: Segundo puesto es fracción del dinero restante (después del primer puesto)
# Definimos conjuntos de fracciones que garanticen matemáticamente el orden correcto:
# primer puesto > segundo puesto > tercer puesto
# VERIFICADOS: todos mantienen el orden estricto decreciente con la nueva lógica
conjuntos_fracciones <- list(
  c("3/8", "4/7"),   # 37.50 > 35.71 > 26.79 ✓
  c("2/5", "3/5"),   # 40.00 > 36.00 > 24.00 ✓
  c("1/2", "2/3"),   # 50.00 > 33.33 > 16.67 ✓
  c("4/9", "3/4"),   # 44.44 > 41.67 > 13.89 ✓
  c("5/12", "2/3"),  # 41.67 > 38.89 > 19.44 ✓
  c("5/13", "3/5"),  # 38.46 > 36.92 > 24.62 ✓
  c("2/5", "4/7"),   # 40.00 > 34.29 > 25.71 ✓
  c("2/5", "5/8"),   # 40.00 > 37.50 > 22.50 ✓
  c("3/7", "3/5"),   # 42.86 > 34.29 > 22.86 ✓
  c("3/7", "2/3")    # 42.86 > 38.10 > 19.05 ✓
)

# Seleccionar un conjunto aleatorio de fracciones
indice_conjunto <- sample(1:length(conjuntos_fracciones), 1)
fracciones_seleccionadas <- conjuntos_fracciones[[indice_conjunto]]

# Asignar fracciones a los puestos
fraccion_primer_puesto <- fracciones_seleccionadas[1]
fraccion_segundo_puesto <- fracciones_seleccionadas[2]
```

**Propósito**: Generar combinaciones de fracciones matemáticamente válidas con nueva lógica.

**Características del diseño mejorado**:

- **Nueva lógica matemática**: El segundo puesto se calcula del dinero restante después del primer puesto
- **Orden garantizado**: Todos los conjuntos verificados para mantener primer > segundo > tercer puesto
- **Validación explícita**: Cada conjunto incluye los porcentajes calculados como comentario
- **Variedad matemática**: Diferentes denominadores y niveles de dificultad
- **Eliminación de casos problemáticos**: Solo conjuntos que funcionan con la nueva lógica

---

## 5. Cálculos Matemáticos

### 5.1 Conversión y Validación

```r
# Convertir fracciones a valores numéricos para cálculos
convertir_fraccion <- function(fraccion) {
  partes <- strsplit(fraccion, "/")[[1]]
  return(as.numeric(partes[1]) / as.numeric(partes[2]))
}

# NUEVA LÓGICA: Segundo puesto es fracción del dinero restante
valor_primer_puesto <- convertir_fraccion(fraccion_primer_puesto)
valor_segundo_puesto_fraccion <- convertir_fraccion(fraccion_segundo_puesto)

# Calcular montos con la nueva lógica
monto_primer_puesto_exacto <- premio_total * valor_primer_puesto
dinero_restante_despues_primer_puesto <- premio_total - monto_primer_puesto_exacto
monto_segundo_puesto_exacto <- valor_segundo_puesto_fraccion * dinero_restante_despues_primer_puesto
monto_tercer_puesto_exacto <- premio_total - monto_primer_puesto_exacto - monto_segundo_puesto_exacto

# Calcular valores equivalentes para compatibilidad con el resto del código
valor_segundo_puesto <- monto_segundo_puesto_exacto / premio_total
valor_tercer_puesto <- monto_tercer_puesto_exacto / premio_total

# Verificar que el valor del tercer puesto sea positivo
test_that("El valor del tercer puesto es positivo", {
  expect_true(valor_tercer_puesto > 0)
})
```

**Propósito**: Convertir fracciones a decimales y validar la nueva lógica matemática.

**Elementos clave renovados**:

- **Función de conversión**: Transforma strings como "1/2" a 0.5
- **Nueva lógica de cálculo**: Segundo puesto se calcula del dinero restante después del primer puesto
- **Cálculos exactos**: Se mantienen valores exactos antes del redondeo
- **Compatibilidad**: Se calculan valores equivalentes para el resto del código
- **Validación automática**: Test unitario asegura valores positivos

### 5.2 Cálculo de Montos

```r
# Los montos exactos ya fueron calculados con la nueva lógica arriba

# Para efectos de presentación, redondear a una decimal si es necesario
monto_primer_puesto <- round(monto_primer_puesto_exacto, 1)
monto_segundo_puesto <- round(monto_segundo_puesto_exacto, 1)
monto_tercer_puesto <- round(monto_tercer_puesto_exacto, 1)

# Ajustar el tercer puesto para asegurar que la suma sea exactamente el premio total
suma_actual <- monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto
if (abs(suma_actual - premio_total) > 0.1) {
  diferencia <- premio_total - (monto_primer_puesto + monto_segundo_puesto)
  monto_tercer_puesto <- round(diferencia, 1)
}

# Verificar que la suma de los tres montos sea igual al premio total (con tolerancia)
test_that("La suma de los tres montos es igual al premio total", {
  suma_total <- monto_primer_puesto + monto_segundo_puesto + monto_tercer_puesto
  expect_equal(suma_total, premio_total, tolerance = 0.1)
})

# Tests adicionales para la nueva lógica
test_that("Las fracciones mantienen el orden correcto", {
  expect_true(valor_primer_puesto > valor_segundo_puesto)
  expect_true(valor_segundo_puesto >= valor_tercer_puesto - 1e-10)
})

test_that("El primer puesto recibe más que el segundo puesto", {
  expect_true(monto_primer_puesto > monto_segundo_puesto)
})

test_that("El segundo puesto recibe al menos tanto como el tercer puesto", {
  expect_true(monto_segundo_puesto >= monto_tercer_puesto - 0.1)
})
```

**Propósito**: Calcular montos exactos con la nueva lógica y manejar errores de redondeo.

**Características mejoradas**:

- **Cálculos exactos previos**: Los montos exactos se calculan antes del redondeo
- **Redondeo a decimales**: A una decimal para mayor precisión
- **Tolerancia en validaciones**: Tests con tolerancia para comparaciones de punto flotante
- **Ajuste inteligente**: Solo ajusta si la diferencia es significativa (> 0.1)
- **Tests adicionales**: Validaciones específicas para el orden de los puestos
- **Validación robusta**: Múltiples tests para asegurar coherencia matemática

---

## 6. Generación de Opciones

```r
# Generar opciones de respuesta
# La respuesta correcta es el monto del segundo puesto (CAMBIO IMPORTANTE)
respuesta_correcta <- monto_segundo_puesto

# NUEVA ESTRATEGIA: Distractores completamente aleatorios sin patrones predecibles
# para que la respuesta correcta pueda aparecer en cualquier posición

# Generar un pool amplio de distractores candidatos con variación aleatoria
set.seed(NULL)  # Asegurar aleatoriedad real
distractores_candidatos <- c(
  # Variaciones aleatorias MUY amplias para cubrir todo el espectro
  round(respuesta_correcta * runif(4, 0.15, 0.6), 1),                    # 15%-60% del correcto (muy menores)
  round(respuesta_correcta * runif(4, 0.7, 0.95), 1),                    # 70%-95% del correcto (menores)
  round(respuesta_correcta * runif(4, 1.05, 1.8), 1),                    # 105%-180% del correcto (mayores)
  round(respuesta_correcta * runif(4, 2.0, 4.5), 1),                     # 200%-450% del correcto (muy mayores)

  # Errores conceptuales con rangos amplios
  round(premio_total * (valor_primer_puesto * runif(2, 0.2, 1.2)), 1),   # Variación amplia del primer puesto
  round(premio_total * (valor_segundo_puesto * runif(2, 0.3, 1.5)), 1),  # Variación amplia del segundo puesto
  round(premio_total * (valor_tercer_puesto * runif(2, 0.4, 3.0)), 1),   # Variación amplia del tercer puesto

  # Errores de cálculo con rango muy amplio
  round(respuesta_correcta + runif(3, -respuesta_correcta*0.8, respuesta_correcta*2), 1), # Errores proporcionales
  round(respuesta_correcta * runif(3, 0.3, 3.5), 1),                     # Multiplicadores muy amplios

  # Valores completamente aleatorios en rangos pedagógicamente plausibles
  round(runif(3, 5, respuesta_correcta * 0.8), 1),                       # Valores menores aleatorios
  round(runif(3, respuesta_correcta * 1.2, respuesta_correcta * 4), 1)   # Valores mayores aleatorios
)

# Filtrar distractores válidos (diferentes de la respuesta correcta y positivos)
distractores_validos <- distractores_candidatos[distractores_candidatos != respuesta_correcta & distractores_candidatos > 0]

# Asegurar que tenemos suficientes distractores únicos
distractores_validos <- unique(distractores_validos)

# SELECCIÓN COMPLETAMENTE ALEATORIA - SIN PATRONES FORZADOS
# Simplemente seleccionar 3 distractores al azar del pool disponible
distractores_seleccionados <- sample(distractores_validos, 3)
distractor1 <- distractores_seleccionados[1]
distractor2 <- distractores_seleccionados[2]
distractor3 <- distractores_seleccionados[3]

# Crear un vector con todas las opciones y mezclarlas
opciones <- c(respuesta_correcta, distractor1, distractor2, distractor3)
names(opciones) <- c("correcta", "distractor1", "distractor2", "distractor3")
opciones_mezcladas <- sample(opciones)

# Identificar la posición de la respuesta correcta en las opciones mezcladas
indice_correcto <- which(opciones_mezcladas == respuesta_correcta)

# Crear el vector de solución para r-exams
solucion <- rep(0, 4)
solucion[indice_correcto] <- 1
```

**Propósito**: Crear opciones de respuesta realistas y completamente aleatorias.

**Nueva estrategia de distractores revolucionaria**:

- **Cambio de pregunta**: Ahora pregunta por el segundo puesto en lugar del tercero
- **Aleatoriedad total**: Sin patrones predecibles, la respuesta correcta puede estar en cualquier posición
- **Pool amplio**: Genera múltiples categorías de distractores candidatos
- **Rangos variables**: Desde 15% hasta 450% del valor correcto
- **Errores conceptuales**: Incluye confusiones con otros puestos
- **Selección aleatoria**: Los 3 distractores se seleccionan completamente al azar
- **Eliminación de sesgos**: No hay patrones que permitan identificar la respuesta correcta por posición

---

## 7. Visualización

### 7.1 Configuración de Colores

```r
# Aleatorización de colores para la tabla TikZ
paletas_colores <- list(
  c("#4285F4", "#EA4335", "#FBBC05", "#34A853"),  # Google colors
  c("#1F77B4", "#FF7F0E", "#2CA02C", "#D62728"),  # Tableau colors
  c("#003f5c", "#58508d", "#bc5090", "#ff6361"),  # Viridis-like
  c("#0073C2", "#EFC000", "#868686", "#CD534C"),  # IBM colors
  c("#7F3C8D", "#11A579", "#3969AC", "#F2B701")   # Colorbrewer
)
paleta_seleccionada <- sample(paletas_colores, 1)[[1]]
color_encabezado <- paleta_seleccionada[1]
color_primer_puesto <- paleta_seleccionada[2]
color_segundo_puesto <- paleta_seleccionada[3]
color_tercer_puesto <- paleta_seleccionada[4]
```

**Propósito**: Proporcionar variedad visual con paletas de colores profesionales.

**Características**:

- **Paletas temáticas**: Google, Tableau, Viridis, IBM, Colorbrewer
- **Consistencia visual**: Cada paleta tiene colores armoniosos
- **Accesibilidad**: Colores con buen contraste

### 7.2 Generación de Tabla con Python

```python
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.gridspec import GridSpec
import numpy as np

# Configurar colores
color_encabezado = "', color_encabezado, '"
color_primer_puesto = "', color_primer_puesto, '"
color_segundo_puesto = "', color_segundo_puesto, '"
color_tercer_puesto = "', color_tercer_puesto, '"

# Crear figura y configurar grid
fig = plt.figure(figsize=(8, 3.5))
gs = GridSpec(4, 2, height_ratios=[1, 1, 1, 1], width_ratios=[3, 7])

# Código actualizado que muestra "del dinero restante" para el segundo puesto

plt.tight_layout(pad=0.5)
plt.savefig("tabla_distribucion.png", dpi=150, bbox_inches="tight")
plt.savefig("tabla_distribucion.pdf", dpi=150, bbox_inches="tight")
plt.close()
```

**Propósito**: Crear una tabla visual atractiva que muestre la distribución del premio con la nueva lógica.

**Ventajas de usar Python**:

- **Flexibilidad gráfica**: matplotlib ofrece control total sobre el diseño
- **Calidad profesional**: Gráficos de alta resolución
- **Compatibilidad**: Genera PNG y PDF para diferentes usos
- **Actualización automática**: El texto se actualiza para mostrar "del dinero restante" para el segundo puesto

---

## 8. Estructura del Ejercicio

### 8.1 Pregunta Principal

```markdown
En `r articulo_contexto` `r contexto` se realizará `r articulo_competencia` `r competencia` para `r grupo_edad`. Para `r termino_premiar` a los `r termino_participantes`, `r if(contexto_seleccionado$genero == "f") "la" else "el"` `r contexto` `r termino_cuenta` `r premio_total` millones de pesos, que se `r termino_repartiran` entre los tres `r termino_puestos`, como se indica a continuación:

¿Qué cantidad de `r termino_dinero` recibirá el segundo puesto?
```

**Propósito**: Presentar el problema de manera clara y contextualizada.

**Elementos de concordancia**:

- **Artículos variables**: `articulo_contexto` y `articulo_competencia`
- **Concordancia condicional**: "la/el" según el género del contexto
- **Vocabulario diverso**: Términos aleatorios enriquecen el lenguaje
- **Tiempo futuro**: "recibirá" mantiene consistencia temporal
- **Pregunta actualizada**: Ahora pregunta por el segundo puesto en lugar del tercero

### 8.2 Lista de Respuestas

```markdown
Answerlist
----------
- `r opciones_mezcladas[1]` millones.
- `r opciones_mezcladas[2]` millones.
- `r opciones_mezcladas[3]` millones.
- `r opciones_mezcladas[4]` millones.
```

**Propósito**: Presentar las opciones en formato estándar de r-exams.

---

## 9. Solución Detallada

```markdown
Para resolver este problema, debemos calcular qué cantidad del premio total corresponde al segundo puesto, considerando que este se calcula como una fracción del dinero restante después del primer puesto.

### Paso 1: Identificar los datos del problema
- Premio total: `r premio_total` millones de pesos
- Primer puesto: `r fraccion_primer_puesto` del `r termino_dinero` total
- Segundo puesto: `r fraccion_segundo_puesto` del `r termino_dinero` restante (después del primer puesto)
- Tercer puesto: el `r termino_dinero` restante (después del primer y segundo puesto)

### Paso 2: Calcular el monto del primer puesto
- Primer puesto: `r fraccion_primer_puesto` = `r round(valor_primer_puesto, 4)`
- Monto del primer puesto = `r round(valor_primer_puesto, 4)` × `r premio_total` = `r round(monto_primer_puesto_exacto, 2)` millones

### Paso 3: Calcular el dinero restante después del primer puesto
- Dinero restante = `r premio_total` - `r round(monto_primer_puesto_exacto, 2)` = `r round(dinero_restante_despues_primer_puesto, 2)` millones

### Paso 4: Calcular el monto del segundo puesto
El segundo puesto recibe `r fraccion_segundo_puesto` del dinero restante:
- Segundo puesto: `r fraccion_segundo_puesto` = `r round(valor_segundo_puesto_fraccion, 4)`
- Monto del segundo puesto = `r round(valor_segundo_puesto_fraccion, 4)` × `r round(dinero_restante_despues_primer_puesto, 2)` = `r round(monto_segundo_puesto_exacto, 2)` millones
- Redondeando: `r monto_segundo_puesto` millones de pesos

**Nota:** El segundo puesto se calcula como una fracción del dinero restante después del primer puesto, lo que garantiza matemáticamente que el primer puesto siempre reciba más que el segundo.

### Verificación
Comprobemos que nuestro cálculo del segundo puesto es correcto:

**Cálculo paso a paso:**
- Primer puesto: `r fraccion_primer_puesto` × `r premio_total` = `r round(monto_primer_puesto_exacto, 2)` millones
- Dinero restante: `r premio_total` - `r round(monto_primer_puesto_exacto, 2)` = `r round(dinero_restante_despues_primer_puesto, 2)` millones
- Segundo puesto: `r fraccion_segundo_puesto` × `r round(dinero_restante_despues_primer_puesto, 2)` = `r round(monto_segundo_puesto_exacto, 2)` millones

Por lo tanto, el segundo puesto recibirá `r monto_segundo_puesto` millones de pesos.
```

**Propósito**: Proporcionar una explicación paso a paso del proceso de solución con la nueva lógica.

**Características pedagógicas mejoradas**:

- **Estructura clara**: Pasos numerados y organizados
- **Nueva lógica explicada**: Se explica claramente que el segundo puesto se calcula del dinero restante
- **Cálculos explícitos**: Cada operación se muestra detalladamente
- **Nota pedagógica**: Se explica por qué esta lógica garantiza el orden correcto
- **Verificación paso a paso**: Comprobación detallada del cálculo
- **Lenguaje adaptativo**: Usa los términos aleatorios del problema

---

## 10. Metainformación

```markdown
Meta-information
================
exname: fracciones_reparto_premio
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Aritmética|Fracciones|Reparto proporcional
```

**Propósito**: Proporcionar metadatos para el sistema r-exams.

**Elementos**:

- **exname**: Identificador único del ejercicio
- **extype**: Tipo de pregunta (selección múltiple)
- **exsolution**: Vector binario con la respuesta correcta
- **exshuffle**: Permite mezclar las opciones
- **exsection**: Categorización temática jerárquica

---

## Características Innovadoras del Código v4

### 1. Sistema de Concordancia de Género
- **Problema resuelto**: Evita errores como "una maratón" o "el ciudad"
- **Implementación**: Data frames con género y artículos correspondientes
- **Escalabilidad**: Fácil agregar nuevos términos manteniendo coherencia

### 2. Nueva Lógica Matemática Revolucionaria
- **Cambio fundamental**: Segundo puesto calculado del dinero restante después del primer puesto
- **Garantía de orden**: Matemáticamente imposible que segundo > primer puesto
- **Eliminación de validaciones complejas**: La lógica inherente asegura el orden correcto
- **Realismo mejorado**: Refleja mejor cómo se distribuyen premios en la realidad

### 3. Validación Matemática Automática Mejorada
- **Tests unitarios ampliados**: Verifican tanto cálculos como orden de puestos
- **Tolerancia para punto flotante**: Comparaciones robustas con tolerancia numérica
- **Manejo de redondeo inteligente**: Ajusta automáticamente solo cuando es necesario
- **Coherencia garantizada**: Múltiples niveles de validación

### 4. Generación de Distractores Completamente Aleatoria
- **Revolución en estrategia**: Eliminación total de patrones predecibles
- **Pool amplio**: Múltiples categorías de distractores candidatos
- **Rangos variables**: Desde 15% hasta 450% del valor correcto
- **Selección aleatoria pura**: Sin sesgos de posición
- **Eliminación de patrones**: Imposible identificar respuesta correcta por posición

### 5. Rango de Premios Ampliado
- **Variabilidad extrema**: De 30-400 millones (múltiplos de 2)
- **Realismo mejorado**: Valores más diversos y representativos
- **Mayor complejidad**: Problemas más variados y desafiantes

### 6. Integración Python-R
- **Visualizaciones profesionales**: matplotlib para gráficos de calidad
- **Flexibilidad**: Fácil modificar colores y diseño
- **Compatibilidad**: Genera múltiples formatos de imagen
- **Actualización automática**: Texto se adapta a la nueva lógica

### 7. Aleatorización Inteligente
- **Variabilidad controlada**: Cada elemento puede variar independientemente
- **Coherencia semántica**: Los términos aleatorios mantienen sentido
- **Escalabilidad**: Fácil agregar nuevas variaciones
- **Pregunta variable**: Cambio de tercero a segundo puesto aumenta variabilidad

---

## Conclusión

La versión 4 de este código representa una evolución significativa en la generación automática de ejercicios educativos, incorporando mejoras revolucionarias:

### Avances Técnicos Principales:

- **Nueva lógica matemática**: Garantiza orden correcto de puestos de forma inherente
- **Aleatoriedad total**: Eliminación de patrones predecibles en distractores
- **Variabilidad extrema**: Rango ampliado de premios y mayor diversidad
- **Validación robusta**: Tests unitarios con tolerancia para punto flotante

### Beneficios Educativos:

- **Mayor realismo**: La nueva lógica refleja mejor distribuciones reales de premios
- **Dificultad variable**: Imposible identificar respuesta correcta por patrones
- **Precisión mejorada**: Cálculos más exactos con manejo inteligente de redondeo
- **Escalabilidad**: Fácil expansión y modificación del sistema

### Calidad del Código:

- **Rigor matemático**: Cálculos precisos y validados con múltiples tests
- **Coherencia lingüística**: Concordancia de género y número mantenida
- **Variabilidad controlada**: Decenas de miles de versiones únicas posibles
- **Calidad visual**: Gráficos profesionales que se adaptan automáticamente
- **Estándares educativos**: Alineado con el marco ICFES

### Impacto Pedagógico:

La implementación demuestra cómo la tecnología puede crear contenido educativo 
de alta calidad que:

- Mantiene la precisión matemática
- Preserva la riqueza lingüística del español
- Elimina sesgos en la presentación de opciones
- Proporciona experiencias de aprendizaje más auténticas y desafiantes

Esta versión establece un nuevo estándar para la generación automática de 
ejercicios matemáticos, combinando innovación técnica con excelencia pedagógica.
