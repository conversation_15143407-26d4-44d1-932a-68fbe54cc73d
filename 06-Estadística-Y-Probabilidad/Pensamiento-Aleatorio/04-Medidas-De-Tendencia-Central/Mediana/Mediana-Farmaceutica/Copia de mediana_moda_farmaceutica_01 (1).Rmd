```{r data generation, echo = FALSE, results = "hide"}
library(exams)

# Generar valores aleatorios
set.seed(sample(1:1000, 1))
moda <- sample(85:95, 1)
mediana <- moda + sample(1:5, 1)

# Asegurar que la mediana sea impar
if(mediana %% 2 == 0) {
  mediana <- mediana + 1
}

# Generar las opciones de respuesta
opciones <- c(
  paste0("Sí, porque la mediana es el resultado de uno de los estudios y la moda el de todos los demás, entonces el menor es ", moda, "%."),
  paste0("No, porque la mediana podría ser esa, por dos estudios muy cercanos a 100%, dos iguales y uno menor que ", moda, "%."),
  paste0("Sí, porque la mediana debe tener dos valores mayores y dos menores, y la moda garantiza que los dos menores son ", moda, "%."),
  paste0("No, porque la mediana es resultado de un estudio y la moda el de otros dos, luego alguno de los restantes podría ser menor que ", moda, "%.")
)

# Mezclar las opciones de respuesta
opciones <- sample(opciones)

# Identificar la respuesta correcta
solucion <- which(grepl("Sí, porque la mediana debe tener dos valores mayores y dos menores", opciones))

```

Question
========

Una empresa farmacéutica calculó el porcentaje de efectividad de un tratamiento para una enfermedad. Para ello, hizo cinco estudios y, de cada estudio, se conoció el porcentaje de efectividad que tuvo el tratamiento. De los cinco porcentajes, solo hay una moda que es `r moda`% y la mediana es `r mediana`%.

¿Es CORRECTO afirmar que la efectividad mínima mostrada en los estudios fue de `r moda`%?

Answerlist
----------
* `r opciones[1]`
* `r opciones[2]`
* `r opciones[3]`
* `r opciones[4]`

Solution
========

La respuesta correcta es: `r opciones[solucion]`

La afirmación es correcta porque:

1. Tenemos cinco estudios en total.
2. La mediana es `r mediana`%, lo que significa que este valor está en el medio de los cinco resultados cuando se ordenan de menor a mayor.
3. La moda es `r moda`%, y es única, lo que significa que este valor aparece más de una vez en los resultados.

Dado que la mediana divide el conjunto de datos en dos partes iguales, debe haber dos valores menores o iguales a `r mediana`% y dos valores mayores o iguales a `r mediana`%.

La única forma de satisfacer estas condiciones es con la siguiente distribución:

1. `r moda`% (valor mínimo, parte de la moda)
2. `r moda`% (parte de la moda)
3. `r mediana`% (mediana)
4. Un valor mayor que `r mediana`%
5. Un valor mayor que `r mediana`%

Por lo tanto, podemos afirmar con certeza que la efectividad mínima mostrada en los estudios fue de `r moda`%, ya que este es el valor más bajo posible que satisface todas las condiciones dadas.

Meta-information
================
exname: Efectividad de tratamiento farmacéutico
extype: schoice
exsolution: `r mchoice2string(solucion == 1:4)`
exshuffle: TRUE