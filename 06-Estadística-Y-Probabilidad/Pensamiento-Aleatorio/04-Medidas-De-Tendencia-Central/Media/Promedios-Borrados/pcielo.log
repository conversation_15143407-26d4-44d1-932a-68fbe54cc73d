This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024/Arch Linux) (preloaded format=pdflatex 2025.2.5)  5 FEB 2025 21:11
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**pcielo.tex
(./pcielo.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size12.clo
File: size12.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(/usr/share/texmf-dist/tex/latex/base/inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks17
\inpenc@posthook=\toks18
)
(/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks19
)
(/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count196
\Gm@cntv=\count197
\c@Gm@tempcnt=\count198
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks20
)
(/usr/share/texmf-dist/tex/latex/graphics/color.sty
Package: color 2022/01/06 v1.3d Standard LaTeX Color (DPC)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package color Info: Driver file: pdftex.def on input line 149.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
)
(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx))
(/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks21
\ex@=\dimen149
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count199
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count266
\leftroot@=\count267
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count268
\DOTSCASE@=\count269
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count270
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count271
\dotsspace@=\muskip17
\c@parentequation=\count272
\dspbrk@lvl=\count273
\tag@help=\toks22
\row@=\count274
\column@=\count275
\maxfields@=\count276
\andhelp@=\toks23
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks24
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks25
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen158
\lightrulewidth=\dimen159
\cmidrulewidth=\dimen160
\belowrulesep=\dimen161
\belowbottomsep=\dimen162
\aboverulesep=\dimen163
\abovetopsep=\dimen164
\cmidrulesep=\dimen165
\cmidrulekern=\dimen166
\defaultaddspace=\dimen167
\@cmidla=\count277
\@cmidlb=\count278
\@aboverulesep=\dimen168
\@belowrulesep=\dimen169
\@thisruleclass=\count279
\@lastruleclass=\count280
\@thisrulewidth=\dimen170
)
(/usr/share/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2023-11-01 v4.19 Multi-page Table package (DPC)
\LTleft=\skip53
\LTright=\skip54
\LTpre=\skip55
\LTpost=\skip56
\LTchunksize=\count281
\LTcapwidth=\dimen171
\LT@head=\box53
\LT@firsthead=\box54
\LT@foot=\box55
\LT@lastfoot=\box56
\LT@gbox=\box57
\LT@cols=\count282
\LT@rows=\count283
\c@LT@tables=\count284
\c@LT@chunks=\count285
\LT@p@ftn=\toks26
)
(/usr/share/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip57
\enit@outerparindent=\dimen172
\enit@toks=\toks27
\enit@inbox=\box58
\enit@count@id=\count286
\enitdp@description=\count287
)
(/usr/share/texmf-dist/tex/latex/fancyhdr/fancyhdr.sty
Package: fancyhdr 2022/11/09 v4.1 Extensive control of page headers and footers

\f@nch@headwidth=\skip58
\f@nch@O@elh=\skip59
\f@nch@O@erh=\skip60
\f@nch@O@olh=\skip61
\f@nch@O@orh=\skip62
\f@nch@O@elf=\skip63
\f@nch@O@erf=\skip64
\f@nch@O@olf=\skip65
\f@nch@O@orf=\skip66
)
(/usr/share/texmf-dist/tex/latex/titling/titling.sty
Package: titling 2009/09/04 v2.1d maketitle typesetting
\thanksmarkwidth=\skip67
\thanksmargin=\skip68
\droptitle=\skip69
)
(/usr/share/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2024/02/07 v24.2 The Babel package
\babel@savecnt=\count288
\U@D=\dimen173
\l@unhyphenated=\language89

(/usr/share/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read2
\bbl@dirlevel=\count289

(/usr/share/texmf-dist/tex/generic/babel-spanish/spanish.ldf
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system
\es@quottoks=\toks28
\es@quotdepth=\count290
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
))
(/usr/share/texmf-dist/tex/generic/babel/locale/es/babel-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 11.
)
(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count291
\l__pdf_internal_box=\box59
) (./pcielo.aux
LaTeX Info: Redefining \. on input line 8.
LaTeX Info: Redefining \% on input line 8.
)
\openout1 = `pcielo.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 46.
LaTeX Font Info:    ... okay on input line 46.
*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(28.45274pt, 540.60239pt, 28.45274pt)
* v-part:(T,H,B)=(85.35826pt, 674.33032pt, 85.35826pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=540.60239pt
* \textheight=674.33032pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-23.91173pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=35.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count292
\scratchdimen=\dimen174
\scratchbox=\box60
\nofMPsegments=\count293
\nofMParguments=\count294
\everyMPshowfont=\toks29
\MPscratchCnt=\count295
\MPscratchDim=\dimen175
\MPnumerator=\count296
\makeMPintoPDFobject=\count297
\everyMPtoPDFconversion=\toks30
)
LaTeX Info: Redefining \. on input line 46.
LaTeX Info: Redefining \% on input line 46.

! You can't use `macro parameter character #' in horizontal mode.
<recently read> ##
                  
l.55   #
        #Question##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.55   ##
         Question##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.55   ##Question#
                  #
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.55   ##Question##
                   
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in vertical mode.
<recently read> ##
                  
l.57     \item #
                #Questionlist##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in vertical mode.
l.57     \item ##
                 Questionlist##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.57     \item ##Questionlist#
                              #
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.57     \item ##Questionlist##
                               
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
<recently read> ##
                  
l.60   #
        #Solution##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.60   ##
         Solution##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.60   ##Solution#
                  #
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.60   ##Solution##
                   
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in vertical mode.
<recently read> ##
                  
l.62     \item #
                #Solutionlist##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in vertical mode.
l.62     \item ##
                 Solutionlist##
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.62     \item ##Solutionlist#
                              #
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

! You can't use `macro parameter character #' in horizontal mode.
l.62     \item ##Solutionlist##
                               
Sorry, but I'm not programmed to handle this case;
I'll just pretend that you didn't ask for it.
If you're in the wrong mode, you might be able to
return to the right one by typing `I}' or `I$' or `I\par'.

[1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}] (./pcielo.aux
LaTeX Info: Redefining \. on input line 8.
LaTeX Info: Redefining \% on input line 8.
)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********
 ) 
Here is how much of TeX's memory you used:
 4603 strings out of 474116
 71804 string characters out of 5750250
 1928187 words of memory out of 5000000
 26949 multiletter control sequences out of 15000+600000
 560485 words of font info for 44 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 57i,5n,65p,223b,365s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/public/amsfonts/cm/cmbx12.pfb></usr/share/
texmf-dist/fonts/type1/public/amsfonts/cm/cmcsc10.pfb></usr/share/texmf-dist/fo
nts/type1/public/amsfonts/cm/cmr12.pfb>
Output written on pcielo.pdf (1 page, 36593 bytes).
PDF statistics:
 23 PDF objects out of 1000 (max. 8388607)
 13 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 500000)
 1 words of extra memory for PDF output out of 10000 (max. 10000000)

