This is pdfTeX, Version 3.141592653-2.6-1.40.26 (TeX Live 2024/Arch Linux) (preloaded format=pdflatex 2025.2.2)  5 FEB 2025 07:34
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**solpcielo.tex
(./solpcielo.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(/usr/share/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(/usr/share/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count188
\c@section=\count189
\c@subsection=\count190
\c@subsubsection=\count191
\c@paragraph=\count192
\c@subparagraph=\count193
\c@figure=\count194
\c@table=\count195
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
)
(/usr/share/texmf-dist/tex/latex/geometry/geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(/usr/share/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
)
(/usr/share/texmf-dist/tex/generic/iftex/ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(/usr/share/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
))
\Gm@cnth=\count196
\Gm@cntv=\count197
\c@Gm@tempcnt=\count198
\Gm@bindingoffset=\dimen141
\Gm@wd@mp=\dimen142
\Gm@odd@mp=\dimen143
\Gm@even@mp=\dimen144
\Gm@layoutwidth=\dimen145
\Gm@layoutheight=\dimen146
\Gm@layouthoffset=\dimen147
\Gm@layoutvoffset=\dimen148
\Gm@dimlist=\toks18
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(/usr/share/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(/usr/share/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks19
\ex@=\dimen149
))
(/usr/share/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen150
)
(/usr/share/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count199
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count266
\leftroot@=\count267
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count268
\DOTSCASE@=\count269
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen151
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count270
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count271
\dotsspace@=\muskip16
\c@parentequation=\count272
\dspbrk@lvl=\count273
\tag@help=\toks20
\row@=\count274
\column@=\count275
\maxfields@=\count276
\andhelp@=\toks21
\eqnshift@=\dimen152
\alignsep@=\dimen153
\tagshift@=\dimen154
\tagwidth@=\dimen155
\totwidth@=\dimen156
\lineht@=\dimen157
\@envbody=\toks22
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks23
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(/usr/share/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(/usr/share/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(/usr/share/texmf-dist/tex/latex/base/latexsym.sty
Package: latexsym 1998/08/17 v2.2e Standard LaTeX package (lasy symbols)
\symlasy=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lasy' in version `bold'
(Font)                  U/lasy/m/n --> U/lasy/b/n on input line 52.
)
(/usr/share/texmf-dist/tex/latex/cancel/cancel.sty
Package: cancel 2013/04/12 v2.2 Cancel math terms
)
(/usr/share/texmf-dist/tex/latex/amscls/amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks24
\thm@bodyfont=\toks25
\thm@headfont=\toks26
\thm@notefont=\toks27
\thm@headpunct=\toks28
\thm@preskip=\skip53
\thm@postskip=\skip54
\thm@headsep=\skip55
\dth@everypar=\toks29
)
(/usr/share/texmf-dist/tex/latex/tools/rawfonts.sty
Package: rawfonts 1994/05/08 Low-level LaTeX 2.09 font compatibility

(/usr/share/texmf-dist/tex/latex/tools/somedefs.sty
Package: somedefs 1994/06/01 v0.03 Toolkit for optional definitions
)
LaTeX Font Info:    Trying to load font information for U+lasy on input line 49
.

(/usr/share/texmf-dist/tex/latex/base/ulasy.fd
File: ulasy.fd 1998/08/17 v2.2e LaTeX symbol font definitions
))
(/usr/share/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(/usr/share/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(/usr/share/texmf-dist/tex/latex/graphics-def/pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
)
(/usr/share/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(/usr/share/texmf-dist/tex/latex/colortbl/colortbl.sty
Package: colortbl 2024/02/20 v1.0g Color table columns (DPC)

(/usr/share/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen158
\ar@mcellbox=\box53
\extrarowheight=\dimen159
\NC@list=\toks30
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box54
)
\everycr=\toks31
\minrowclearance=\skip58
\rownum=\count277
)
(/usr/share/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks32
\pgfutil@tempdima=\dimen160
\pgfutil@tempdimb=\dimen161
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box55
)
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(/usr/share/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.
)
\Gin@req@height=\dimen162
\Gin@req@width=\dimen163
)
(/usr/share/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks33
\pgfkeys@temptoks=\toks34

(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeyslibraryfiltered.code.te
x
\pgfkeys@tmptoks=\toks35
))
\pgf@x=\dimen164
\pgf@y=\dimen165
\pgf@xa=\dimen166
\pgf@ya=\dimen167
\pgf@xb=\dimen168
\pgf@yb=\dimen169
\pgf@xc=\dimen170
\pgf@yc=\dimen171
\pgf@xd=\dimen172
\pgf@yd=\dimen173
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count278
\c@pgf@countb=\count279
\c@pgf@countc=\count280
\c@pgf@countd=\count281
\t@pgf@toka=\toks36
\t@pgf@tokb=\toks37
\t@pgf@tokc=\toks38
\pgf@sys@id@count=\count282
 (/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count283
\pgfsyssoftpath@bigbuffer@items=\count284
)
(/usr/share/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count285
\pgfmath@box=\box56
\pgfmath@toks=\toks39
\pgfmath@stack@operand=\toks40
\pgfmath@stack@operation=\toks41
)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonometric.code
.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison.code.te
x) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerarithmetics
.code.tex) (/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count286
))
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen175
\pgf@picmaxx=\dimen176
\pgf@picminy=\dimen177
\pgf@picmaxy=\dimen178
\pgf@pathminx=\dimen179
\pgf@pathmaxx=\dimen180
\pgf@pathminy=\dimen181
\pgf@pathmaxy=\dimen182
\pgf@xx=\dimen183
\pgf@xy=\dimen184
\pgf@yx=\dimen185
\pgf@yy=\dimen186
\pgf@zx=\dimen187
\pgf@zy=\dimen188
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen189
\pgf@path@lasty=\dimen190
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen191
\pgf@shorten@start@additional=\dimen192
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box57
\pgf@hbox=\box58
\pgf@layerbox@main=\box59
\pgf@picture@serial@count=\count287
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen193
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformations.code.t
ex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen194
\pgf@pt@y=\dimen195
\pgf@pt@temp=\dimen196
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing.code.te
x
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen197
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen198
\pgf@sys@shading@range@num=\count288
\pgf@shadingcount=\count289
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box60
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box61
)
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen199
\pgf@nodesepend=\dimen256
)
(/usr/share/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen257
\pgffor@skip=\dimen258
\pgffor@stack=\toks42
\pgffor@toks=\toks43
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers.code.te
x
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count290
\pgfplotmarksize=\dimen259
)
\tikz@lastx=\dimen260
\tikz@lasty=\dimen261
\tikz@lastxsaved=\dimen262
\tikz@lastysaved=\dimen263
\tikz@lastmovetox=\dimen264
\tikz@lastmovetoy=\dimen265
\tikzleveldistance=\dimen266
\tikzsiblingdistance=\dimen267
\tikz@figbox=\box62
\tikz@figbox@bg=\box63
\tikz@tempbox=\box64
\tikz@tempbox@bg=\box65
\tikztreelevel=\count291
\tikznumberofchildren=\count292
\tikznumberofcurrentchild=\count293
\tikz@fig@count=\count294
 (/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count295
\pgfmatrixcurrentcolumn=\count296
\pgf@matrix@numberofcolumns=\count297
)
\tikz@expandcount=\count298

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
topaths.code.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2021/05/15 v1.18.1 Data Visualization (1.18.1)

(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
\t@pgfplots@toka=\toks44
\t@pgfplots@tokb=\toks45
\t@pgfplots@tokc=\toks46
\pgfplots@tmpa=\dimen268
\c@pgfplots@coordindex=\count299
\c@pgfplots@scanlineindex=\count300

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_l
oader.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex)
Package pgfplots: loading complementary utilities for your pgf version...
\t@pgf@toka=\toks47
\t@pgf@tokb=\toks48
\t@pgf@tokc=\toks49

(/usr/share/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldpgfsupp_p
gfutil-common-lists.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsliststructure
ext.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray.code.te
x
\c@pgfplotsarray@tmp=\count301
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatrix.code.t
ex)
(/usr/share/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshared.code.t
ex
\c@pgfplotstable@counta=\count302
\t@pgfplotstable@a=\toks50
)
(/usr/share/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque.code.te
x) (/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.surfshading
.code.tex
\c@pgfplotslibrarysurf@no=\count303

(/usr/share/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surfshading.
pgfsys-pdftex.def)))
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.code.tex
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.tex)))
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.code.tex
\pgfdecoratedcompleteddistance=\dimen269
\pgfdecoratedremainingdistance=\dimen270
\pgfdecoratedinputsegmentcompleteddistance=\dimen271
\pgfdecoratedinputsegmentremainingdistance=\dimen272
\pgf@decorate@distancetomove=\dimen273
\pgf@decorate@repeatstate=\count304
\pgfdecorationsegmentamplitude=\dimen274
\pgfdecorationsegmentlength=\dimen275
)
\tikz@lib@dec@box=\box66
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathmorphing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathmorphing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.pathreplacing.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.pathreplacing.code.tex))
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.contourlua
.code.tex)
\pgfplots@numplots=\count305
\pgfplots@xmin@reg=\dimen276
\pgfplots@xmax@reg=\dimen277
\pgfplots@ymin@reg=\dimen278
\pgfplots@ymax@reg=\dimen279
\pgfplots@zmin@reg=\dimen280
\pgfplots@zmax@reg=\dimen281
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
plotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.code.tex
File: pgflibraryplotmarks.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (/usr/share/texmf-dist/tex/latex/a4wide/a4wide.sty
Package: a4wide 1994/08/30

(/usr/share/texmf-dist/tex/latex/ntgclass/a4.sty
Package: a4 2023/01/10 v1.2g A4 based page layout
))
(/usr/share/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks51
\verbatim@line=\toks52
\verbatim@in@stream=\read3
)
(/usr/share/texmf/tex/latex/Sweave.sty
Package: Sweave 

(/usr/share/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
)
(/usr/share/texmf-dist/tex/latex/fancyvrb/fancyvrb.sty
Package: fancyvrb 2024/01/20 4.5c verbatim text (tvz,hv)
\FV@CodeLineNo=\count306
\FV@InFile=\read4
\FV@TabBox=\box67
\c@FancyVerbLine=\count307
\FV@StepNumber=\count308
\FV@OutFile=\write4
)
(/usr/share/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
)
(/usr/share/texmf-dist/tex/latex/base/fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
))
(/usr/share/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
(/usr/share/texmf-dist/tex/latex/xargs/xargs.sty
Package: xargs 2008/03/22 v1.1  extended macro definitions  (mpg)

(/usr/share/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(/usr/share/texmf-dist/tex/generic/xkeyval/xkeyval.tex
(/usr/share/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks53
\XKV@tempa@toks=\toks54
)
\XKV@depth=\count309
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
\xargs@max=\count310
\xargs@toksa=\toks55
\xargs@toksb=\toks56
)
(/usr/share/texmf-dist/tex/latex/tabu/tabu.sty
Package: tabu 2019/01/11 v2.9 - flexible LaTeX tabulars (FC+tabu-fixed)

(/usr/share/texmf-dist/tex/latex/varwidth/varwidth.sty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box68
\sift@deathcycles=\count311
\@vwid@loff=\dimen282
\@vwid@roff=\dimen283
)
\c@taburow=\count312
\tabu@nbcols=\count313
\tabu@cnt=\count314
\tabu@Xcol=\count315
\tabu@alloc=\count316
\tabu@nested=\count317
\tabu@target=\dimen284
\tabu@spreadtarget=\dimen285
\tabu@naturalX=\dimen286
\tabucolX=\dimen287
\tabu@Xsum=\dimen288
\extrarowdepth=\dimen289
\abovetabulinesep=\dimen290
\belowtabulinesep=\dimen291
\tabustrutrule=\dimen292
\tabu@thebody=\toks57
\tabu@footnotes=\toks58
\tabu@box=\box69
\tabu@arstrutbox=\box70
\tabu@hleads=\box71
\tabu@vleads=\box72
\tabu@cellskip=\skip59
)
(/usr/share/texmf-dist/tex/latex/tools/multicol.sty
Package: multicol 2023/03/30 v1.9f multicolumn formatting (FMi)
\c@tracingmulticols=\count318
\mult@box=\box73
\multicol@leftmargin=\dimen293
\c@unbalance=\count319
\c@collectmore=\count320
\doublecol@number=\count321
\multicoltolerance=\count322
\multicolpretolerance=\count323
\full@width=\dimen294
\page@free=\dimen295
\premulticols=\dimen296
\postmulticols=\dimen297
\multicolsep=\skip60
\multicolbaselineskip=\skip61
\partial@page=\box74
\last@line=\box75
\mc@boxedresult=\box76
\maxbalancingoverflow=\dimen298
\mult@rightbox=\box77
\mult@grightbox=\box78
\mult@firstbox=\box79
\mult@gfirstbox=\box80
\@tempa=\box81
\@tempa=\box82
\@tempa=\box83
\@tempa=\box84
\@tempa=\box85
\@tempa=\box86
\@tempa=\box87
\@tempa=\box88
\@tempa=\box89
\@tempa=\box90
\@tempa=\box91
\@tempa=\box92
\@tempa=\box93
\@tempa=\box94
\@tempa=\box95
\@tempa=\box96
\@tempa=\box97
\@tempa=\box98
\@tempa=\box99
\@tempa=\box100
\@tempa=\box101
\@tempa=\box102
\@tempa=\box103
\@tempa=\box104
\@tempa=\box105
\@tempa=\box106
\@tempa=\box107
\@tempa=\box108
\@tempa=\box109
\@tempa=\box110
\@tempa=\box111
\@tempa=\box112
\@tempa=\box113
\@tempa=\box114
\@tempa=\box115
\@tempa=\box116
\c@minrows=\count324
\c@columnbadness=\count325
\c@finalcolumnbadness=\count326
\last@try=\dimen299
\multicolovershoot=\dimen300
\multicolundershoot=\dimen301
\mult@nat@firstbox=\box117
\colbreak@box=\box118
\mc@col@check@num=\count327
)
(/usr/share/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen302
\lightrulewidth=\dimen303
\cmidrulewidth=\dimen304
\belowrulesep=\dimen305
\belowbottomsep=\dimen306
\aboverulesep=\dimen307
\abovetopsep=\dimen308
\cmidrulesep=\dimen309
\cmidrulekern=\dimen310
\defaultaddspace=\dimen311
\@cmidla=\count328
\@cmidlb=\count329
\@aboverulesep=\dimen312
\@belowrulesep=\dimen313
\@thisruleclass=\count330
\@lastruleclass=\count331
\@thisrulewidth=\dimen314
)
(/usr/share/texmf-dist/tex/latex/jknapltx/mathrsfs.sty
Package: mathrsfs 1996/01/01 Math RSFS package v1.0 (jk)
\symrsfs=\mathgroup7
)
(/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-euclide.sty
2024/02/04 5.06c tkz-euclide.sty
Package: tkz-euclide  2024/02/04 5.06c for pure Euclidean Geometry 

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
angles.code.tex
File: tikzlibraryangles.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
arrows.code.tex
File: tikzlibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.tex
File: pgflibraryarrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\arrowsize=\dimen315
))
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.meta.code.tex
File: pgflibraryarrows.meta.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowinset=\dimen316
\pgfarrowlength=\dimen317
\pgfarrowwidth=\dimen318
\pgfarrowlinewidth=\dimen319
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
backgrounds.code.tex
File: tikzlibrarybackgrounds.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@layerbox@background=\box119
\pgf@layerboxsaved@background=\box120
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
calc.code.tex
File: tikzlibrarycalc.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.markings.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.markings.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.shapes.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.shapes.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
decorations.text.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrarydecorati
ons.text.code.tex
\pgf@lib@dec@text@box=\box121
)
\tikz@lib@dec@te@box=\box122
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
intersections.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/pgflibraryintersections.code.t
ex
\pgf@intersect@solutions=\count332
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
math.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
fpu.code.tex)
\tikz@math@for@depth=\count333
\tikz@math@dimen=\dimen320
\tikz@math@toks=\toks59
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
positioning.code.tex
File: tikzlibrarypositioning.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
quotes.code.tex
File: tikzlibraryquotes.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.misc.c
ode.tex
File: pgflibraryshapes.misc.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
through.code.tex
File: tikzlibrarythrough.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/latex/l3packages/xfp/xfp.sty
(/usr/share/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2024-02-20 L3 programming layer (loader) 

(/usr/share/texmf-dist/tex/latex/l3backend/l3backend-pdftex.def
File: l3backend-pdftex.def 2024-02-20 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count334
\l__pdf_internal_box=\box123
))
Package: xfp 2024-02-18 L3 Floating point unit
)
(/usr/share/texmf-dist/tex/latex/xpatch/xpatch.sty
Package: xpatch 2020/03/25 v0.3a Extending etoolbox patching commands

(/usr/share/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2024-02-18 L3 Experimental document command parser
)
(/usr/share/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count335
))
\tkzRadius=\dimen321
\tkzLength=\dimen322
\tkz@radi=\dimen323
\tkz@ax=\dimen324
\tkz@ay=\dimen325
\tkz@bx=\dimen326
\tkz@by=\dimen327
\tkz@cx=\dimen328
\tkz@cy=\dimen329
\tkz@dx=\dimen330
\tkz@dy=\dimen331
\tkz@cntmk=\count336

Local configuration file tkz-euclide.cfg found and used
(/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-euclide.cfg
2024/02/04 5.06c tkz-euclide.cfg
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-base.tex
2024/02/04 5.06c tkz-tools-eu-base.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-utilities.tex
2024/02/04 5.06c tkz-tools-eu-utilities.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-BB.tex
2024/02/04 5.06c tkz-obj-eu-BB.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-grids.tex
2024/02/04 5.06c tkz-obj-eu-grids.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-lib-eu-marks.tex
2024/02/04 5.06c tkz-lib-eu-marks.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-text.tex
2024/02/04 5.06c tkz-tools-eu-text.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-lib-eu-shape.tex
2024/02/04 5.06c tkz-lib-eu-shape.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-axesmin.tex
2024/02/04 5.06c tkz-obj-eu-axesmin
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-colors.tex
2024/02/04 5.06c tkz-tools-eu-colors
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points.tex
2024/02/04 5.06c tkz-obj-eu-points.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-points.tex
2024/02/04 5.06c tkz-draw-eu-points.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points.tex
2024/02/04 5.06c tkz-obj-eu-points.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-by.tex
2024/02/04 5.06c tkz-tools-eu-points-by.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-math.tex
2024/02/04 5.06c tkz-tools-eu-math.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-intersections.tex
2024/02/04 5.06c tkz-tools-eu-intersections.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-with.tex
2024/02/04 5.06c tkz-obj-eu-points-with.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-spc.tex
2024/02/04 5.06c tkz-obj-el-points.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-tools-eu-angles.tex
2024/02/04 5.06c tkz-tools-eu-angles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-circles.tex
2024/02/04 5.06c tkz-obj-eu-circles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-circles-by.tex
2024/02/04 5.06c tkz-obj-eu-circles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-rnd.tex
2024/02/04 5.06c tkz-obj-eu-points-rnd.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-lines.tex
2024/02/04 5.06c tkz-obj-eu-lines.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-polygons.tex
2024/02/04 5.06c tkz-obj-eu-polygons.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-triangles.tex
2024/02/04 5.06c tkz-obj-eu-triangles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-lines.tex
2024/02/04 5.06c tkz-obj-eu-draw-lines.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-circles.tex
2024/02/04 5.06c tkz-obj-eu-draw-circles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-ellipses.tex
2024/02/04 5.06c tkz-obj-eu-draw-ellipses.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-polygons.tex
2024/02/04 5.06c tkz-obj-eu-polygons.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-angles.tex
2024/02/04 5.06c tkz-tool-eu-angles.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-sectors.tex
2024/02/04 5.06c tkz-obj-eu-sectors.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-compass.tex
2024/02/04 5.06c tkz-obj-eu-compass.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-show.tex
2024/02/04 5.06c tkz-obj-eu-show.tex
) (/usr/share/texmf-dist/tex/latex/tkz-euclide/tkz-draw-eu-protractor.tex
2024/02/04 5.06c tkz-obj-eu-protractor.tex
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
babel.code.tex
File: tikzlibrarybabel.code.tex 2023-01-15 v3.1.10 (3.1.10)
) (/usr/share/texmf-dist/tex/generic/babel/babel.sty
Package: babel 2024/02/07 v24.2 The Babel package
\babel@savecnt=\count337
\U@D=\dimen332
\l@unhyphenated=\language5

(/usr/share/texmf-dist/tex/generic/babel/txtbabel.def)
\bbl@readstream=\read5
\bbl@dirlevel=\count338

(/usr/share/texmf-dist/tex/generic/babel-spanish/spanish.ldf
Language: spanish.ldf 2021/05/27 v5.0q Spanish support from the babel system


Package babel Warning: No hyphenation patterns were preloaded for
(babel)                the language 'Spanish' into the format.
(babel)                Please, configure your TeX system to add them and
(babel)                rebuild the format. Now I will use the patterns
(babel)                preloaded for \language=0 instead on input line 51.

Package babel Info: Hyphen rules for 'spanish' set to \l@english
(babel)             (\language0). Reported on input line 52.
\es@quottoks=\toks60
\es@quotdepth=\count339
Package babel Info: Making " an active character on input line 570.
Package babel Info: Making . an active character on input line 675.
Package babel Info: Making < an active character on input line 722.
Package babel Info: Making > an active character on input line 722.
)) (/usr/share/texmf-dist/tex/generic/babel/locale/es/babel-spanish.tex
Package babel Info: Importing font and identification data for spanish
(babel)             from babel-es.ini. Reported on input line 11.
)
(/usr/share/texmf-dist/tex/latex/floatrow/floatrow.sty
Package: floatrow 2008/08/02 v0.3b floatrow: float package extension

(/usr/share/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen333
\captionmargin=\dimen334
\caption@leftmargin=\dimen335
\caption@rightmargin=\dimen336
\caption@width=\dimen337
\caption@indent=\dimen338
\caption@parindent=\dimen339
\caption@hangindent=\dimen340
Package caption Info: Standard document class detected.
)
\c@float@type=\count340
\float@exts=\toks61
\float@box=\box124
\@floatcapt=\box125
Package floatrow Info: Modified float package code loaded on input line 455.
Package floatrow Info: Modified rotfloat package code loaded on input line 473.

\FR@everyfloat=\toks62
\flrow@foot=\insert252
\FB@wd=\dimen341
\FBo@wd=\dimen342
\FBc@wd=\dimen343
\FBo@ht=\skip62
\FBc@ht=\skip63
\FBf@ht=\skip64
\FBo@max=\skip65
\FBc@max=\skip66
\FBf@max=\skip67
\c@FBl@b=\count341
\floatbox@depth=\count342
\c@FRobj=\count343
\c@FRsobj=\count344
\Xhsize=\skip68
\sXhsize=\skip69
\Zhsize=\skip70
\sZhsize=\skip71
\flrow@rowbox=\box126
\FR@Zunitlength=\dimen344
\c@FBcnt=\count345
\FPOScnt=\count346
\LTleft=\skip72
\LTright=\skip73
\LTleft=\skip74
\LTright=\skip75
\flrow@types=\toks63
)
(/usr/share/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX

(/usr/share/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(/usr/share/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(/usr/share/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(/usr/share/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(/usr/share/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(/usr/share/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(/usr/share/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(/usr/share/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(/usr/share/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(/usr/share/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(/usr/share/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(/usr/share/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count347
)
\@linkdim=\dimen345
\Hy@linkcounter=\count348
\Hy@pagecounter=\count349

(/usr/share/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(/usr/share/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count350

(/usr/share/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count351
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen346

(/usr/share/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(/usr/share/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count352
\Field@Width=\dimen347
\Fld@charsize=\dimen348
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.

(/usr/share/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count353
\c@Item=\count354
\c@Hfootnote=\count355
)
Package hyperref Info: Driver (autodetected): hpdftex.

(/usr/share/texmf-dist/tex/latex/hyperref/hpdftex.def
File: hpdftex.def 2024-01-20 v7.01h Hyperref driver for pdfTeX

(/usr/share/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count356
\c@bookmark@seq@number=\count357

(/usr/share/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(/usr/share/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
)
\Hy@SectionHShift=\skip76
)
(/usr/share/texmf-dist/tex/latex/ragged2e/ragged2e.sty
Package: ragged2e 2023/06/22 v3.6 ragged2e Package
\CenteringLeftskip=\skip77
\RaggedLeftLeftskip=\skip78
\RaggedRightLeftskip=\skip79
\CenteringRightskip=\skip80
\RaggedLeftRightskip=\skip81
\RaggedRightRightskip=\skip82
\CenteringParfillskip=\skip83
\RaggedLeftParfillskip=\skip84
\RaggedRightParfillskip=\skip85
\JustifyingParfillskip=\skip86
\CenteringParindent=\skip87
\RaggedLeftParindent=\skip88
\RaggedRightParindent=\skip89
\JustifyingParindent=\skip90
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.code.tex
File: tikzlibraryshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.geometric.code.tex
File: tikzlibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.geomet
ric.code.tex
File: pgflibraryshapes.geometric.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.symbols.code.tex
File: tikzlibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.symbol
s.code.tex
File: pgflibraryshapes.symbols.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.arrows.code.tex
File: tikzlibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.arrows
.code.tex
File: pgflibraryshapes.arrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.callouts.code.tex
(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.callou
ts.code.tex))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
shapes.multipart.code.tex
File: tikzlibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)

(/usr/share/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshapes.multip
art.code.tex
File: pgflibraryshapes.multipart.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodepartlowerbox=\box127
\pgfnodeparttwobox=\box128
\pgfnodepartthreebox=\box129
\pgfnodepartfourbox=\box130
\pgfnodeparttwentybox=\box131
\pgfnodepartnineteenbox=\box132
\pgfnodeparteighteenbox=\box133
\pgfnodepartseventeenbox=\box134
\pgfnodepartsixteenbox=\box135
\pgfnodepartfifteenbox=\box136
\pgfnodepartfourteenbox=\box137
\pgfnodepartthirteenbox=\box138
\pgfnodeparttwelvebox=\box139
\pgfnodepartelevenbox=\box140
\pgfnodeparttenbox=\box141
\pgfnodepartninebox=\box142
\pgfnodeparteightbox=\box143
\pgfnodepartsevenbox=\box144
\pgfnodepartsixbox=\box145
\pgfnodepartfivebox=\box146
)))
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
chains.code.tex
File: tikzlibrarychains.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
matrix.code.tex
File: tikzlibrarymatrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
trees.code.tex
File: tikzlibrarytrees.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tikzlibrary
fit.code.tex
File: tikzlibraryfit.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(/usr/share/texmf-dist/tex/generic/pgfplots/libs/tikzlibrarypgfplots.patchplots
.code.tex) (/usr/share/texmf-dist/tex/latex/psnfss/helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
)
(/usr/share/texmf-dist/tex/latex/sfmath/sfmath.sty
Package: sfmath 2007/08/27 v0.8 sans serif maths
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/phv/m/n on input line 329.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/phv/bx/n on input line 330.
\symSFMath=\mathgroup8
LaTeX Font Info:    Overwriting symbol font `SFMath' in version `normal'
(Font)                  OT1/phv/m/sl --> OT1/phv/m/sl on input line 343.
LaTeX Font Info:    Overwriting symbol font `SFMath' in version `bold'
(Font)                  OT1/phv/m/sl --> OT1/phv/bx/sl on input line 344.
LaTeX Font Info:    Redeclaring math symbol \imath on input line 398.
LaTeX Font Info:    Redeclaring math symbol \jmath on input line 399.
\symSFMathUp=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `SFMathUp' in version `normal'
(Font)                  OT1/phv/m/n --> OT1/phv/m/n on input line 406.
LaTeX Font Info:    Overwriting symbol font `SFMathUp' in version `bold'
(Font)                  OT1/phv/m/n --> OT1/phv/bx/n on input line 407.
LaTeX Font Info:    Redeclaring math symbol \ldotp on input line 410.
\symSFMathGreek=\mathgroup10
LaTeX Font Info:    Overwriting symbol font `SFMathGreek' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/cmss/m/n on input line 419.
LaTeX Font Info:    Overwriting symbol font `SFMathGreek' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/bx/n on input line 420.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 422.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 423.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 424.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 425.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 426.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 427.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 428.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 429.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 430.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 431.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 432.
\symSFMathUpGreek=\mathgroup11
LaTeX Font Info:    Overwriting symbol font `SFMathUpGreek' in version `normal'

(Font)                  OT1/cmss/m/n --> OT1/cmss/m/n on input line 436.
LaTeX Font Info:    Overwriting symbol font `SFMathUpGreek' in version `bold'
(Font)                  OT1/cmss/m/n --> OT1/cmss/bx/n on input line 437.
LaTeX Font Info:    Overwriting math alphabet `\mathnormal' in version `normal'

(Font)                  OML/cmm/m/it --> OT1/phv/m/sl on input line 446.
LaTeX Font Info:    Overwriting math alphabet `\mathnormal' in version `bold'
(Font)                  OML/cmm/b/it --> OT1/phv/bx/sl on input line 447.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/phv/m/n --> OT1/phv/m/n on input line 449.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/phv/bx/n --> OT1/phv/bx/n on input line 450.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/phv/bx/n on input line 452.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/phv/bx/n on input line 453.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/phv/m/sl on input line 455.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/phv/bx/sl on input line 456.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/phv/m/n on input line 458.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/phv/bx/n on input line 459.
LaTeX Font Info:    Overwriting math alphabet `\mathsl' in version `bold'
(Font)                  OT1/phv/m/sl --> OT1/phv/bx/sl on input line 466.
)
(/usr/share/texmf-dist/tex/latex/tools/longtable.sty
Package: longtable 2023-11-01 v4.19 Multi-page Table package (DPC)
\LTleft=\skip91
\LTright=\skip92
\LTpre=\skip93
\LTpost=\skip94
\LTchunksize=\count358
\LTcapwidth=\dimen349
\LT@head=\box147
\LT@firsthead=\box148
\LT@foot=\box149
\LT@lastfoot=\box150
\LT@gbox=\box151
\LT@cols=\count359
\LT@rows=\count360
\c@LT@tables=\count361
\c@LT@chunks=\count362
\LT@p@ftn=\toks64
)
LaTeX Font Info:    Trying to load font information for T1+phv on input line 11
2.

(/usr/share/texmf-dist/tex/latex/psnfss/t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
) (./solpcielo.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
)
\openout1 = `solpcielo.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 112.
LaTeX Font Info:    ... okay on input line 112.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(28.45274pt, 557.67403pt, 28.45274pt)
* v-part:(T,H,B)=(42.67912pt, 839.35631pt, 56.9055pt)
* \paperwidth=614.57951pt
* \paperheight=938.94093pt
* \textwidth=557.67403pt
* \textheight=839.35631pt
* \oddsidemargin=-43.81725pt
* \evensidemargin=-43.81725pt
* \topmargin=-66.59087pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=10.0pt
* \footskip=30.0pt
* \marginparwidth=54.2025pt
* \marginparsep=11.0pt
* \columnsep=10.0pt
* \skip\footins=9.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(/usr/share/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count363
\scratchdimen=\dimen350
\scratchbox=\box152
\nofMPsegments=\count364
\nofMParguments=\count365
\everyMPshowfont=\toks65
\MPscratchCnt=\count366
\MPscratchDim=\dimen351
\MPnumerator=\count367
\makeMPintoPDFobject=\count368
\everyMPtoPDFconversion=\toks66
) (/usr/share/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(/usr/share/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Liv
e
))
Package pgfplots notification 'compat/show suggested version=true': you might b
enefit from \pgfplotsset{compat=1.18} (current compat level: 1.17).

LaTeX Info: Redefining \. on input line 112.
LaTeX Info: Redefining \% on input line 112.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
(/usr/share/texmf-dist/tex/latex/floatrow/fr-longtable.sty
Package: fr-longtable 2007/11/28 v0.1b (beta) floatrow: additions for longtable

\flrow@LT@lasthead=\box153
\flrow@LT@prelastfoot=\box154
\c@FBLTpage=\count369
)
Package hyperref Info: Link coloring OFF on input line 112.
 (./solpcielo.out)
(./solpcielo.out)
\@outlinefile=\write5
\openout5 = `solpcielo.out'.

LaTeX Font Info:    Trying to load font information for OT1+phv on input line 1
24.
 (/usr/share/texmf-dist/tex/latex/psnfss/ot1phv.fd
File: ot1phv.fd 2020/03/25 scalable font definitions for OT1/phv.
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 124
.

(/usr/share/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 124
.

(/usr/share/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+rsfs on input line 12
4.

(/usr/share/texmf-dist/tex/latex/jknapltx/ursfs.fd
File: ursfs.fd 1998/03/24 rsfs font definition file (jk)
) [1

{/var/lib/texmf/fonts/map/pdftex/updmap/pdftex.map}{/usr/share/texmf-dist/fonts
/enc/dvips/base/8r.enc}]

! LaTeX Error: Something's wrong--perhaps a missing \item.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.140 \end{enumerate}
                     
Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

(./solpcielo.aux
LaTeX Info: Redefining \. on input line 11.
LaTeX Info: Redefining \% on input line 11.
)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2020/03/25>
 ***********
Package rerunfilecheck Info: File `solpcielo.out' has not changed.
(rerunfilecheck)             Checksum: D41D8CD98F00B204E9800998ECF8427E;0.
 ) 
Here is how much of TeX's memory you used:
 42881 strings out of 476076
 991407 string characters out of 5793776
 1929187 words of memory out of 5000000
 64123 multiletter control sequences out of 15000+600000
 587994 words of font info for 119 fonts, out of 8000000 for 9000
 14 hyphenation exceptions out of 8191
 102i,9n,117p,1178b,609s stack positions out of 10000i,1000n,20000p,200000b,200000s
</usr/share/texmf-dist/fonts/type1/urw/helvetic/uhvb8a.pfb>
Output written on solpcielo.pdf (1 page, 11883 bytes).
PDF statistics:
 22 PDF objects out of 1000 (max. 8388607)
 16 compressed objects within 1 object stream
 2 named destinations out of 1000 (max. 500000)
 13 words of extra memory for PDF output out of 10000 (max. 10000000)

