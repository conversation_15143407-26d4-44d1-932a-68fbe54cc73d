---
output:
  pdf_document: default
  html_document: default
---
```{r setup, include=FALSE}
library(exams)
#library(tidyverse)

# Establecer una semilla fija para depuración

options(scipen=999)
knitr::opts_chunk$set(warning = FALSE, message = FALSE, error = TRUE)
```


```{r DefiniciónDeVariables, message=FALSE, warning=FALSE, results='asis'}
#--------------------------------------------------------------------
# DEFINICIÓN DE VARIABLES Y FUNCIONES AUXILIARES
#--------------------------------------------------------------------

# Lista ampliada de colores en español para mayor variedad
colores_es <- c("rojo", "verde", "azul", "amarillo", "naranja", "morado", "rosa", "marrón",
                "gris", "negro", "blanco", "turquesa", "magenta", "cian", "índigo", "violeta",
                "granate", "marino", "oliva", "coral", "beige", "púrpura", "esmeralda", "dorado",
                "plateado", "lavanda", "carmesí", "ocre", "jade", "ámbar", "celeste", "fucsia")

# Seleccionar 3 colores aleatorios sin reemplazo
aleacolor <- sample(colores_es, size = 3, replace = FALSE)

# Genera números aleatorios para la cantidad de bolas de cada color
# Ampliamos el rango para aumentar la variabilidad
cantidades <- sample(5:25, 3, replace = TRUE)
total_bolas <- sum(cantidades)

# Función para calcular el máximo común divisor (MCD)
gcd <- function(a, b) {
  if (b == 0)
    return(a)
  else
    return(gcd(b, a %% b))
}

# Función para generar fracciones con un nivel similar de simplificación
generar_fraccion_similar <- function(target_fraction) {
  # Extraer numerador y denominador de la fracción objetivo
  parts <- as.numeric(strsplit(target_fraction, "/")[[1]])
  target_num <- parts[1]
  target_denom <- parts[2]

  # Calcular la proporción objetivo
  target_prop <- target_num / target_denom

  # Generar una fracción similar pero no igual
  # Intentar mantener un nivel similar de simplificación
  max_intentos <- 100  # Limitar el número de intentos para evitar bucles infinitos
  for (intento in 1:max_intentos) {
    # Generar un denominador cercano al original
    new_denom <- sample(max(target_denom - 10, 15):min(target_denom + 10, 60), 1)

    # Calcular el numerador aproximado basado en la proporción
    approx_num <- round(target_prop * new_denom)

    # Ajustar ligeramente para que no sea exactamente la misma proporción
    adjustment <- sample(c(-2, -1, 1, 2), 1)
    new_num <- approx_num + adjustment

    # Asegurarse de que la fracción esté en el rango válido
    if (new_num >= 5 && new_num <= 30 && new_denom >= 15 && new_denom <= 60) {
      # Calcular MCD para verificar si la fracción es simplificable
      mcd_new <- gcd(new_num, new_denom)

      # Queremos que la fracción tenga un nivel similar de simplificación
      # Si la fracción original está simplificada, esta también debería estarlo parcialmente
      if (mcd_new > 1 && mcd_new <= 5) {
        # La fracción es simplificable pero no demasiado obvia
        return(c(new_num, new_denom))
      } else if (mcd_new == 1 && target_num / target_denom != new_num / new_denom) {
        # La fracción ya está simplificada y es diferente de la original
        return(c(new_num, new_denom))
      }
    }
  }

  # Si después de max_intentos no se encontró una fracción adecuada,
  # devolver una fracción por defecto que sea diferente a la original
  default_num <- target_num + 1
  default_denom <- target_denom + 2
  return(c(default_num, default_denom))
}

# Función mejorada para generar distractores coherentes y plausibles
# Genera combinaciones de cantidades que suman diferente al total original
# y que tienen una proporción diferente para el primer color
generate_distractors <- function(correct_value, total, n = 3, target_fraction) {
  # Calculamos la proporción correcta
  correct_mcd <- gcd(correct_value, total)
  correct_prop <- correct_value / correct_mcd

  # Matriz para almacenar los distractores
  distractores <- matrix(nrow = n, ncol = 3)

  # Generar fracciones similares para los distractores
  for (i in 1:n) {
    # Generar una fracción similar pero no igual
    similar_fraction <- generar_fraccion_similar(target_fraction)
    new_num <- similar_fraction[1]
    new_denom <- similar_fraction[2]

    # Calcular el total de bolas basado en el nuevo denominador
    alt_total <- new_denom

    # Calcular la cantidad de bolas del primer color
    distractor_color1 <- new_num

    # Distribuir el resto entre los otros dos colores
    remaining <- alt_total - distractor_color1
    if (remaining > 5) {
      # Dividir el resto entre los otros dos colores
      distractor_color2 <- sample(5:min(remaining-5, 25), 1)
      distractor_color3 <- remaining - distractor_color2

      # Verificar que los valores sean plausibles
      if (distractor_color3 >= 5 && distractor_color3 <= 25) {
        distractores[i, ] <- c(distractor_color1, distractor_color2, distractor_color3)
      } else {
        # Si no se cumple, intentar una distribución más equilibrada
        distractor_color2 <- floor(remaining / 2)
        distractor_color3 <- remaining - distractor_color2
        distractores[i, ] <- c(distractor_color1, distractor_color2, distractor_color3)
      }
    } else {
      # Si no hay suficiente para distribuir, ajustar
      distractor_color1 <- distractor_color1 - 5
      remaining <- alt_total - distractor_color1
      distractor_color2 <- ceiling(remaining / 2)
      distractor_color3 <- remaining - distractor_color2
      distractores[i, ] <- c(distractor_color1, distractor_color2, distractor_color3)
    }
  }
  return(distractores)
}

# Cálculo de la fracción simplificada para la probabilidad
numerador <- cantidades[1]
denominador <- total_bolas
mcd <- gcd(numerador, denominador)
fracc_simp <- paste(numerador / mcd, "/", denominador / mcd)

# Generamos los distractores con fracciones similares
distractors <- generate_distractors(cantidades[1], total_bolas, n = 3, target_fraction = fracc_simp)

# Calculamos las fracciones para los distractores (para mostrar en la solución)
fracciones_distractores <- vector("character", 3)
for (i in 1:3) {
  num_distractor <- distractors[i, 1]
  denom_distractor <- sum(distractors[i, ])
  mcd_distractor <- gcd(num_distractor, denom_distractor)

  # Decidir si simplificar o no la fracción del distractor
  # Para que no sea obvio cuál es la respuesta correcta
  if (runif(1) < 0.5) {
    # Mostrar la fracción sin simplificar
    fracciones_distractores[i] <- paste(num_distractor, "/", denom_distractor, sep = "")
  } else {
    # Mostrar la fracción simplificada
    fracciones_distractores[i] <- paste(num_distractor / mcd_distractor, "/",
                                       denom_distractor / mcd_distractor, sep = "")
  }
}

#--------------------------------------------------------------------
# DEFINICIÓN DE OPCIONES DE RESPUESTA
#--------------------------------------------------------------------

# Función para generar una opción con orden aleatorio de colores
generar_opcion_aleatoria <- function(cantidades, colores) {
  # Crear los tres fragmentos de texto (uno para cada color)
  fragmentos <- c(
    sprintf('%s bolas de color %s', cantidades[1], colores[1]),
    sprintf('%s de color %s', cantidades[2], colores[2]),
    sprintf('%s de color %s', cantidades[3], colores[3])
  )

  # Aleatorizar el orden de los fragmentos
  fragmentos_aleatorios <- sample(fragmentos)

  # Unir los fragmentos con comas y "y" antes del último
  return(paste(
    fragmentos_aleatorios[1],
    ", ",
    fragmentos_aleatorios[2],
    " y ",
    fragmentos_aleatorios[3],
    sep = ""
  ))
}

# Crear la opción correcta en orden estándar (para la solución)
opcion_correcta_ordenada <- sprintf('%s bolas de color %s, %s de color %s y %s de color %s',
                                   cantidades[1], aleacolor[1],
                                   cantidades[2], aleacolor[2],
                                   cantidades[3], aleacolor[3])

# Crear la opción correcta con orden aleatorio de colores (para las opciones)
opcion_correcta <- generar_opcion_aleatoria(cantidades, aleacolor)

# Crear opciones incorrectas basadas en los distractores generados, también con orden aleatorio
opciones_incorrectas <- c(
  generar_opcion_aleatoria(
    c(distractors[1,1], distractors[1,2], distractors[1,3]),
    aleacolor
  ),

  generar_opcion_aleatoria(
    c(distractors[2,1], distractors[2,2], distractors[2,3]),
    aleacolor
  ),

  generar_opcion_aleatoria(
    c(distractors[3,1], distractors[3,2], distractors[3,3]),
    aleacolor
  )
)

# Combinar todas las opciones
todas_opciones <- c(opcion_correcta, opciones_incorrectas)

# Aleatorizar el orden de las opciones
opciones_aleatorias <- sample(todas_opciones)

# Determinar la posición de la respuesta correcta
posicion_correcta <- which(opciones_aleatorias == opcion_correcta)

# Crear el string de solución (por ejemplo, "0100" si la respuesta correcta está en la segunda posición)
solucion_string <- rep(0, 4)
solucion_string[posicion_correcta] <- 1
solucion_string <- paste(solucion_string, collapse = "")

# Asignar las opciones aleatorizadas a variables para usar en el documento
opcion01 <- opciones_aleatorias[1]
opcion02 <- opciones_aleatorias[2]
opcion03 <- opciones_aleatorias[3]
opcion04 <- opciones_aleatorias[4]

# Formato para el enunciado (orden de los colores)
enunci <- sprintf('%s, %s y %s', aleacolor[1], aleacolor[2], aleacolor[3])

# Decidir si mostrar la fracción simplificada o no en el enunciado
# Para que no sea obvio cuál es la respuesta correcta
mostrar_fraccion <- if (runif(1) < 0.5 || mcd == 1) {
  # Si el MCD es 1 o aleatoriamente, mostrar la fracción simplificada
  fracc_simp
} else {
  # Mostrar la fracción sin simplificar
  paste(numerador, "/", denominador, sep = "")
}

# Cálculos adicionales para la solución
prob_color1 <- cantidades[1] / total_bolas
prob_color1_percent <- round(prob_color1 * 100, 2)
prob_color2 <- cantidades[2] / total_bolas
prob_color2_percent <- round(prob_color2 * 100, 2)
prob_color3 <- cantidades[3] / total_bolas
prob_color3_percent <- round(prob_color3 * 100, 2)
```

Question
========

De una caja que contiene bolas de color `r enunci` del mismo tamaño, se saca una bola al azar.

Si se sabe que la probabilidad de sacar una bola de color `r aleacolor[1]` es de `r mostrar_fraccion`, en la caja puede haber

Answerlist
----------

- `r opcion01`

- `r opcion02`

- `r opcion03`

- `r opcion04`


Solution
========

Para resolver este problema, debemos entender la relación entre la probabilidad y la composición de la caja.

La probabilidad de sacar una bola de color `r aleacolor[1]` es `r fracc_simp`, lo que significa que:

$P(\text{bola } `r aleacolor[1]`) = \frac{\text{número de bolas } `r aleacolor[1]`}{\text{número total de bolas}} = \frac{`r numerador`}{`r denominador`} = `r fracc_simp`$

Por lo tanto, en la caja debe haber:

- `r cantidades[1]` bolas de color `r aleacolor[1]` (que representan el `r prob_color1_percent`% del total)
- `r cantidades[2]` bolas de color `r aleacolor[2]` (que representan el `r prob_color2_percent`% del total)
- `r cantidades[3]` bolas de color `r aleacolor[3]` (que representan el `r prob_color3_percent`% del total)

Lo que suma un total de `r total_bolas` bolas.

Verifiquemos que esta composición cumple con la probabilidad dada:
$\frac{`r cantidades[1]`}{`r total_bolas`} = \frac{`r numerador`}{`r denominador`} = `r fracc_simp`$

Las otras opciones no cumplen con la probabilidad requerida:

- Si hubiera `r distractors[1,1]` bolas de color `r aleacolor[1]` de un total de `r sum(distractors[1,])` bolas, la probabilidad sería $\frac{`r distractors[1,1]`}{`r sum(distractors[1,])`} = `r fracciones_distractores[1]` \neq `r fracc_simp`$

- Si hubiera `r distractors[2,1]` bolas de color `r aleacolor[1]` de un total de `r sum(distractors[2,])` bolas, la probabilidad sería $\frac{`r distractors[2,1]`}{`r sum(distractors[2,])`} = `r fracciones_distractores[2]` \neq `r fracc_simp`$

- Si hubiera `r distractors[3,1]` bolas de color `r aleacolor[1]` de un total de `r sum(distractors[3,])` bolas, la probabilidad sería $\frac{`r distractors[3,1]`}{`r sum(distractors[3,])`} = `r fracciones_distractores[3]` \neq `r fracc_simp`$



Por lo tanto, la respuesta correcta es: `r opcion_correcta_ordenada`

Meta-information
================
exname: 11_C01_G09_2020_Tipo1_Probabilidades_N3(single-choice)
extype: schoice
exsolution: `r solucion_string`
exshuffle: FALSE
