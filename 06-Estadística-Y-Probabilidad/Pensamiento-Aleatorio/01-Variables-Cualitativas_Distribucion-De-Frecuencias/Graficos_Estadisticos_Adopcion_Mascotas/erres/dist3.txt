```{r data generation, echo = FALSE, results = "hide"}
sc <- NULL
while(is.null(sc)) {
p <- c(sample(1:3, 1), sample(1:5, 1))
q <- c(sample(4:5, 1), sample((1:5)[-p[2]], 1))
sol <- sqrt(sum((p - q)^2))

err <- c(sqrt(sum((p + q)^2)), sqrt(sum(abs(p - q))))
err <- err[abs(err - sol) > 0.1]
if(length(err) > 1) err <- sample(err, 1)

sc <- num_to_schoice(sol, wrong = err, range = c(0.1, 10), delta = 0.3, digits = 3)
}
```

Question
========
What is the distance between the two points
$p = (`r p[1]`, `r p[2]`)$ and $q = (`r q[1]`, `r q[2]`)$
in a Cartesian coordinate system?

```{r questionlist, echo = FALSE, results = "asis"}
answerlist(sc$questions, markup = "markdown")
```

Solution
========
The distance $d$ of $p$ and $q$ is given by
$d^2 = (p_1 - q_1)^2 + (p_2 - q_2)^2$ (Pythagorean formula).

Hence $d = \sqrt{(p_1 - q_1)^2 + (p_2 - q_2)^2} =
  \sqrt{(`r p[1]` - `r q[1]`)^2 + (`r p[2]` - `r q[2]`)^2}
   = `r round(sol, digits = 3)`$.
\
```{r distplot, echo = FALSE, results = "hide", fig.path = "", fig.cap = ""}
par(mar = c(4, 4, 1, 1))
plot(0, type = "n", xlim = c(0, 6), ylim = c(0, 6), xlab = "x", ylab = "y")
grid(col = "slategray")
points(rbind(p, q), pch = 19)
text(rbind(p, q), c("p", "q"), pos = c(2, 4))
lines(rbind(p, q))
lines(c(p[1], p[1], q[1]), c(p[2], q[2], q[2]), lty = 2)
```

```{r solutionlist, echo = FALSE, results = "asis"}
answerlist(ifelse(sc$solutions, "True", "False"), markup = "markdown")
```

Meta-information
================
extype: schoice
exsolution: `r mchoice2string(sc$solutions)`
exname: Euclidean distance
