```{r data generation, echo = FALSE, results = "hide"}
## regression parameters
n <- sample(40:90, 1)
b <- sample(c(-1, 1), 1) * runif(1, 1, 2) * sample(c(0.1, 0.5, 1), 1)
s <- sample(c(0.5, 1, 2), 1)

## data and regression
x2_ok <- FALSE
while(!x2_ok) {

d <- data.frame(
  x1 = rnorm(n),
  x2 = rnorm(n),
  err = rnorm(n, sd = s)
)
d$y <- 0 + b * d$x1 + d$err

x2_ok <- summary(lm(y ~ x1 + x2, data = d))$coefficients[3, 4] > 0.1
}

## different types
type <- sample(c("linear", "semi-logarithmic", "log-log"), 1)
if(type == "linear") {
  m <- lm(y ~ x1 + x2, data = d)
  xunit <- "unit" 
  yunit <- "units"
  eff <- round(coef(m)[2], digits = 2)
} else if(type == "semi-logarithmic") {
  d$y <- exp(d$y)
  m <- lm(log(y) ~ x1 + x2, data = d)
  xunit <- "unit" 
  yunit <- "percent"
  eff <- round(100 * exp(coef(m)[2]) - 100, digits = 2)
} else if(type == "log-log") {
  d$y <- exp(d$y)
  d$x1 <- exp(d$x1)
  d$x2 <- exp(d$x2)
  m <- lm(log(y) ~ log(x1) + log(x2), data = d)
  xunit <- "percent" 
  yunit <- "percent"
  eff <- round(100 * exp(0.01 * coef(m)[2]) - 100, digits = 2)
}

## summaries
direct <- if(coef(m)[2] > 0) "increases" else "decreases"
if(summary(m)$coefficients[2, 4] < 0.05) {
  sign1 <- "Also"
  sign2 <- ""
} else {
  sign1 <- "However"
  sign2 <- "_not_"
}

rsq <- summary(m)$r.squared
fstat <- summary(m)$fstatistic["value"]

write.csv(d, "regression.csv", row.names = FALSE, quote = FALSE)
```

Question
========
Using the data provided in [regression.csv](regression.csv) estimate a linear regression of
`y` on `x1` and `x2`. Answer the following questions.

Answerlist
----------
* Proportion of variance explained (in percent):
* F-statistic:
* Characterize in your own words how the response `y` depends on the regressors `x1` and `x2`.
* Upload the R script you used to analyze the data.

Solution
========
The presented results describe a `r type` regression.

```{r, echo = FALSE, comment = ""}
summary(m)
```

The mean of the response `y` `r direct` with increasing `x1`.
If `x1` increases by 1 `r xunit` then a change of `y` by about `r eff` `r yunit` can be expected.
`r sign1`, the effect of `x1` is `r sign2` significant at the 5 percent level.

Variable `x2` has no significant influence on the response at 5 percent level.

The R-squared is `r signif(rsq, 4)` and thus `r fmt(100 * rsq, 2)` percent of the
variance of the response is explained by the regression.

The F-statistic is `r fmt(fstat, 2)`.

Answerlist
----------
* Proportion of variance explained: `r fmt(100 * rsq, 2)` percent.
* F-statistic: `r fmt(fstat, 2)`.
* Characterization: `r type`.
* R code.

Meta-information
================
exname: Regression cloze essay
extype: cloze
exsolution: `r fmt(100 * rsq, 2)`|`r fmt(fstat, 2)`|nil|nil
exclozetype: num|num|essay|file
extol: 0.1
