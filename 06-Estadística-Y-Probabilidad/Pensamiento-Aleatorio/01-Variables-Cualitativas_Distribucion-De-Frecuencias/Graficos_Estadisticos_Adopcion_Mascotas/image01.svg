<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="202" height="64" viewBox="0 0 202 64">
<defs>
<g>
<g id="glyph-0-0">
<path d="M 5.984375 -1.75 L 6.5625 0 L 8.40625 0 L 5.390625 -8.71875 L 3.40625 -8.71875 L 0.3125 0 L 2.140625 0 L 2.71875 -1.75 Z M 5.5 -3.25 L 3.234375 -3.25 L 4.359375 -6.65625 Z M 5.5 -3.25 "/>
</g>
<g id="glyph-0-1">
<path d="M 0.75 -6.453125 L 0.75 0 L 2.421875 0 L 2.421875 -3.875 C 2.421875 -4.640625 2.96875 -5.140625 3.78125 -5.140625 C 4.5 -5.140625 4.859375 -4.75 4.859375 -3.984375 L 4.859375 0 L 6.53125 0 L 6.53125 -4.328125 C 6.53125 -5.75 5.734375 -6.5625 4.359375 -6.5625 C 3.484375 -6.5625 2.90625 -6.234375 2.421875 -5.515625 L 2.421875 -6.453125 Z M 0.75 -6.453125 "/>
</g>
<g id="glyph-0-2">
<path d="M 2.46875 -6.453125 L 0.796875 -6.453125 L 0.796875 0 L 2.46875 0 Z M 2.46875 -8.71875 L 0.796875 -8.71875 L 0.796875 -7.21875 L 2.46875 -7.21875 Z M 2.46875 -8.71875 "/>
</g>
<g id="glyph-0-3">
<path d="M 0.71875 -6.453125 L 0.71875 0 L 2.390625 0 L 2.390625 -3.875 C 2.390625 -4.65625 2.828125 -5.140625 3.53125 -5.140625 C 4.09375 -5.140625 4.453125 -4.8125 4.453125 -4.296875 L 4.453125 0 L 6.125 0 L 6.125 -3.875 C 6.125 -4.65625 6.546875 -5.140625 7.265625 -5.140625 C 7.828125 -5.140625 8.171875 -4.8125 8.171875 -4.296875 L 8.171875 0 L 9.84375 0 L 9.84375 -4.5625 C 9.84375 -5.828125 9.078125 -6.5625 7.796875 -6.5625 C 6.984375 -6.5625 6.40625 -6.28125 5.90625 -5.609375 C 5.59375 -6.21875 4.953125 -6.5625 4.140625 -6.5625 C 3.40625 -6.5625 2.921875 -6.3125 2.375 -5.65625 L 2.375 -6.453125 Z M 0.71875 -6.453125 "/>
</g>
<g id="glyph-0-4">
<path d="M 6.265625 -0.203125 C 5.96875 -0.484375 5.875 -0.65625 5.875 -0.984375 L 5.875 -4.578125 C 5.875 -5.890625 4.984375 -6.5625 3.234375 -6.5625 C 1.5 -6.5625 0.578125 -5.828125 0.484375 -4.328125 L 2.09375 -4.328125 C 2.171875 -5 2.453125 -5.21875 3.28125 -5.21875 C 3.921875 -5.21875 4.25 -5 4.25 -4.5625 C 4.25 -4.34375 4.140625 -4.15625 3.953125 -4.0625 C 3.734375 -3.9375 3.734375 -3.9375 2.90625 -3.8125 L 2.234375 -3.6875 C 0.953125 -3.484375 0.328125 -2.828125 0.328125 -1.65625 C 0.328125 -1.109375 0.484375 -0.65625 0.78125 -0.328125 C 1.15625 0.046875 1.71875 0.28125 2.296875 0.28125 C 3.015625 0.28125 3.671875 -0.03125 4.25 -0.640625 C 4.25 -0.3125 4.296875 -0.1875 4.453125 0 L 6.265625 0 Z M 4.25 -2.59375 C 4.25 -1.625 3.765625 -1.078125 2.921875 -1.078125 C 2.359375 -1.078125 2.015625 -1.375 2.015625 -1.859375 C 2.015625 -2.359375 2.265625 -2.59375 2.96875 -2.734375 L 3.53125 -2.84375 C 3.984375 -2.921875 4.046875 -2.953125 4.25 -3.046875 Z M 4.25 -2.59375 "/>
</g>
<g id="glyph-0-5">
<path d="M 2.46875 -8.71875 L 0.796875 -8.71875 L 0.796875 0 L 2.46875 0 Z M 2.46875 -8.71875 "/>
</g>
<g id="glyph-0-6">
<path d="M 2.703125 -3.109375 L 4.9375 -3.109375 C 6.53125 -3.109375 7.5625 -4.25 7.5625 -6.015625 C 7.5625 -7.75 6.578125 -8.71875 4.75 -8.71875 L 0.90625 -8.71875 L 0.90625 0 L 2.703125 0 Z M 2.703125 -4.609375 L 2.703125 -7.21875 L 4.375 -7.21875 C 5.328125 -7.21875 5.765625 -6.796875 5.765625 -5.90625 C 5.765625 -5.015625 5.328125 -4.609375 4.375 -4.609375 Z M 2.703125 -4.609375 "/>
</g>
<g id="glyph-0-7">
<path d="M 3.59375 -6.5625 C 1.625 -6.5625 0.421875 -5.265625 0.421875 -3.140625 C 0.421875 -1.015625 1.625 0.28125 3.609375 0.28125 C 5.5625 0.28125 6.796875 -1.03125 6.796875 -3.09375 C 6.796875 -5.28125 5.625 -6.5625 3.59375 -6.5625 Z M 3.609375 -5.21875 C 4.53125 -5.21875 5.125 -4.390625 5.125 -3.125 C 5.125 -1.90625 4.5 -1.078125 3.609375 -1.078125 C 2.703125 -1.078125 2.09375 -1.90625 2.09375 -3.140625 C 2.09375 -4.375 2.703125 -5.21875 3.609375 -5.21875 Z M 3.609375 -5.21875 "/>
</g>
<g id="glyph-0-8">
<path d="M 0.75 -6.453125 L 0.75 0 L 2.421875 0 L 2.421875 -3.4375 C 2.421875 -4.40625 2.921875 -4.90625 3.890625 -4.90625 C 4.078125 -4.90625 4.203125 -4.890625 4.421875 -4.859375 L 4.421875 -6.546875 C 4.328125 -6.5625 4.265625 -6.5625 4.21875 -6.5625 C 3.453125 -6.5625 2.78125 -6.0625 2.421875 -5.1875 L 2.421875 -6.453125 Z M 0.75 -6.453125 "/>
</g>
<g id="glyph-0-9">
<path d="M 6.234375 -4.046875 C 6.125 -5.625 5.0625 -6.5625 3.453125 -6.5625 C 1.53125 -6.5625 0.40625 -5.296875 0.40625 -3.09375 C 0.40625 -0.96875 1.515625 0.28125 3.4375 0.28125 C 5 0.28125 6.078125 -0.6875 6.234375 -2.234375 L 4.640625 -2.234375 C 4.421875 -1.40625 4.09375 -1.078125 3.4375 -1.078125 C 2.59375 -1.078125 2.078125 -1.84375 2.078125 -3.09375 C 2.078125 -3.703125 2.203125 -4.28125 2.40625 -4.65625 C 2.59375 -5.015625 2.96875 -5.21875 3.4375 -5.21875 C 4.125 -5.21875 4.453125 -4.890625 4.640625 -4.046875 Z M 6.234375 -4.046875 "/>
</g>
<g id="glyph-0-10">
<path d="M 6.265625 -2.703125 C 6.28125 -2.84375 6.28125 -2.90625 6.28125 -2.984375 C 6.28125 -3.640625 6.1875 -4.234375 6.03125 -4.6875 C 5.59375 -5.859375 4.546875 -6.5625 3.25 -6.5625 C 1.40625 -6.5625 0.265625 -5.21875 0.265625 -3.0625 C 0.265625 -0.984375 1.390625 0.28125 3.21875 0.28125 C 4.65625 0.28125 5.828125 -0.546875 6.203125 -1.8125 L 4.546875 -1.8125 C 4.34375 -1.296875 3.890625 -1 3.28125 -1 C 2.796875 -1 2.421875 -1.203125 2.171875 -1.5625 C 2.015625 -1.8125 1.953125 -2.09375 1.9375 -2.703125 Z M 1.953125 -3.8125 C 2.0625 -4.796875 2.484375 -5.28125 3.234375 -5.28125 C 3.640625 -5.28125 4.03125 -5.078125 4.265625 -4.75 C 4.421875 -4.5 4.5 -4.25 4.53125 -3.8125 Z M 1.953125 -3.8125 "/>
</g>
<g id="glyph-0-11">
<path d="M 3.59375 -6.328125 L 2.671875 -6.328125 L 2.671875 -8.0625 L 0.984375 -8.0625 L 0.984375 -6.328125 L 0.171875 -6.328125 L 0.171875 -5.21875 L 0.984375 -5.21875 L 0.984375 -1.25 C 0.984375 -0.234375 1.53125 0.28125 2.625 0.28125 C 3 0.28125 3.296875 0.234375 3.59375 0.140625 L 3.59375 -1.03125 C 3.4375 -1 3.328125 -0.984375 3.21875 -0.984375 C 2.765625 -0.984375 2.671875 -1.125 2.671875 -1.703125 L 2.671875 -5.21875 L 3.59375 -5.21875 Z M 3.59375 -6.328125 "/>
</g>
<g id="glyph-0-12">
<path d="M 2.515625 -6.453125 L 0.84375 -6.453125 L 0.84375 0.75 C 0.84375 1.125 0.703125 1.25 0.34375 1.25 C 0.234375 1.25 0.171875 1.25 0.046875 1.203125 L 0.046875 2.546875 C 0.375 2.578125 0.796875 2.609375 1.046875 2.609375 C 2.078125 2.609375 2.515625 2.171875 2.515625 1.125 Z M 2.515625 -8.71875 L 0.84375 -8.71875 L 0.84375 -7.21875 L 2.515625 -7.21875 Z M 2.515625 -8.71875 "/>
</g>
<g id="glyph-0-13">
<path d="M 4.84375 0 L 6.515625 0 L 6.515625 -8.71875 L 4.84375 -8.71875 L 4.84375 -5.625 C 4.421875 -6.265625 3.859375 -6.5625 3.0625 -6.5625 C 1.515625 -6.5625 0.34375 -5.078125 0.34375 -3.125 C 0.34375 -2.265625 0.609375 -1.375 1.046875 -0.75 C 1.484375 -0.125 2.265625 0.28125 3.0625 0.28125 C 3.859375 0.28125 4.421875 -0.03125 4.84375 -0.65625 Z M 3.4375 -5.15625 C 4.28125 -5.15625 4.84375 -4.34375 4.84375 -3.109375 C 4.84375 -1.9375 4.265625 -1.125 3.4375 -1.125 C 2.59375 -1.125 2.015625 -1.953125 2.015625 -3.125 C 2.015625 -4.328125 2.59375 -5.15625 3.4375 -5.15625 Z M 3.4375 -5.15625 "/>
</g>
<g id="glyph-0-14">
<path d="M 2.359375 -6.453125 L 0.6875 -6.453125 L 0.6875 2.609375 L 2.359375 2.609375 L 2.359375 -0.765625 C 2.765625 -0.046875 3.34375 0.28125 4.15625 0.28125 C 5.71875 0.28125 6.859375 -1.171875 6.859375 -3.125 C 6.859375 -4.046875 6.59375 -4.953125 6.171875 -5.546875 C 5.734375 -6.15625 4.9375 -6.5625 4.15625 -6.5625 C 3.34375 -6.5625 2.765625 -6.21875 2.359375 -5.5 Z M 3.78125 -5.15625 C 4.625 -5.15625 5.1875 -4.34375 5.1875 -3.109375 C 5.1875 -1.9375 4.609375 -1.109375 3.78125 -1.109375 C 2.9375 -1.109375 2.359375 -1.921875 2.359375 -3.125 C 2.359375 -4.34375 2.9375 -5.15625 3.78125 -5.15625 Z M 3.78125 -5.15625 "/>
</g>
<g id="glyph-0-15">
<path d="M 6.03125 -4.375 C 6.015625 -5.734375 4.953125 -6.5625 3.234375 -6.5625 C 1.59375 -6.5625 0.578125 -5.734375 0.578125 -4.40625 C 0.578125 -3.984375 0.703125 -3.609375 0.9375 -3.359375 C 1.15625 -3.125 1.359375 -3.03125 1.984375 -2.828125 L 3.984375 -2.203125 C 4.40625 -2.0625 4.546875 -1.9375 4.546875 -1.671875 C 4.546875 -1.28125 4.078125 -1.046875 3.296875 -1.046875 C 2.875 -1.046875 2.515625 -1.125 2.3125 -1.265625 C 2.125 -1.40625 2.0625 -1.53125 1.984375 -1.875 L 0.34375 -1.875 C 0.390625 -0.46875 1.4375 0.28125 3.390625 0.28125 C 4.296875 0.28125 4.96875 0.078125 5.453125 -0.296875 C 5.921875 -0.6875 6.21875 -1.28125 6.21875 -1.90625 C 6.21875 -2.75 5.796875 -3.28125 4.953125 -3.53125 L 2.828125 -4.140625 C 2.359375 -4.296875 2.25 -4.390625 2.25 -4.65625 C 2.25 -5.015625 2.625 -5.25 3.21875 -5.25 C 4.015625 -5.25 4.40625 -4.953125 4.421875 -4.375 Z M 6.03125 -4.375 "/>
</g>
<g id="glyph-1-0">
<path d="M 1.8125 -8.71875 L 0.8125 -8.71875 L 0.8125 0 L 1.8125 0 Z M 1.8125 -8.71875 "/>
</g>
<g id="glyph-1-1">
<path d="M 6.390625 -0.578125 C 6.28125 -0.5625 6.234375 -0.5625 6.1875 -0.5625 C 5.828125 -0.5625 5.640625 -0.734375 5.640625 -1.046875 L 5.640625 -4.734375 C 5.640625 -5.84375 4.828125 -6.4375 3.28125 -6.4375 C 2.375 -6.4375 1.625 -6.1875 1.203125 -5.71875 C 0.921875 -5.390625 0.796875 -5.03125 0.78125 -4.40625 L 1.78125 -4.40625 C 1.859375 -5.171875 2.3125 -5.515625 3.25 -5.515625 C 4.140625 -5.515625 4.65625 -5.1875 4.65625 -4.59375 L 4.65625 -4.328125 C 4.65625 -3.90625 4.40625 -3.734375 3.609375 -3.640625 C 2.203125 -3.453125 1.984375 -3.40625 1.609375 -3.25 C 0.875 -2.953125 0.5 -2.390625 0.5 -1.578125 C 0.5 -0.4375 1.296875 0.28125 2.5625 0.28125 C 3.34375 0.28125 3.984375 0 4.6875 -0.640625 C 4.75 -0.015625 5.0625 0.28125 5.71875 0.28125 C 5.921875 0.28125 6.078125 0.25 6.390625 0.171875 Z M 4.65625 -1.96875 C 4.65625 -1.640625 4.546875 -1.4375 4.25 -1.15625 C 3.84375 -0.78125 3.359375 -0.59375 2.765625 -0.59375 C 2 -0.59375 1.546875 -0.96875 1.546875 -1.609375 C 1.546875 -2.265625 1.984375 -2.59375 3.046875 -2.75 C 4.09375 -2.890625 4.3125 -2.9375 4.65625 -3.09375 Z M 4.65625 -1.96875 "/>
</g>
<g id="glyph-1-2">
<path d="M 4.921875 -6.265625 L 4.921875 -5.359375 C 4.421875 -6.09375 3.8125 -6.4375 3.015625 -6.4375 C 1.421875 -6.4375 0.34375 -5.0625 0.34375 -3.03125 C 0.34375 -2 0.625 -1.15625 1.140625 -0.5625 C 1.609375 -0.03125 2.265625 0.28125 2.921875 0.28125 C 3.71875 0.28125 4.265625 -0.0625 4.828125 -0.84375 L 4.828125 -0.53125 C 4.828125 0.328125 4.71875 0.84375 4.46875 1.1875 C 4.203125 1.546875 3.6875 1.765625 3.078125 1.765625 C 2.625 1.765625 2.21875 1.65625 1.953125 1.4375 C 1.71875 1.25 1.625 1.09375 1.5625 0.71875 L 0.546875 0.71875 C 0.65625 1.90625 1.578125 2.609375 3.046875 2.609375 C 3.984375 2.609375 4.78125 2.3125 5.1875 1.8125 C 5.671875 1.234375 5.84375 0.4375 5.84375 -1.03125 L 5.84375 -6.265625 Z M 3.125 -5.515625 C 4.203125 -5.515625 4.828125 -4.609375 4.828125 -3.046875 C 4.828125 -1.546875 4.1875 -0.640625 3.125 -0.640625 C 2.046875 -0.640625 1.390625 -1.5625 1.390625 -3.078125 C 1.390625 -4.59375 2.0625 -5.515625 3.125 -5.515625 Z M 3.125 -5.515625 "/>
</g>
<g id="glyph-1-3">
<path d="M 0.828125 -6.265625 L 0.828125 0 L 1.828125 0 L 1.828125 -3.25 C 1.828125 -4.140625 2.0625 -4.734375 2.53125 -5.078125 C 2.84375 -5.3125 3.140625 -5.375 3.84375 -5.390625 L 3.84375 -6.40625 C 3.671875 -6.4375 3.578125 -6.4375 3.453125 -6.4375 C 2.8125 -6.4375 2.3125 -6.0625 1.75 -5.125 L 1.75 -6.265625 Z M 0.828125 -6.265625 "/>
</g>
<g id="glyph-1-4">
<path d="M 3.03125 -6.265625 L 2.015625 -6.265625 L 2.015625 -7.984375 L 1.015625 -7.984375 L 1.015625 -6.265625 L 0.171875 -6.265625 L 0.171875 -5.453125 L 1.015625 -5.453125 L 1.015625 -0.71875 C 1.015625 -0.078125 1.453125 0.28125 2.21875 0.28125 C 2.46875 0.28125 2.703125 0.25 3.03125 0.1875 L 3.03125 -0.640625 C 2.90625 -0.609375 2.75 -0.59375 2.5625 -0.59375 C 2.125 -0.59375 2.015625 -0.71875 2.015625 -1.15625 L 2.015625 -5.453125 L 3.03125 -5.453125 Z M 3.03125 -6.265625 "/>
</g>
<g id="glyph-1-5">
<path d="M 3.25 -6.4375 C 1.5 -6.4375 0.4375 -5.1875 0.4375 -3.078125 C 0.4375 -0.984375 1.484375 0.28125 3.265625 0.28125 C 5.015625 0.28125 6.09375 -0.984375 6.09375 -3.03125 C 6.09375 -5.203125 5.0625 -6.4375 3.25 -6.4375 Z M 3.265625 -5.515625 C 4.390625 -5.515625 5.0625 -4.609375 5.0625 -3.046875 C 5.0625 -1.578125 4.359375 -0.640625 3.265625 -0.640625 C 2.15625 -0.640625 1.46875 -1.5625 1.46875 -3.078125 C 1.46875 -4.59375 2.15625 -5.515625 3.265625 -5.515625 Z M 3.265625 -5.515625 "/>
</g>
<g id="glyph-1-6">
<path d="M 5.6875 -8.46875 L 1.3125 -8.46875 L 0.6875 -3.859375 L 1.65625 -3.859375 C 2.140625 -4.453125 2.546875 -4.65625 3.203125 -4.65625 C 4.34375 -4.65625 5.0625 -3.875 5.0625 -2.625 C 5.0625 -1.40625 4.34375 -0.65625 3.203125 -0.65625 C 2.28125 -0.65625 1.71875 -1.125 1.46875 -2.078125 L 0.421875 -2.078125 C 0.5625 -1.390625 0.6875 -1.046875 0.9375 -0.734375 C 1.40625 -0.09375 2.265625 0.28125 3.234375 0.28125 C 4.9375 0.28125 6.125 -0.96875 6.125 -2.765625 C 6.125 -4.4375 5.015625 -5.578125 3.390625 -5.578125 C 2.796875 -5.578125 2.3125 -5.421875 1.828125 -5.0625 L 2.15625 -7.4375 L 5.6875 -7.4375 Z M 5.6875 -8.46875 "/>
</g>
<g id="glyph-1-7">
<path d="M 3.09375 -6.03125 L 3.09375 0 L 4.140625 0 L 4.140625 -8.46875 L 3.453125 -8.46875 C 3.078125 -7.171875 2.84375 -7 1.21875 -6.796875 L 1.21875 -6.03125 Z M 3.09375 -6.03125 "/>
</g>
<g id="glyph-1-8">
<path d="M 3.28125 -8.46875 C 2.5 -8.46875 1.78125 -8.125 1.34375 -7.546875 C 0.78125 -6.796875 0.515625 -5.671875 0.515625 -4.09375 C 0.515625 -1.25 1.46875 0.28125 3.28125 0.28125 C 5.078125 0.28125 6.0625 -1.25 6.0625 -4.03125 C 6.0625 -5.671875 5.796875 -6.78125 5.234375 -7.546875 C 4.796875 -8.140625 4.09375 -8.46875 3.28125 -8.46875 Z M 3.28125 -7.546875 C 4.421875 -7.546875 4.984375 -6.390625 4.984375 -4.125 C 4.984375 -1.71875 4.4375 -0.59375 3.265625 -0.59375 C 2.15625 -0.59375 1.59375 -1.765625 1.59375 -4.09375 C 1.59375 -6.40625 2.15625 -7.546875 3.28125 -7.546875 Z M 3.28125 -7.546875 "/>
</g>
<g id="glyph-1-9">
<path d="M 6.046875 -1.046875 L 1.59375 -1.046875 C 1.703125 -1.734375 2.078125 -2.171875 3.125 -2.78125 L 4.3125 -3.4375 C 5.5 -4.078125 6.109375 -4.953125 6.109375 -5.984375 C 6.109375 -6.6875 5.828125 -7.34375 5.3125 -7.8125 C 4.8125 -8.265625 4.203125 -8.46875 3.390625 -8.46875 C 2.3125 -8.46875 1.515625 -8.09375 1.046875 -7.375 C 0.75 -6.921875 0.625 -6.390625 0.59375 -5.53125 L 1.65625 -5.53125 C 1.6875 -6.109375 1.75 -6.453125 1.90625 -6.734375 C 2.171875 -7.25 2.71875 -7.546875 3.359375 -7.546875 C 4.3125 -7.546875 5.03125 -6.875 5.03125 -5.96875 C 5.03125 -5.296875 4.640625 -4.71875 3.890625 -4.296875 L 2.78125 -3.671875 C 1.015625 -2.671875 0.5 -1.859375 0.40625 0 L 6.046875 0 Z M 6.046875 -1.046875 "/>
</g>
<g id="glyph-1-10">
<path d="M 5.765625 0 L 5.765625 -6.265625 L 4.765625 -6.265625 L 4.765625 -2.71875 C 4.765625 -1.4375 4.09375 -0.59375 3.0625 -0.59375 C 2.265625 -0.59375 1.765625 -1.078125 1.765625 -1.828125 L 1.765625 -6.265625 L 0.78125 -6.265625 L 0.78125 -1.4375 C 0.78125 -0.390625 1.546875 0.28125 2.765625 0.28125 C 3.6875 0.28125 4.28125 -0.046875 4.859375 -0.875 L 4.859375 0 Z M 5.765625 0 "/>
</g>
<g id="glyph-1-11">
<path d="M 6.21875 -8.46875 L 0.546875 -8.46875 L 0.546875 -7.4375 L 5.125 -7.4375 C 3.109375 -4.546875 2.28125 -2.78125 1.65625 0 L 2.765625 0 C 3.234375 -2.71875 4.296875 -5.046875 6.21875 -7.59375 Z M 6.21875 -8.46875 "/>
</g>
</g>
</defs>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.00003125 L 201.073531 -0.00003125 " transform="matrix(1, 0, 0, -1, 0.321, 0.957)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.00084375 L -0.0006875 12.30775 " transform="matrix(1, 0, 0, -1, 0.321, 13.464)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-0" x="6.298" y="9.877"/>
<use xlink:href="#glyph-0-1" x="14.929654" y="9.877"/>
<use xlink:href="#glyph-0-2" x="22.234282" y="9.877"/>
<use xlink:href="#glyph-0-3" x="25.557827" y="9.877"/>
<use xlink:href="#glyph-0-4" x="36.186" y="9.877"/>
<use xlink:href="#glyph-0-5" x="42.833091" y="9.877"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0011875 -0.00084375 L -0.0011875 12.30775 " transform="matrix(1, 0, 0, -1, 52.134, 13.464)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-6" x="58.111" y="9.877"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-7" x="65.60691" y="9.877"/>
<use xlink:href="#glyph-0-8" x="72.911538" y="9.877"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-9" x="77.323006" y="9.877"/>
<use xlink:href="#glyph-0-10" x="83.970098" y="9.877"/>
<use xlink:href="#glyph-0-1" x="90.617189" y="9.877"/>
<use xlink:href="#glyph-0-11" x="97.921816" y="9.877"/>
<use xlink:href="#glyph-0-4" x="101.902898" y="9.877"/>
<use xlink:href="#glyph-0-12" x="108.549989" y="9.877"/>
<use xlink:href="#glyph-0-10" x="111.873534" y="9.877"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-13" x="121.844171" y="9.877"/>
<use xlink:href="#glyph-0-10" x="129.148798" y="9.877"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-14" x="139.119435" y="9.877"/>
<use xlink:href="#glyph-0-10" x="146.424062" y="9.877"/>
<use xlink:href="#glyph-0-8" x="153.071154" y="9.877"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-15" x="157.542398" y="9.877"/>
<use xlink:href="#glyph-0-7" x="164.18949" y="9.877"/>
<use xlink:href="#glyph-0-1" x="171.494117" y="9.877"/>
<use xlink:href="#glyph-0-4" x="178.798744" y="9.877"/>
<use xlink:href="#glyph-0-15" x="185.445835" y="9.877"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 0.00153125 -0.00084375 L 0.00153125 12.30775 " transform="matrix(1, 0, 0, -1, 201.393, 13.464)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.0014375 L -0.0006875 12.307156 " transform="matrix(1, 0, 0, -1, 0.321, 25.772)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0011875 -0.0014375 L -0.0011875 12.307156 " transform="matrix(1, 0, 0, -1, 52.134, 25.772)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-2" x="61.321" y="22.185"/>
<use xlink:href="#glyph-0-1" x="64.644546" y="22.185"/>
<use xlink:href="#glyph-0-11" x="71.949173" y="22.185"/>
<use xlink:href="#glyph-0-10" x="75.930254" y="22.185"/>
<use xlink:href="#glyph-0-8" x="82.577346" y="22.185"/>
<use xlink:href="#glyph-0-10" x="87.227918" y="22.185"/>
<use xlink:href="#glyph-0-15" x="93.87501" y="22.185"/>
<use xlink:href="#glyph-0-4" x="100.522101" y="22.185"/>
<use xlink:href="#glyph-0-13" x="107.169192" y="22.185"/>
<use xlink:href="#glyph-0-4" x="114.473819" y="22.185"/>
<use xlink:href="#glyph-0-15" x="121.12091" y="22.185"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-10" x="131.091547" y="22.185"/>
<use xlink:href="#glyph-0-1" x="137.738638" y="22.185"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-0-4" x="148.366811" y="22.185"/>
<use xlink:href="#glyph-0-13" x="155.013902" y="22.185"/>
<use xlink:href="#glyph-0-7" x="162.31853" y="22.185"/>
<use xlink:href="#glyph-0-14" x="169.623157" y="22.185"/>
<use xlink:href="#glyph-0-11" x="176.927784" y="22.185"/>
<use xlink:href="#glyph-0-4" x="180.908866" y="22.185"/>
<use xlink:href="#glyph-0-8" x="187.555957" y="22.185"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 0.00153125 -0.0014375 L 0.00153125 12.307156 " transform="matrix(1, 0, 0, -1, 201.393, 25.772)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.00165625 L 201.073531 -0.00165625 " transform="matrix(1, 0, 0, -1, 0.321, 25.971)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 0.0015625 L -0.0006875 12.103125 " transform="matrix(1, 0, 0, -1, 0.321, 38.275)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-0" x="6.298" y="34.688"/>
<use xlink:href="#glyph-1-1" x="8.952054" y="34.688"/>
<use xlink:href="#glyph-1-2" x="15.599146" y="34.688"/>
<use xlink:href="#glyph-1-1" x="22.246237" y="34.688"/>
<use xlink:href="#glyph-1-3" x="28.893328" y="34.688"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-4" x="33.352618" y="34.688"/>
<use xlink:href="#glyph-1-5" x="36.676163" y="34.688"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0011875 0.0015625 L -0.0011875 12.103125 " transform="matrix(1, 0, 0, -1, 52.134, 38.275)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-6" x="120.116" y="34.688"/>
<use xlink:href="#glyph-1-7" x="126.763091" y="34.688"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 0.00153125 0.0015625 L 0.00153125 12.103125 " transform="matrix(1, 0, 0, -1, 201.393, 38.275)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 0.00134375 L 201.073531 0.00134375 " transform="matrix(1, 0, 0, -1, 0.321, 38.474)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 0.00065625 L -0.0006875 12.106125 " transform="matrix(1, 0, 0, -1, 0.321, 50.778)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-2" x="6.298" y="47.191"/>
<use xlink:href="#glyph-1-1" x="12.945091" y="47.191"/>
<use xlink:href="#glyph-1-4" x="19.592182" y="47.191"/>
<use xlink:href="#glyph-1-5" x="22.915728" y="47.191"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0011875 0.00065625 L -0.0011875 12.106125 " transform="matrix(1, 0, 0, -1, 52.134, 50.778)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-7" x="116.793" y="47.191"/>
<use xlink:href="#glyph-1-8" x="123.440091" y="47.191"/>
<use xlink:href="#glyph-1-9" x="130.087182" y="47.191"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 0.00153125 0.00065625 L 0.00153125 12.106125 " transform="matrix(1, 0, 0, -1, 201.393, 50.778)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 0.0004375 L 201.073531 0.0004375 " transform="matrix(1, 0, 0, -1, 0.321, 50.977)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.00025 L -0.0006875 12.105219 " transform="matrix(1, 0, 0, -1, 0.321, 63.281)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-4" x="6.298" y="59.694"/>
<use xlink:href="#glyph-1-5" x="9.621546" y="59.694"/>
<use xlink:href="#glyph-1-3" x="16.268637" y="59.694"/>
</g>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-4" x="20.727926" y="59.694"/>
<use xlink:href="#glyph-1-10" x="24.051472" y="59.694"/>
<use xlink:href="#glyph-1-2" x="30.698563" y="59.694"/>
<use xlink:href="#glyph-1-1" x="37.345654" y="59.694"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0011875 -0.00025 L -0.0011875 12.105219 " transform="matrix(1, 0, 0, -1, 52.134, 63.281)"/>
<g fill="rgb(0%, 0%, 0%)" fill-opacity="1">
<use xlink:href="#glyph-1-7" x="120.116" y="59.694"/>
<use xlink:href="#glyph-1-11" x="126.763091" y="59.694"/>
</g>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M 0.00153125 -0.00025 L 0.00153125 12.105219 " transform="matrix(1, 0, 0, -1, 201.393, 63.281)"/>
<path fill="none" stroke-width="0.398" stroke-linecap="butt" stroke-linejoin="miter" stroke="rgb(0%, 0%, 0%)" stroke-opacity="1" stroke-miterlimit="10" d="M -0.0006875 -0.00046875 L 201.073531 -0.00046875 " transform="matrix(1, 0, 0, -1, 0.321, 63.48)"/>
</svg>
