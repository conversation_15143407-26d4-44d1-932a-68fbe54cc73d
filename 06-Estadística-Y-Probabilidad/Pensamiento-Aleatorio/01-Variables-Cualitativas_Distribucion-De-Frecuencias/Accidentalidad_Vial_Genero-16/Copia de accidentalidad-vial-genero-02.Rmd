---
output:
  word_document: default
  html_document: default
  pdf_document: default
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Aleatorizar ciudades para el contexto del problema
ciudades <- c("Lima", "Bogotá", "Ciudad de México", "Santiago", "Buenos Aires", 
              "Quito", "Caracas", "La Paz", "Asunción", "Montevideo")
ciudades_seleccionadas <- sample(ciudades, sample(3:5, 1))
ciudades_texto <- paste(ciudades_seleccionadas, collapse = ", ")

# Aleatorizar años para el estudio (mantener 4 años consecutivos)
año_inicial <- sample(2000:2018, 1)
años <- año_inicial:(año_inicial + 3)
años_texto <- paste(min(años), "y", max(años))

# Generar datos de mortalidad total
base_mortalidad <- sample(4000:6000, 1)
variacion_maxima <- round(base_mortalidad * 0.15)  # Variación máxima de 15%

# Generar datos totales con variación aleatoria pero con tendencia realista
set.seed(sample(1:1000, 1))
mortalidad_total <- numeric(4)
tendencia <- sample(c("creciente", "decreciente", "pico", "valle"), 1)

if (tendencia == "creciente") {
  factor_incremento <- seq(1, 1.15, length.out = 4)
  mortalidad_total <- round(base_mortalidad * factor_incremento + rnorm(4, 0, variacion_maxima * 0.3))
} else if (tendencia == "decreciente") {
  factor_decremento <- seq(1.15, 1, length.out = 4)
  mortalidad_total <- round(base_mortalidad * factor_decremento + rnorm(4, 0, variacion_maxima * 0.3))
} else if (tendencia == "pico") {
  factores <- c(1, 1.08, 1.15, 1.05)
  mortalidad_total <- round(base_mortalidad * factores + rnorm(4, 0, variacion_maxima * 0.3))
} else { # valle
  factores <- c(1.1, 1.02, 1, 1.08)
  mortalidad_total <- round(base_mortalidad * factores + rnorm(4, 0, variacion_maxima * 0.3))
}

# Asegurar que todos los valores sean positivos y tengan magnitud adecuada
mortalidad_total <- pmax(mortalidad_total, base_mortalidad * 0.9)
mortalidad_total <- pmin(mortalidad_total, base_mortalidad * 1.2)
mortalidad_total <- round(mortalidad_total)

# Generar datos para hombres (aproximadamente 80-85% del total)
proporcion_hombres <- runif(4, 0.8, 0.85)
mortalidad_hombres <- round(mortalidad_total * proporcion_hombres)

# Calcular datos para mujeres (el resto)
mortalidad_mujeres <- mortalidad_total - mortalidad_hombres

# Opciones de respuesta (4 tipos de gráficas)
# Establecer la gráfica de líneas separadas como la única correcta
opciones <- c("lineas_separadas", "lineas_superpuestas", "barras_apiladas", "barras_agrupadas")
opcion_correcta <- "lineas_separadas"
indice_correcto <- 1  # Índice de "lineas_separadas" en el vector opciones

# Vector de solución para r-exams
solucion <- integer(4)
solucion[indice_correcto] <- 1
```

```{r generar_codigo_tikz}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función para generar el código TikZ de la tabla
generar_tabla_tikz <- function(años, datos_hombres) {
  # Crear tabla con TikZ
  tabla_code <- c("\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|c|c|}",
    "    \\hline",
    "    \\rowcolor{orange!20}",
    "    \\textbf{Año} & \\textbf{Personas de género masculino} \\\\",
    "    \\textbf{} & \\textbf{víctimas de accidentalidad vial} \\\\",
    "    \\hline")
  
  # Añadir filas con datos
  for (i in 1:length(años)) {
    tabla_code <- c(tabla_code, paste0("    ", años[i], " & ", format(datos_hombres[i], big.mark = ","), " \\\\"))
    tabla_code <- c(tabla_code, "    \\hline")
  }
  
  # Cerrar la tabla
  tabla_code <- c(tabla_code,
    "  \\end{tabular}",
    "};",
    "\\end{tikzpicture}")
  
  return(tabla_code)
}

# Generar código TikZ para la tabla
tabla_tikz <- generar_tabla_tikz(años, mortalidad_hombres)
```

```{r generar_graficas_python}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Código Python para las gráficas
codigo_base_python <- "
import matplotlib.pyplot as plt
import numpy as np
import matplotlib
matplotlib.rcParams['font.size'] = 9

años = %s
mortalidad_hombres = %s
mortalidad_mujeres = %s
"

# Reemplazar valores en el código Python
codigo_python_base <- sprintf(codigo_base_python, 
                             paste(años, collapse=", "), 
                             paste(mortalidad_hombres, collapse=", "), 
                             paste(mortalidad_mujeres, collapse=", "))

# Código para graficar los totales
codigo_python_grafica_total <- paste0(codigo_python_base, "
# Configuración de la figura
plt.figure(figsize=(6, 3.5))

# Datos totales
mortalidad_total = [h + m for h, m in zip(mortalidad_hombres, mortalidad_mujeres)]

# Graficar los puntos y líneas
plt.plot(años, mortalidad_total, 'ro-', linewidth=2, markersize=8)

# Añadir etiquetas a cada punto
for x, y in zip(años, mortalidad_total):
    plt.text(x, y + 100, f'{y:,}', ha='center', va='bottom', fontweight='bold', color='red')

# Configuración del gráfico
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(años)
plt.ylim(min(mortalidad_total) * 0.9, max(mortalidad_total) * 1.1)
plt.xlabel('Año')
plt.ylabel('Número de víctimas')
plt.tight_layout()

# Guardar gráfica
plt.savefig('grafica_total.png', dpi=150)
plt.close()
")

# Código para las gráficas de opciones

# Opción 1: Líneas separadas (masculino y femenino) - CORRECTA
codigo_python_opcion1 <- paste0(codigo_python_base, "
plt.figure(figsize=(5, 3.5))

# Graficar líneas separadas para hombres y mujeres
plt.plot(años, mortalidad_hombres, 'bo-', label='Masculino', linewidth=2)
plt.plot(años, mortalidad_mujeres, 'mo-', label='Femenino', linewidth=2)

# Añadir etiquetas a cada punto
for x, y in zip(años, mortalidad_hombres):
    plt.text(x, y + 50, f'{y:,}', ha='center', va='bottom', color='blue', fontsize=8)
for x, y in zip(años, mortalidad_mujeres):
    plt.text(x, y - 100, f'{y:,}', ha='center', va='top', color='magenta', fontsize=8)

# Configuración del gráfico
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(años)
plt.ylim(0, max(mortalidad_hombres) * 1.15)
plt.xlabel('Año')
plt.ylabel('Número de víctimas')
plt.legend()
plt.tight_layout()

# Guardar gráfica
plt.savefig('opcion1.png', dpi=150)
plt.close()
")

# Opción 2: Líneas superpuestas con datos incorrectos (intercambiar datos)
codigo_python_opcion2 <- paste0(codigo_python_base, "
plt.figure(figsize=(5, 3.5))

# Invertir datos para crear un distractor convincente
mortalidad_hombres_invertidos = mortalidad_mujeres
mortalidad_mujeres_invertidas = mortalidad_hombres
mortalidad_total_incorrecto = [h + m for h, m in zip(mortalidad_hombres_invertidos, mortalidad_mujeres_invertidas)]

# Graficar líneas con datos incorrectos
plt.plot(años, mortalidad_hombres_invertidos, 'g-', label='Masculino', linewidth=2)
plt.plot(años, mortalidad_total_incorrecto, 'y-', label='Total', linewidth=2)
plt.fill_between(años, mortalidad_hombres_invertidos, mortalidad_total_incorrecto, color='g', alpha=0.15)
plt.fill_between(años, 0, mortalidad_hombres_invertidos, color='y', alpha=0.15)

# Añadir etiquetas
for i, (x, y1, y2) in enumerate(zip(años, mortalidad_hombres_invertidos, mortalidad_mujeres_invertidas)):
    plt.text(x, y1/2, f'{y1:,}', ha='center', va='center', color='darkgreen', fontsize=8, fontweight='bold')
    plt.text(x, y1 + y2/2, f'{y2:,}', ha='center', va='center', color='orange', fontsize=8, fontweight='bold')

# Configuración del gráfico
plt.grid(True, linestyle='--', alpha=0.7)
plt.xticks(años)
plt.ylim(0, max(mortalidad_total_incorrecto) * 1.1)
plt.xlabel('Año')
plt.ylabel('Número de víctimas')
plt.legend()
plt.tight_layout()

# Guardar gráfica
plt.savefig('opcion2.png', dpi=150)
plt.close()
")

# Opción 3: Barras apiladas con proporciones incorrectas
codigo_python_opcion3 <- paste0(codigo_python_base, "
plt.figure(figsize=(5, 3.5))

# Usar proporciones incorrectas (50-50 en lugar de la proporción real)
mortalidad_total_correcto = [h + m for h, m in zip(mortalidad_hombres, mortalidad_mujeres)]
mortalidad_hombres_incorrecto = [t * 0.5 for t in mortalidad_total_correcto]
mortalidad_mujeres_incorrecto = [t * 0.5 for t in mortalidad_total_correcto]

# Crear barras apiladas con datos incorrectos
años_str = [str(año) for año in años]
p1 = plt.bar(años_str, mortalidad_hombres_incorrecto, color='r')
p2 = plt.bar(años_str, mortalidad_mujeres_incorrecto, bottom=mortalidad_hombres_incorrecto, color='orange')

# Añadir etiquetas a cada barra
for i, valor in enumerate(mortalidad_hombres_incorrecto):
    plt.text(i, valor/2, f'{int(valor):,}', ha='center', va='center', color='white', fontweight='bold', fontsize=8)
for i, (hombre, mujer) in enumerate(zip(mortalidad_hombres_incorrecto, mortalidad_mujeres_incorrecto)):
    plt.text(i, hombre + mujer/2, f'{int(mujer):,}', ha='center', va='center', color='white', fontweight='bold', fontsize=8)

# Configuración del gráfico
plt.grid(True, linestyle='--', alpha=0.7, axis='y')
plt.ylim(0, max(mortalidad_total_correcto) * 1.1)
plt.xlabel('Año')
plt.ylabel('Número de víctimas')
plt.legend(['Masculino', 'Femenino'])
plt.tight_layout()

# Guardar gráfica
plt.savefig('opcion3.png', dpi=150)
plt.close()
")

# Opción 4: Barras agrupadas con tendencia incorrecta
codigo_python_opcion4 <- paste0(codigo_python_base, "
plt.figure(figsize=(5, 3.5))

# Crear datos con tendencia incorrecta (invertir la tendencia)
mortalidad_hombres_invertidos = list(reversed(mortalidad_hombres))
mortalidad_mujeres_invertidos = list(reversed(mortalidad_mujeres))

# Configurar barras agrupadas
x = np.arange(len(años))
width = 0.35

# Crear barras agrupadas con datos incorrectos
p1 = plt.bar(x - width/2, mortalidad_hombres_invertidos, width, color='g')
p2 = plt.bar(x + width/2, mortalidad_mujeres_invertidos, width, color='m')

# Añadir etiquetas a cada barra
for i, valor in enumerate(mortalidad_hombres_invertidos):
    plt.text(i - width/2, valor + 50, f'{valor:,}', ha='center', va='bottom', color='darkgreen', fontsize=8)
for i, valor in enumerate(mortalidad_mujeres_invertidos):
    plt.text(i + width/2, valor + 50, f'{valor:,}', ha='center', va='bottom', color='darkmagenta', fontsize=8)

# Configuración del gráfico
plt.grid(True, linestyle='--', alpha=0.7, axis='y')
plt.xticks(x, años)
plt.ylim(0, max(mortalidad_hombres) * 1.3)
plt.xlabel('Año')
plt.ylabel('Número de víctimas')
plt.legend(['Masculino', 'Femenino'])
plt.tight_layout()

# Guardar gráfica
plt.savefig('opcion4.png', dpi=150)
plt.close()
")

# Ejecutar los códigos de Python para generar las gráficas
py_run_string(codigo_python_grafica_total)
py_run_string(codigo_python_opcion1)
py_run_string(codigo_python_opcion2)
py_run_string(codigo_python_opcion3)
py_run_string(codigo_python_opcion4)
```

```{r pruebas_diversidad, echo=FALSE, results='hide'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función para generar una huella digital de los datos para probar diversidad
generar_huella <- function(datos_total, datos_hombres, datos_mujeres) {
  huella <- paste(
    paste(datos_total, collapse="-"),
    paste(datos_hombres, collapse="-"),
    paste(datos_mujeres, collapse="-"),
    sep="_"
  )
  return(digest(huella))
}

# Prueba de diversidad - generar 100 conjuntos diferentes de datos
set.seed(123)
huellas <- character(100)
for (i in 1:100) {
  # Generar datos con el mismo proceso que usamos antes
  base_mortalidad_test <- sample(4000:6000, 1)
  variacion_maxima_test <- round(base_mortalidad_test * 0.15)
  
  tendencia_test <- sample(c("creciente", "decreciente", "pico", "valle"), 1)
  mortalidad_total_test <- numeric(4)
  
  if (tendencia_test == "creciente") {
    factor_incremento <- seq(1, 1.15, length.out = 4)
    mortalidad_total_test <- round(base_mortalidad_test * factor_incremento + rnorm(4, 0, variacion_maxima_test * 0.3))
  } else if (tendencia_test == "decreciente") {
    factor_decremento <- seq(1.15, 1, length.out = 4)
    mortalidad_total_test <- round(base_mortalidad_test * factor_decremento + rnorm(4, 0, variacion_maxima_test * 0.3))
  } else if (tendencia_test == "pico") {
    factores <- c(1, 1.08, 1.15, 1.05)
    mortalidad_total_test <- round(base_mortalidad_test * factores + rnorm(4, 0, variacion_maxima_test * 0.3))
  } else { # valle
    factores <- c(1.1, 1.02, 1, 1.08)
    mortalidad_total_test <- round(base_mortalidad_test * factores + rnorm(4, 0, variacion_maxima_test * 0.3))
  }
  
  mortalidad_total_test <- pmax(mortalidad_total_test, base_mortalidad_test * 0.9)
  mortalidad_total_test <- pmin(mortalidad_total_test, base_mortalidad_test * 1.2)
  mortalidad_total_test <- round(mortalidad_total_test)
  
  proporcion_hombres_test <- runif(4, 0.8, 0.85)
  mortalidad_hombres_test <- round(mortalidad_total_test * proporcion_hombres_test)
  mortalidad_mujeres_test <- mortalidad_total_test - mortalidad_hombres_test
  
  # Generar huella digital
  huellas[i] <- generar_huella(mortalidad_total_test, mortalidad_hombres_test, mortalidad_mujeres_test)
}

# Verificar número de huellas únicas
n_unicas <- length(unique(huellas))
diversidad_suficiente <- n_unicas >= 90  # Queremos al menos 90% de diversidad
```

Question
========

Una empresa dedicada a estudiar los datos de accidentes de tránsito en varias de las ciudades del país, ha realizado un análisis donde se muestra el registro de mortalidad por accidentes de tránsito entre los años `r años[1]` y `r años[4]`.

```{r grafica_total, echo=FALSE, results='asis', fig.align='center'}
# Usando método alternativo para incluir imágenes
cat("![](grafica_total.png)")
```

Se necesita clasificar estos datos por género masculino y femenino. La tabla muestra el número de víctimas de género masculino por año.

```{r tabla_tikz, echo=FALSE, results='asis'}
include_tikz(tabla_tikz, 
             name = "tabla_datos", 
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "8cm")
```

¿Cuál es la gráfica que muestra los resultados de mortalidad por accidentes de tránsito diferenciados por género?

Answerlist
----------

```{r options, echo=FALSE, results='asis'}
# Mostrar las opciones de gráficas usando método alternativo
cat("-\n")
cat("![](opcion1.png)\n\n")
cat("-\n")
cat("![](opcion2.png)\n\n")
cat("-\n")
cat("![](opcion3.png)\n\n")
cat("-\n")
cat("![](opcion4.png)\n\n")
```

Solution
========

La respuesta correcta es la gráfica que representa de manera precisa los datos de mortalidad por género:

- Número total de víctimas por año: `r paste(format(mortalidad_total, big.mark=","), collapse=", ")`
- Víctimas de género masculino por año: `r paste(format(mortalidad_hombres, big.mark=","), collapse=", ")`
- Víctimas de género femenino por año: `r paste(format(mortalidad_mujeres, big.mark=","), collapse=", ")`

La gráfica correcta debe mostrar claramente la diferencia entre la mortalidad masculina y femenina para cada año del estudio.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: mortalidad_transito_genero
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Interpretación de gráficas
