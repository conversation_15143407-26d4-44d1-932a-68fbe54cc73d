---
output:
  html_document: default
  pdf_document: default
  word_document: default
---
```{r inicio, include=FALSE}
library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.width = 8,
  fig.height = 5,
  out.width = "100%",
  fig.align = "center"
)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
# Definición de variables para el problema
# Para este problema, podemos variar los tiempos de las diferentes fases
# del vuelo y los estilos de las gráficas

# Vector con posibles valores para los tiempos (en segundos)
# Tiempo de aumento de altura (25-35 seg)
tiempos_aumento <- sample(seq(25, 35, by = 5), 1)
tiempos_pirueta <- sample(seq(3, 7, by = 1), 1)    # Tiempo de pirueta (3-7 seg)
tiempos_elevacion <- sample(seq(4, 6, by = 1), 1)  # Tiempo de elevación adicional (4-6 seg)
tiempos_giro <- sample(seq(15, 25, by = 5), 1)     # Tiempo de giro (15-25 seg)
# Tiempo de aterrizaje (20-30 seg)
tiempos_aterrizaje <- sample(seq(20, 30, by = 5), 1)

# Alturas máximas posibles (en metros)
altura_maxima <- sample(seq(100, 200, by = 20), 1)

# Definir variables para mostrar en el texto del problema
tiempo_total <- tiempos_aumento + tiempos_pirueta + tiempos_elevacion +
  tiempos_giro + tiempos_aterrizaje

# Estilos para las gráficas
colores_correctos <- c("blue", "darkgreen", "purple")
estilos_linea <- c("-", "--", "-.")
grosores_linea <- c(2, 2.5, 3)

# Seleccionar aleatoriamente los estilos para la gráfica correcta
indice_estilo <- sample(1:3, 1)
color_correcto <- colores_correctos[indice_estilo]
estilo_linea <- estilos_linea[indice_estilo]
grosor_linea <- grosores_linea[indice_estilo]

# Definir los tipos de errores para las gráficas incorrectas
# 1. Falta la fase de elevación adicional
# 2. Pirueta con pico (no descenso constante)
# 3. Mantiene altura después del ascenso (sin pirueta)
# 4. Giro con altura decreciente (no mantiene altura constante)
# 5. Altura máxima incorrecta (demasiado alta o baja)
# 6. Aterrizaje no constante (con oscilaciones)

# Seleccionar 3 tipos de errores distintos de los 6 disponibles
errores_seleccionados <- sample(1:6, 3, replace = FALSE)

# Asignar colores para las gráficas incorrectas (distintos al color correcto)
colores_incorrectos <- setdiff(c("red", "orange", "brown", "gray", "black"), color_correcto)[1:3]

# Definir la posición de la respuesta correcta (1-4)
posicion_correcta <- sample(1:4, 1)

# Crear un vector con la solución (0001, 0010, 0100 o 1000)
solucion <- rep(0, 4)
solucion[posicion_correcta] <- 1
solucion_str <- paste(solucion, collapse = "")

# Generar ID para los chunks de Python (necesario para múltiples chunks de Python)
ids_chunks <- sample(10000:99999, 4)
```

```{r GenerarCodigosPython, message=FALSE, warning=FALSE, results='hide'}
# Función para generar el código Python para una gráfica específica
generar_codigo_python <- function(tipo_grafica, color, id_chunk) {
  codigo <- "import matplotlib.pyplot as plt\nimport numpy as np\n\n"
  codigo <- paste0(codigo, "# Configuración para eliminar etiquetas\n")
  codigo <- paste0(codigo, "plt.rcParams['figure.figsize'] = (8, 5)\n")
  codigo <- paste0(codigo, "plt.rcParams['figure.titlesize'] = 0\n\n")

  # Obtener variables de R
  codigo <- paste0(codigo, "# Obtener variables de R\n")
  codigo <- paste0(codigo, "tiempo_aumento = r.tiempos_aumento\n")
  codigo <- paste0(codigo, "tiempo_pirueta = r.tiempos_pirueta\n")
  codigo <- paste0(codigo, "tiempo_elevacion = r.tiempos_elevacion\n")
  codigo <- paste0(codigo, "tiempo_giro = r.tiempos_giro\n")
  codigo <- paste0(codigo, "tiempo_aterrizaje = r.tiempos_aterrizaje\n")
  codigo <- paste0(codigo, "altura_max = r.altura_maxima\n\n")

  # Generar código específico según el tipo de gráfica
  if (tipo_grafica == "correcta") {
    # Gráfica correcta
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica correcta\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (descenso constante)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max/3])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Elevación adicional\n")
    codigo <- paste0(codigo, "x_elevacion = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y_elevacion = np.array([altura_max/3, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Giro a altura constante\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 5: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max, 0])\n\n")

  } else if (tipo_grafica == "error1") {
    # Error 1: Falta la fase de elevación adicional
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Falta elevación adicional\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (descenso constante)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max/2])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Giro a altura constante (falta la fase de elevación)\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max/2, altura_max/2])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max/2, 0])\n\n")

  } else if (tipo_grafica == "error2") {
    # Error 2: Pirueta con pico (no descenso constante)
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Pirueta con pico\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (con pico incorrecto)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta/2, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max*1.3, altura_max/2])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Elevación adicional\n")
    codigo <- paste0(codigo, "x_elevacion = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y_elevacion = np.array([altura_max/2, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Giro a altura constante\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 5: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max, 0])\n\n")

  } else if (tipo_grafica == "error3") {
    # Error 3: Mantiene altura después del ascenso (sin pirueta)
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Sin pirueta, mantiene altura\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Mantener altura en vez de hacer pirueta (incorrecto)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Giro a altura constante\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max, 0])\n\n")

  } else if (tipo_grafica == "error4") {
    # Error 4: Giro con altura decreciente (no mantiene altura constante)
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Giro con altura decreciente\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (descenso constante)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max/3])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Elevación adicional\n")
    codigo <- paste0(codigo, "x_elevacion = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y_elevacion = np.array([altura_max/3, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Giro con altura decreciente (incorrecto)\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max, altura_max*0.7])\n\n")

    codigo <- paste0(codigo, "# Fase 5: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max*0.7, 0])\n\n")

  } else if (tipo_grafica == "error5") {
    # Error 5: Altura máxima incorrecta (demasiado alta)
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Altura máxima incorrecta\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura (demasiado alta)\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max*1.5])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (descenso constante)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max*1.5, altura_max*0.5])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Elevación adicional\n")
    codigo <- paste0(codigo, "x_elevacion = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y_elevacion = np.array([altura_max*0.5, altura_max*1.5])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Giro a altura constante\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max*1.5, altura_max*1.5])\n\n")

    codigo <- paste0(codigo, "# Fase 5: Aterrizaje (descenso constante)\n")
    codigo <- paste0(codigo, "x4 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje])\n")
    codigo <- paste0(codigo, "y4 = np.array([altura_max*1.5, 0])\n\n")

  } else if (tipo_grafica == "error6") {
    # Error 6: Aterrizaje no constante (con oscilaciones)
    codigo <- paste0(codigo, "# Definir puntos clave para la gráfica con error: Aterrizaje con oscilaciones\n")
    codigo <- paste0(codigo, "# Fase 1: Aumento constante de altura\n")
    codigo <- paste0(codigo, "x1 = np.array([0, tiempo_aumento])\n")
    codigo <- paste0(codigo, "y1 = np.array([0, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 2: Pirueta (descenso constante)\n")
    codigo <- paste0(codigo, "x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_pirueta])\n")
    codigo <- paste0(codigo, "y2 = np.array([altura_max, altura_max/3])\n\n")

    codigo <- paste0(codigo, "# Fase 3: Elevación adicional\n")
    codigo <- paste0(codigo, "x_elevacion = np.array([tiempo_aumento + tiempo_pirueta, tiempo_aumento + tiempo_pirueta + tiempo_elevacion])\n")
    codigo <- paste0(codigo, "y_elevacion = np.array([altura_max/3, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 4: Giro a altura constante\n")
    codigo <- paste0(codigo, "x3 = np.array([tiempo_aumento + tiempo_pirueta + tiempo_elevacion, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro])\n")
    codigo <- paste0(codigo, "y3 = np.array([altura_max, altura_max])\n\n")

    codigo <- paste0(codigo, "# Fase 5: Aterrizaje con oscilaciones (incorrecto)\n")
    codigo <- paste0(codigo, "t_aterrizaje = np.linspace(tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje, 50)\n")
    codigo <- paste0(codigo, "h_aterrizaje = altura_max * (1 - (t_aterrizaje - (tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro))/tiempo_aterrizaje) + altura_max * 0.1 * np.sin(10 * t_aterrizaje)\n")
    codigo <- paste0(codigo, "h_aterrizaje = np.clip(h_aterrizaje, 0, None)  # Asegurar que no haya alturas negativas\n\n")
  }

  # Crear figura y graficar
  codigo <- paste0(codigo, "# Crear figura\n")
  codigo <- paste0(codigo, "fig = plt.figure()\n")
  codigo <- paste0(codigo, "ax = fig.add_subplot(111)\n\n")

  # Graficar cada segmento con el color seleccionado
  codigo <- paste0(codigo, "# Graficar cada segmento\n")

  if (tipo_grafica == "correcta") {
    # Adaptar estilo de línea para formato correcto de matplotlib
    estilo_plt <- ""
    if (estilo_linea == "-") {
      estilo_plt <- "solid"
    } else if (estilo_linea == "--") {
      estilo_plt <- "dashed"
    } else if (estilo_linea == "-.") {
      estilo_plt <- "dashdot"
    }

    codigo <- paste0(codigo, "ax.plot(x1, y1, color='", color, "', linestyle='", estilo_plt, "', linewidth=", grosor_linea, ")\n")
    codigo <- paste0(codigo, "ax.plot(x2, y2, color='", color, "', linestyle='", estilo_plt, "', linewidth=", grosor_linea, ")\n")
    codigo <- paste0(codigo, "ax.plot(x_elevacion, y_elevacion, color='", color, "', linestyle='", estilo_plt, "', linewidth=", grosor_linea, ")\n")
    codigo <- paste0(codigo, "ax.plot(x3, y3, color='", color, "', linestyle='", estilo_plt, "', linewidth=", grosor_linea, ")\n")
    codigo <- paste0(codigo, "ax.plot(x4, y4, color='", color, "', linestyle='", estilo_plt, "', linewidth=", grosor_linea, ")\n\n")
  } else if (tipo_grafica == "error6") {
    codigo <- paste0(codigo, "ax.plot(x1, y1, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(x2, y2, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(x_elevacion, y_elevacion, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(x3, y3, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(t_aterrizaje, h_aterrizaje, color='", color, "', linewidth=2)\n\n")
  } else {
    codigo <- paste0(codigo, "ax.plot(x1, y1, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(x2, y2, color='", color, "', linewidth=2)\n")

    if (tipo_grafica != "error3") {
      codigo <- paste0(codigo, "ax.plot(x_elevacion, y_elevacion, color='", color, "', linewidth=2)\n")
    }

    codigo <- paste0(codigo, "ax.plot(x3, y3, color='", color, "', linewidth=2)\n")
    codigo <- paste0(codigo, "ax.plot(x4, y4, color='", color, "', linewidth=2)\n\n")
  }

  # Configuración del gráfico
  codigo <- paste0(codigo, "# Configuración del gráfico\n")
  codigo <- paste0(codigo, "ax.set_xlabel('Segundos', fontweight='bold')\n")
  codigo <- paste0(codigo, "ax.set_ylabel('Altura (m)', fontweight='bold')\n")
  codigo <- paste0(codigo, "ax.grid(True, linestyle='--', alpha=0.7)\n")

  if (tipo_grafica == "error5") {
    codigo <- paste0(codigo, "ax.set_xlim(0, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje + 5)\n")
    codigo <- paste0(codigo, "ax.set_ylim(0, altura_max * 1.6)\n\n")
  } else {
    codigo <- paste0(codigo, "ax.set_xlim(0, tiempo_aumento + tiempo_pirueta + tiempo_elevacion + tiempo_giro + tiempo_aterrizaje + 5)\n")
    codigo <- paste0(codigo, "ax.set_ylim(0, altura_max * 1.1)\n\n")
  }

  codigo <- paste0(codigo, "# Ajustar márgenes\n")
  codigo <- paste0(codigo, "plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)\n")
  codigo <- paste0(codigo, "plt.show()\n")

  return(codigo)
}

# Asignar tipo de gráfica a cada posición
tipos_graficas <- rep("", 4)
tipos_graficas[posicion_correcta] <- "correcta"

# Asignar los tipos de errores para las gráficas incorrectas
contador_error <- 1
for (i in 1:4) {
  if (i != posicion_correcta) {
    tipos_graficas[i] <- paste0("error", errores_seleccionados[contador_error])
    contador_error <- contador_error + 1
  }
}

# Asignar colores a cada gráfica
colores_graficas <- rep("", 4)
colores_graficas[posicion_correcta] <- color_correcto
contador_color <- 1
for (i in 1:4) {
  if (i != posicion_correcta) {
    colores_graficas[i] <- colores_incorrectos[contador_color]
    contador_color <- contador_color + 1
  }
}

# Generar los códigos Python para cada gráfica
codigos_python <- vector("list", 4)
for (i in 1:4) {
  codigos_python[[i]] <- generar_codigo_python(tipos_graficas[i], colores_graficas[i], ids_chunks[i])
}
```

Question
========

En una entrevista para una cadena de televisión, un piloto de un avión de acrobacias comenta en detalle cómo fue su presentación:

"Durante los primeros `r tiempos_aumento` segundos aumenté la altura de manera constante y luego, realicé una pirueta en la que descendí de manera constante durante `r tiempos_pirueta` segundos. Después del descenso, necesité recuperar la altitud para la siguiente maniobra, así que elevé nuevamente el avión durante `r tiempos_elevacion` segundos hasta alcanzar la altura máxima inicial. Una vez recuperada la altura, comencé a girar en torno al eje del avión durante `r tiempos_giro` segundos, manteniendo la misma altura. Para finalizar, aterricé reduciendo la altura de manera constante durante `r tiempos_aterrizaje` segundos".

¿Cuál de las siguientes gráficas representa de forma CORRECTA la información dada por el piloto?

Answerlist
----------
-
<br/>
```{python GraficoA, echo=FALSE, message=FALSE, comment='', warning=FALSE, results='hide', fig.cap=''}
```{r, echo=FALSE, results='asis'}
cat(codigos_python[[1]])
```

-
<br/>
```{python GraficoB, echo=FALSE, message=FALSE, comment='', warning=FALSE, results='hide', fig.cap=''}
```{r, echo=FALSE, results='asis'}
cat(codigos_python[[2]])
```

-
<br/>
```{python GraficoC, echo=FALSE, message=FALSE, comment='', warning=FALSE, results='hide', fig.cap=''}
```{r, echo=FALSE, results='asis'}
cat(codigos_python[[3]])
```

-
<br/>
```{python GraficoD, echo=FALSE, message=FALSE, comment='', warning=FALSE, results='hide', fig.cap=''}
```{r, echo=FALSE, results='asis'}
cat(codigos_python[[4]])
```

Solution
========

La gráfica correcta es la que representa adecuadamente las cinco fases del vuelo acrobático descritas por el piloto:

1. Durante los primeros `r tiempos_aumento` segundos: aumento constante de altura (línea ascendente).
2. Pirueta con descenso constante durante `r tiempos_pirueta` segundos (línea descendente).
3. Recuperación de altitud durante `r tiempos_elevacion` segundos hasta alcanzar la altura máxima inicial (línea ascendente).
4. Giro alrededor del eje del avión durante `r tiempos_giro` segundos manteniendo la misma altura (línea horizontal).
5. Aterrizaje con reducción constante de altura durante `r tiempos_aterrizaje` segundos (línea descendente final).

```{r GenerarExplicacionDetallada, results='asis'}
# Generar explicación detallada basada en los errores específicos seleccionados
explicaciones_errores <- list(
  error1 = "- Falta la fase de recuperación de altitud después de la pirueta. En esta gráfica el avión permanece a media altura después de la pirueta, cuando debería elevarse nuevamente hasta la altura máxima.",
  error2 = "- La pirueta no muestra un descenso constante como indicó el piloto, sino que presenta un pico hacia arriba (aumento de altura) antes del descenso, lo cual contradice la descripción.",
  error3 = "- No muestra la pirueta con descenso constante. En cambio, mantiene la altura constante después del ascenso inicial, contradiciendo la descripción del piloto.",
  error4 = "- Durante la fase de giro, la altura disminuye gradualmente cuando el piloto especificó que mantuvo la misma altura durante esta fase.",
  error5 = "- La altura máxima alcanzada es significativamente mayor que la mencionada por el piloto, lo que distorsiona toda la trayectoria del vuelo.",
  error6 = "- El aterrizaje muestra oscilaciones en lugar del descenso constante descrito por el piloto."
)

# Obtener explicaciones para los errores seleccionados
explicaciones_seleccionadas <- explicaciones_errores[paste0("error", errores_seleccionados)]

# Generar explicación basada en la posición correcta y los errores
cat("La gráfica correcta es la opción", LETTERS[posicion_correcta], ", que tiene color", color_correcto, "y muestra exactamente las cinco fases descritas por el piloto, incluyendo la fase de recuperación de altitud después de la pirueta.\n\n")
cat("Las demás opciones contienen los siguientes errores:\n\n")

# Mostrar explicaciones de los errores
for (explicacion in explicaciones_seleccionadas) {
  cat(explicacion, "\n")
}
```

```{r version_diversity_test, echo = FALSE, results = "hide"}
# Prueba de diversidad de versiones mejorada
test_that("Prueba de diversidad de versiones", {
  # Generar y almacenar 1000 versiones
  versiones <- list()
  for (i in 1:1000) {
    # Generar las variables
    tiempo_aumento_test <- sample(seq(25, 35, by = 5), 1)
    tiempo_pirueta_test <- sample(seq(3, 7, by = 1), 1)
    tiempo_elevacion_test <- sample(seq(4, 6, by = 1), 1)
    tiempo_giro_test <- sample(seq(15, 25, by = 5), 1)
    tiempo_aterrizaje_test <- sample(seq(20, 30, by = 5), 1)
    altura_maxima_test <- sample(seq(100, 200, by = 20), 1)

    # Seleccionar estilos aleatorios
    indice_estilo_test <- sample(1:3, 1)
    color_correcto_test <- c("blue", "darkgreen", "purple")[indice_estilo_test]

    # Seleccionar errores y posición correcta
    errores_test <- paste(sort(sample(1:6, 3, replace = FALSE)), collapse = "-")
    posicion_test <- sample(1:4, 1)

    # Combine the variables into a single string
    version_string <- paste(
      tiempo_aumento_test, tiempo_pirueta_test, tiempo_elevacion_test,
      tiempo_giro_test, tiempo_aterrizaje_test, altura_maxima_test,
      color_correcto_test, errores_test, posicion_test, collapse = "-"
    )

    # Create a hash of the data
    versiones[[i]] <- digest::digest(version_string)
  }

  # Verificar al menos 100 versiones únicas
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 100,
              info = paste("Solo se generaron", n_versiones_unicas,
                           "versiones únicas. Se requieren al menos 100."))
})
```

Meta-information
================

exname: vuelo_acrobatico_mejorado(single-choice)
extype: schoice
exsolution: `r solucion_str`
exshuffle: TRUE
exsection: Interpretación de gráficas
