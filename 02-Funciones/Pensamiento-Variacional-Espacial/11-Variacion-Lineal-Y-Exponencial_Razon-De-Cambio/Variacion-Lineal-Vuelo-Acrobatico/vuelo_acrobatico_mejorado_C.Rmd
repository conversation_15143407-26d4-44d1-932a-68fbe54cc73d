---
output:
  html_document: default
  pdf_document: default
  word_document: default
---
```{r inicio, include=FALSE}
library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',  # Importante: mantener todas las figuras
  out.width = "100%"  # Controlar el ancho de salida
)

# Configuración específica para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurarse que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
# Definición de variables para el problema
# Vector con posibles valores para los tiempos (en segundos)
# Tiempo de aumento de altura (25-35 seg)
tiempos_aumento <- sample(seq(25, 35, by = 5), 1)
tiempos_pirueta <- sample(seq(3, 7, by = 1), 1)    # Tiempo de pirueta (3-7 seg)
tiempos_giro <- sample(seq(15, 25, by = 5), 1)     # Tiempo de giro (15-25 seg)
# Tiempo de aterrizaje (20-30 seg)
tiempos_aterrizaje <- sample(seq(20, 30, by = 5), 1)

# Alturas máximas posibles (en metros)
altura_maxima <- sample(seq(100, 200, by = 20), 1)

# Definir variables para mostrar en el texto del problema
tiempo_total <- tiempos_aumento + tiempos_pirueta + tiempos_giro + tiempos_aterrizaje

# Selección de gráficas
# Seleccionar aleatoriamente 3 de las 6 gráficas incorrectas
graficas_incorrectas_seleccionadas <- sample(1:6, 3)
# Seleccionar aleatoriamente 1 de las 3 gráficas correctas
grafica_correcta_seleccionada <- sample(1:3, 1)

# Determinar el índice de la respuesta correcta (entre 1 y 4)
indice_correcto <- sample(1:4, 1)

# Vector para controlar el orden de las gráficas
orden_graficas <- numeric(4)
orden_graficas[indice_correcto] <- grafica_correcta_seleccionada + 10  # Código para gráficas correctas: 11, 12, 13
# Asignar las 3 gráficas incorrectas a las posiciones restantes
posiciones_incorrectas <- setdiff(1:4, indice_correcto)
for (i in 1:3) {
  orden_graficas[posiciones_incorrectas[i]] <- graficas_incorrectas_seleccionadas[i]  # Códigos para incorrectas: 1-6
}

# Vector para la solución
solucion <- numeric(4)
solucion[indice_correcto] <- 1

# Crear una variable para almacenar los chunks de Python
py_chunks <- character(4)

# Configuración general para las gráficas
py_config <- "
import matplotlib.pyplot as plt
import numpy as np

# Configuración para eliminar etiquetas
plt.rcParams['figure.figsize'] = (8, 5)
plt.rcParams['figure.titlesize'] = 0
"

# Colores para las gráficas correctas
colores_correctas <- c("'blue'", "'darkgreen'", "'purple'")
# Estilos de línea para gráficas correctas
estilos_linea <- c("'-'", "'--'", "'-.'")
# Grosores de línea para gráficas correctas
grosores_linea <- c(2, 2.5, 3)

# Generar el código Python para cada gráfica
for (i in 1:4) {
  tipo_grafica <- orden_graficas[i]
  
  # Gráficas correctas (11, 12, 13)
  if (tipo_grafica > 10) {
    num_correcta <- tipo_grafica - 10
    
    py_chunks[i] <- paste0(py_config, "
# Definir puntos clave para la gráfica correcta ", num_correcta, "
# Fase 1: Aumento constante de altura
x1 = np.array([0, ", tiempos_aumento, "])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Pirueta (descenso constante)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima/2, "])  # Descenso a mitad de altura

# Fase 3: Giro a altura constante
x3 = np.array([", tiempos_aumento + tiempos_pirueta, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro, "])
y3 = np.array([", altura_maxima/2, ", ", altura_maxima/2, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([", tiempos_aumento + tiempos_pirueta + tiempos_giro, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro + tiempos_aterrizaje, "])
y4 = np.array([", altura_maxima/2, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Color, estilo y grosor de línea específicos para esta variante correcta
color = ", colores_correctas[num_correcta], "
estilo = ", estilos_linea[num_correcta], "
grosor = ", grosores_linea[num_correcta], "

# Graficar cada segmento
ax.plot(x1, y1, color=color, linestyle=estilo, linewidth=grosor)
ax.plot(x2, y2, color=color, linestyle=estilo, linewidth=grosor)
ax.plot(x3, y3, color=color, linestyle=estilo, linewidth=grosor)
ax.plot(x4, y4, color=color, linestyle=estilo, linewidth=grosor)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
  } 
  # Gráficas incorrectas (1-6)
  else {
    if (tipo_grafica == 1) {
      # Gráfica incorrecta 1: Sin pirueta (mantiene altura constante)
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 1: Sin pirueta (mantiene altura constante)
# Fase 1: Aumento constante de altura
x1 = np.array([0, ", tiempos_aumento, "])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Mantener altura en vez de hacer pirueta (incorrecto)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima, "])

# Fase 3: Aterrizaje (descenso constante)
x3 = np.array([", tiempos_aumento + tiempos_pirueta + tiempos_giro, ", ", tiempo_total, "])
y3 = np.array([", altura_maxima, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, color='C0', linestyle='-', linewidth=2)
ax.plot(x2, y2, color='C0', linestyle='-', linewidth=2)
ax.plot(x3, y3, color='C0', linestyle='-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    } 
    else if (tipo_grafica == 2) {
      # Gráfica incorrecta 2: Con pico más alto en la pirueta
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 2: Con pico más alto en la pirueta
# Fase 1: Aumento constante de altura
x1 = np.array([0, ", tiempos_aumento, "])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Pirueta (con pico incorrecto)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta/2, ", ", tiempos_aumento + tiempos_pirueta, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima*1.5, ", ", altura_maxima/2, "])  # Pico más alto (incorrecto)

# Fase 3: Giro a altura constante
x3 = np.array([", tiempos_aumento + tiempos_pirueta, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro, "])
y3 = np.array([", altura_maxima/2, ", ", altura_maxima/2, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([", tiempos_aumento + tiempos_pirueta + tiempos_giro, ", ", tiempo_total, "])
y4 = np.array([", altura_maxima/2, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, color='red', linestyle='-', linewidth=2)
ax.plot(x2, y2, color='red', linestyle='-', linewidth=2)
ax.plot(x3, y3, color='red', linestyle='-', linewidth=2)
ax.plot(x4, y4, color='red', linestyle='-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.8, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    }
    else if (tipo_grafica == 3) {
      # Gráfica incorrecta 3: Con descenso incorrecto durante la pirueta
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 3: Con descenso incorrecto durante la pirueta
# Fase 1: Aumento constante de altura
x1 = np.array([0, ", tiempos_aumento, "])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Pirueta (descenso incorrecto - demasiado pronunciado)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima/4, "])  # Descenso más pronunciado

# Fase 3: Giro a altura constante
x3 = np.array([", tiempos_aumento + tiempos_pirueta, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro, "])
y3 = np.array([", altura_maxima/4, ", ", altura_maxima/4, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([", tiempos_aumento + tiempos_pirueta + tiempos_giro, ", ", tiempo_total, "])
y4 = np.array([", altura_maxima/4, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, color='C2', linestyle='-', linewidth=2)
ax.plot(x2, y2, color='C2', linestyle='-', linewidth=2)
ax.plot(x3, y3, color='C2', linestyle='-', linewidth=2)
ax.plot(x4, y4, color='C2', linestyle='-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    }
    else if (tipo_grafica == 4) {
      # Gráfica incorrecta 4: Con fase adicional después de la pirueta
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 4: Con fase adicional después de la pirueta
# Fase 1: Aumento constante de altura
x1 = np.array([0, ", tiempos_aumento, "])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Pirueta (descenso constante)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima/3, "])

# Fase EXTRA: Elevación adicional (incorrecto)
x_extra = np.array([", tiempos_aumento + tiempos_pirueta, ", ", tiempos_aumento + tiempos_pirueta + 5, "])
y_extra = np.array([", altura_maxima/3, ", ", altura_maxima, "])

# Fase 3: Giro a altura constante
x3 = np.array([", tiempos_aumento + tiempos_pirueta + 5, ", ", tiempos_aumento + tiempos_pirueta + 5 + tiempos_giro, "])
y3 = np.array([", altura_maxima, ", ", altura_maxima, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([", tiempos_aumento + tiempos_pirueta + 5 + tiempos_giro, ", ", tiempos_aumento + tiempos_pirueta + 5 + tiempos_giro + tiempos_aterrizaje, "])
y4 = np.array([", altura_maxima, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, color='orange', linestyle='-', linewidth=2)
ax.plot(x2, y2, color='orange', linestyle='-', linewidth=2)
ax.plot(x_extra, y_extra, color='orange', linestyle='-', linewidth=2)
ax.plot(x3, y3, color='orange', linestyle='-', linewidth=2)
ax.plot(x4, y4, color='orange', linestyle='-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempos_aumento + tiempos_pirueta + 5 + tiempos_giro + tiempos_aterrizaje + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    }
    else if (tipo_grafica == 5) {
      # Gráfica incorrecta 5: Con aumento no lineal de altura
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 5: Con aumento no lineal de altura
# Fase 1: Aumento no lineal de altura (incorrecto)
t1 = np.linspace(0, ", tiempos_aumento, ", 100)
y1 = ", altura_maxima, " * (t1/", tiempos_aumento, ")**2  # Curva parabólica (aceleración)

# Fase 2: Pirueta (descenso constante)
x2 = np.array([", tiempos_aumento, ", ", tiempos_aumento + tiempos_pirueta, "])
y2 = np.array([", altura_maxima, ", ", altura_maxima/2, "])

# Fase 3: Giro a altura constante
x3 = np.array([", tiempos_aumento + tiempos_pirueta, ", ", tiempos_aumento + tiempos_pirueta + tiempos_giro, "])
y3 = np.array([", altura_maxima/2, ", ", altura_maxima/2, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([", tiempos_aumento + tiempos_pirueta + tiempos_giro, ", ", tiempo_total, "])
y4 = np.array([", altura_maxima/2, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(t1, y1, color='C4', linewidth=2)
ax.plot(x2, y2, color='C4', linewidth=2)
ax.plot(x3, y3, color='C4', linewidth=2)
ax.plot(x4, y4, color='C4', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    }
    else if (tipo_grafica == 6) {
      # Gráfica incorrecta 6: Con tiempos incorrectos en las fases
      py_chunks[i] <- paste0(py_config, "
# Gráfica incorrecta 6: Con tiempos incorrectos en las fases
# Tiempo incorrecto para la primera fase (más corto)
tiempo_incorrecto1 = ", tiempos_aumento/2, "
tiempo_incorrecto2 = ", tiempos_pirueta*2, "  # Pirueta más larga

# Fase 1: Aumento constante de altura (tiempo incorrecto)
x1 = np.array([0, tiempo_incorrecto1])
y1 = np.array([0, ", altura_maxima, "])

# Fase 2: Pirueta (descenso constante, tiempo incorrecto)
x2 = np.array([tiempo_incorrecto1, tiempo_incorrecto1 + tiempo_incorrecto2])
y2 = np.array([", altura_maxima, ", ", altura_maxima/2, "])

# Fase 3: Giro a altura constante
x3 = np.array([tiempo_incorrecto1 + tiempo_incorrecto2, tiempo_incorrecto1 + tiempo_incorrecto2 + ", tiempos_giro, "])
y3 = np.array([", altura_maxima/2, ", ", altura_maxima/2, "])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([tiempo_incorrecto1 + tiempo_incorrecto2 + ", tiempos_giro, ", ", tiempo_total, "])
y4 = np.array([", altura_maxima/2, ", 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, color='C5', linestyle='-', linewidth=2)
ax.plot(x2, y2, color='C5', linestyle='-', linewidth=2)
ax.plot(x3, y3, color='C5', linestyle='-', linewidth=2)
ax.plot(x4, y4, color='C5', linestyle='-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, ", tiempo_total + 5, ")
ax.set_ylim(0, ", altura_maxima * 1.1, ")

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.savefig('grafica_", i, ".png', dpi=100)
plt.close()
")
    }
  }
}

# Ejecutar todos los chunks de Python para generar las gráficas
for (i in 1:4) {
  py_run_string(py_chunks[i])
}
```

Question
========

En una entrevista para una cadena de televisión, un piloto de un avión de acrobacias comenta en detalle cómo fue su presentación:

"Durante los primeros `r tiempos_aumento` segundos aumenté la altura de manera constante y luego, realicé una pirueta en la que descendí de manera constante durante `r tiempos_pirueta` segundos. Después del descenso, comencé a girar en torno al eje del avión durante `r tiempos_giro` segundos, manteniendo la misma altura. Para finalizar, aterricé reduciendo la altura de manera constante durante `r tiempos_aterrizaje` segundos".

¿Cuál de las siguientes gráficas representa de forma CORRECTA la información dada por el piloto?

Answerlist
----------
```{r Answerlist, results='asis', echo=FALSE}
for (i in 1:4) {
  cat("-\n<br/>\n")
  cat(paste0("![Gráfica ", i, "](grafica_", i, ".png)\n\n"))
}
```

Solution
========

La gráfica correcta es la que representa adecuadamente las cuatro fases del vuelo acrobático descritas por el piloto:

1. Durante los primeros `r tiempos_aumento` segundos: aumento constante de altura (línea ascendente).
2. Pirueta con descenso constante durante `r tiempos_pirueta` segundos (línea descendente).
3. Giro alrededor del eje del avión durante `r tiempos_giro` segundos manteniendo la misma altura (línea horizontal).
4. Aterrizaje con reducción constante de altura durante `r tiempos_aterrizaje` segundos (línea descendente final).

Las demás opciones contienen elementos incorrectos que no corresponden con la descripción dada por el piloto. Estos errores pueden incluir:

- Ausencia de la pirueta con descenso constante, manteniendo la altura constante después del ascenso inicial.
- Pico hacia arriba durante la pirueta, cuando el piloto mencionó un descenso constante.
- Descenso demasiado pronunciado durante la pirueta.
- Inclusión de una fase adicional de elevación después de la pirueta, que no fue mencionada por el piloto.
- Aumento no lineal de altura en la primera fase, cuando el piloto especificó un aumento constante.
- Tiempos incorrectos en las diferentes fases del vuelo.

```{r version diversity test, echo = FALSE, results = "hide"}
# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  # Generar y almacenar 1000 versiones
  versiones <- list()
  for (i in 1:1000) {
    # Generar las variables
    tiempo_aumento_test <- sample(seq(25, 35, by = 5), 1)
    tiempo_pirueta_test <- sample(seq(3, 7, by = 1), 1)
    tiempo_giro_test <- sample(seq(15, 25, by = 5), 1)
    tiempo_aterrizaje_test <- sample(seq(20, 30, by = 5), 1)
    altura_maxima_test <- sample(seq(100, 200, by = 20), 1)
    
    # Generar la selección de gráficas
    graficas_incorrectas_test <- paste(sort(sample(1:6, 3)), collapse = "-")
    grafica_correcta_test <- sample(1:3, 1)
    indice_correcto_test <- sample(1:4, 1)

    # Combine the variables into a single string
    version_string <- paste(tiempo_aumento_test, tiempo_pirueta_test,
                            tiempo_giro_test, tiempo_aterrizaje_test,
                            altura_maxima_test, graficas_incorrectas_test,
                            grafica_correcta_test, indice_correcto_test, 
                            collapse = "-")

    # Create a hash of the data
    versiones[[i]] <- digest::digest(version_string)
  }

  # Verificar al menos 100 versiones únicas
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 100,
              info = paste("Solo se generaron", n_versiones_unicas,
                           "versiones únicas. Se requieren al menos 100."))
})
```

Meta-information
================

exname: vuelo_acrobatico(single-choice)
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
exsection: Interpretación de gráficas
