```{r inicio, include=FALSE}
library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = ""
)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
# Definición de variables para el problema
# Para este problema, podemos variar los tiempos de las diferentes fases
# del vuelo

# Vector con posibles valores para los tiempos (en segundos)
# Tiempo de aumento de altura (25-35 seg)
tiempos_aumento <- sample(seq(25, 35, by = 5), 1)
# Tiempo de vuelo horizontal (antes pirueta + giro) (18-32 seg)
tiempos_horizontal <- sample(seq(18, 32, by = 2), 1)
# Tiempo de aterrizaje (20-30 seg)
tiempos_aterrizaje <- sample(seq(20, 30, by = 5), 1)

# Alturas máximas posibles (en metros)
altura_maxima <- sample(seq(100, 200, by = 20), 1)

# Definir variables para mostrar en el texto del problema
tiempo_total <- tiempos_aumento + tiempos_horizontal + tiempos_aterrizaje
```

Question
========

En una entrevista para una cadena de televisión, un piloto de un avión de acrobacias comenta en detalle cómo fue su presentación:

"Durante los primeros `r tiempos_aumento` segundos aumenté la altura de manera constante hasta alcanzar la altura máxima. Después mantuve la misma altura durante `r tiempos_horizontal` segundos mientras realizaba diversas maniobras de exhibición. Para finalizar, aterricé reduciendo la altura de manera constante durante `r tiempos_aterrizaje` segundos".

¿Cuál de las siguientes gráficas representa de forma CORRECTA la información dada por el piloto?

Answerlist
----------
-
<br/>
```{python GraficoA, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", fig.cap=""}
import matplotlib.pyplot as plt
import numpy as np

# Configuración para eliminar etiquetas
plt.rcParams['figure.figsize'] = (8, 5)
plt.rcParams['figure.titlesize'] = 0

# Obtener variables de R
tiempo_aumento = r.tiempos_aumento
tiempo_horizontal = r.tiempos_horizontal
tiempo_aterrizaje = r.tiempos_aterrizaje
altura_max = r.altura_maxima

# Definir puntos clave para el gráfico A (CORRECTO - Ahora coincide con la descripción modificada)
# Fase 1: Aumento constante de altura
x1 = np.array([0, tiempo_aumento])
y1 = np.array([0, altura_max])

# Fase 2: Mantener altura durante las maniobras
x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_horizontal])
y2 = np.array([altura_max, altura_max])

# Fase 3: Aterrizaje (descenso constante)
x3 = np.array([tiempo_aumento + tiempo_horizontal, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje])
y3 = np.array([altura_max, 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, 'b-', linewidth=2)
ax.plot(x2, y2, 'b-', linewidth=2)
ax.plot(x3, y3, 'b-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje + 5)
ax.set_ylim(0, altura_max * 1.1)

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.show()
```

-
<br/>
```{python GraficoB, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", fig.cap=""}
# Gráfico B (INCORRECTO - Con pico más alto durante las maniobras)
import matplotlib.pyplot as plt
import numpy as np

# Configuración para eliminar etiquetas
plt.rcParams['figure.figsize'] = (8, 5)
plt.rcParams['figure.titlesize'] = 0

# Definir puntos clave para el gráfico B
# Fase 1: Aumento constante de altura
x1 = np.array([0, tiempo_aumento])
y1 = np.array([0, altura_max])

# Fase 2: Maniobras (con pico incorrecto)
x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_horizontal/2, tiempo_aumento + tiempo_horizontal])
y2 = np.array([altura_max, altura_max*1.5, altura_max])  # Pico más alto (incorrecto)

# Fase 3: Aterrizaje (descenso constante)
x3 = np.array([tiempo_aumento + tiempo_horizontal, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje])
y3 = np.array([altura_max, 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, 'r-', linewidth=2)
ax.plot(x2, y2, 'r-', linewidth=2)
ax.plot(x3, y3, 'r-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje + 5)
ax.set_ylim(0, altura_max * 1.8)

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.show()
```

-
<br/>
```{python GraficoC, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", fig.cap=""}
# Gráfico C (INCORRECTO - Muestra descenso durante las maniobras)
import matplotlib.pyplot as plt
import numpy as np

# Configuración para eliminar etiquetas
plt.rcParams['figure.figsize'] = (8, 5)
plt.rcParams['figure.titlesize'] = 0

# Definir puntos clave para el gráfico C
# Fase 1: Aumento constante de altura
x1 = np.array([0, tiempo_aumento])
y1 = np.array([0, altura_max])

# Fase 2: Descenso constante durante maniobras (incorrecto)
x2 = np.array([tiempo_aumento, tiempo_aumento + tiempo_horizontal/3])
y2 = np.array([altura_max, altura_max/2])

# Fase 3: Altura constante (parte de las maniobras)
x3 = np.array([tiempo_aumento + tiempo_horizontal/3, tiempo_aumento + tiempo_horizontal])
y3 = np.array([altura_max/2, altura_max/2])

# Fase 4: Aterrizaje (descenso constante)
x4 = np.array([tiempo_aumento + tiempo_horizontal, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje])
y4 = np.array([altura_max/2, 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, 'g-', linewidth=2)
ax.plot(x2, y2, 'g-', linewidth=2)
ax.plot(x3, y3, 'g-', linewidth=2)
ax.plot(x4, y4, 'g-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje + 5)
ax.set_ylim(0, altura_max * 1.1)

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.show()
```

-
<br/>
```{python GraficoD, echo=FALSE, message=FALSE, comment = '', warning=FALSE, results="hide", fig.cap=""}
# Gráfico D (INCORRECTO - Con oscilaciones durante las maniobras)
import matplotlib.pyplot as plt
import numpy as np

# Configuración para eliminar etiquetas
plt.rcParams['figure.figsize'] = (8, 5)
plt.rcParams['figure.titlesize'] = 0

# Definir puntos clave para el gráfico D
# Fase 1: Aumento constante de altura
x1 = np.array([0, tiempo_aumento])
y1 = np.array([0, altura_max])

# Fase 2: Maniobras con oscilaciones (incorrecto)
t = np.linspace(tiempo_aumento, tiempo_aumento + tiempo_horizontal, 100)
oscilacion = altura_max * 0.1 * np.sin(10 * (t - tiempo_aumento))
altura_base = np.ones_like(t) * altura_max
x2 = t
y2 = altura_base + oscilacion

# Fase 3: Aterrizaje (descenso constante)
x3 = np.array([tiempo_aumento + tiempo_horizontal, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje])
y3 = np.array([altura_max, 0])

# Crear figura
fig = plt.figure()
ax = fig.add_subplot(111)

# Graficar cada segmento
ax.plot(x1, y1, 'r-', linewidth=2)
ax.plot(x2, y2, 'r-', linewidth=2)
ax.plot(x3, y3, 'r-', linewidth=2)

# Configuración del gráfico
ax.set_xlabel('Segundos', fontweight='bold')
ax.set_ylabel('Altura (m)', fontweight='bold')
ax.grid(True, linestyle='--', alpha=0.7)
ax.set_xlim(0, tiempo_aumento + tiempo_horizontal + tiempo_aterrizaje + 5)
ax.set_ylim(0, altura_max * 1.2)

# Mostrar gráfico
plt.subplots_adjust(top=1, bottom=0.1, left=0.1, right=0.9)
plt.show()
```

Solution
========

La gráfica correcta es la que representa adecuadamente las tres fases del vuelo acrobático descritas por el piloto:

1. Durante los primeros `r tiempos_aumento` segundos: aumento constante de altura (línea ascendente).
2. Mantenimiento de la misma altura durante `r tiempos_horizontal` segundos mientras realizaba maniobras de exhibición (línea horizontal).
3. Aterrizaje con reducción constante de altura durante `r tiempos_aterrizaje` segundos (línea descendente final).

Esta gráfica es la que tiene color azul y muestra exactamente las tres fases descritas, sin elementos adicionales.

Las demás opciones contienen elementos incorrectos:

- Una gráfica muestra un pico de altura durante las maniobras, pero el piloto mencionó que mantuvo la misma altura.
- Otra gráfica muestra un descenso durante las maniobras y luego mantiene una altura constante menor, lo cual contradice la descripción del piloto.
- Otra gráfica muestra oscilaciones en la altura durante las maniobras, cuando el piloto especificó que mantuvo la misma altura.

```{r version diversity test, echo = FALSE, results = "hide"}
# Prueba de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
  # Generar y almacenar 1000 versiones
  versiones <- list()
  for (i in 1:1000) {
    # Generar las variables
    tiempo_aumento_test <- sample(seq(25, 35, by = 5), 1)
    tiempo_horizontal_test <- sample(seq(18, 32, by = 2), 1)
    tiempo_aterrizaje_test <- sample(seq(20, 30, by = 5), 1)
    altura_maxima_test <- sample(seq(100, 200, by = 20), 1)

    # Combine the variables into a single string
    version_string <- paste(tiempo_aumento_test, tiempo_horizontal_test,
                            tiempo_aterrizaje_test, altura_maxima_test, collapse = "-")

    # Create a hash of the data
    versiones[[i]] <- digest::digest(version_string)
  }

  # Verificar al menos 100 versiones únicas
  n_versiones_unicas <- length(unique(versiones))
  expect_true(n_versiones_unicas >= 100,
              info = paste("Solo se generaron", n_versiones_unicas,
                           "versiones únicas. Se requieren al menos 100."))
})
```

Meta-information
================

exname: vuelo_acrobatico(single-choice)
extype: schoice
exsolution: 1000
exshuffle: TRUE
exsection: Interpretación de gráficas
