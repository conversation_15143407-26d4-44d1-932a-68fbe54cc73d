```{r datos1, echo = FALSE, results = "hide"}
knitr::opts_chunk$set(echo = FALSE)
##opts_chunk$set(fig.cap="")
options(tinytex.verbose = TRUE)
library(exams)
typ <- match_exams_device()


med1<-sample(6:10,1)
med1
medp1<-med1*1.5
medp1
medp2<-med1*2.5
medp2
medp3<-med1*3.5
medp3
med2<-sample(medp1:3+medp1,1)
med2
med3<-sample(medp2:3+medp2,1)
med3
med4<-sample(medp3:3+medp3,1)
med4

image01<-'
\\begin{tikzpicture}[x={1mm}, y={1mm}, z={(90:1mm)}, >=latex]
	\\fill[green!15, draw=black] (0, 0, 0) -- (15, 0, 0) -- (15, 21, 0) node (A) {} -- (0, 21, 0) node (B) {} -- cycle;
	\\fill[orange!25, draw=black] (0, 0, 0) node (O) {} -- (3, 0, {-3 * sqrt(3)}) node (F) {} -- (3, 21, {-3 * sqrt(3)}) node (C) {} -- (0, 21, 0) -- cycle;
	\\fill[orange!50, draw=black] (3, 0, {-3 * sqrt(3)}) -- (11, 0, {-3 * sqrt(3)}) -- (11, 21, {-3 * sqrt(3)}) node (D) {} -- (3, 21, {-3 * sqrt(3)}) -- cycle;
	\\fill[orange!25, draw=black] (11, 0, {-3 * sqrt(3)}) -- (15, 0, 0) node (E) {} -- (15, 21, 0) -- (11, 21, {-3 * sqrt(3)}) -- cycle;
	\\begin{scope}[gray!90]
		\\draw[<->] ($(O) + (-1, 0, 0)$) -- ($(F) + (-1, 0, 0)$) node [midway, left] {\\tiny %s cm};
		\\draw[<->] ($(C) + (0, -1, 0)$) -- ($(D) + (0, -1, 0)$) node [midway, below] {\\tiny %s cm};
		\\draw[<->] ($(A) + (0, 1, 0)$) -- ($(B) + (0, 1, 0)$) node [midway, above] {\\tiny %s cm};
		\\draw[<->] ($(A) + (1, 0, 0)$) -- ($(E) + (1, 0, 0)$) node [midway, right] {\\tiny %s cm};
	\\end{scope}
\\end{tikzpicture}
'
image01 <- sprintf(image01, med1,med2,med3,med4)

image02<-'
\\begin{tikzpicture}[x={5mm}, y={5mm}, z={5mm}, >=latex]
		\\draw[dashed] (0, 0, 0) coordinate (E) -- (0, 0, 8) -- (18, 0, 8);
		\\draw[dashed] (0, 0, 8) -- (0, 6, 8) coordinate(D);
		\\draw[fill=green, opacity=.7] (E) -- (18, 0, 0) coordinate (A) -- (18, 6, 0) -- (0, 6, 0) coordinate (B) -- cycle;
		\\draw[fill=green, opacity=.7] (A) -- (18, 0, 8) -- (18, 6, 8) coordinate (C) -- (18, 6, 0) -- cycle;
		\\draw[fill=green, opacity=.25] (B) -- (18, 6, 0) -- (C) -- (D) -- cycle;
		\\draw[<->] (0, -.5, 0) -- (18, -.5, 0) node[midway, fill=white] {18 cm};
		\\draw pic["$23.96^o$", angle eccentricity=1.3, draw, angle radius=2cm, red] {angle = D--C--B};
		\\draw[red] (C) -- (B);
		\\draw pic["$18.43^o$", angle eccentricity=1.3, draw, angle radius=2cm, red] {angle = B--A--E};
		\\draw[red] (A) -- (B);
	\\end{tikzpicture}
'
questions<-solutions<-explanations<-NULL
  questions[1]<-paste("Gráfico 1.")
  solutions[1]<-TRUE
  explanations[1]<-"."

  questions[2]<-paste("Gráfico 2.")
  solutions[2]<-FALSE
  explanations[2]<-"."

  questions[3]<-paste("Gráfico 3.")
  solutions[3]<-FALSE
  explanations[3]<-"."

  questions[4]<-paste("Gráfico 4.")
  solutions[4]<-FALSE
  explanations[4]<-"."

  orden<-sample(1:4)
  questions<-questions[orden]
  solutions<-solutions[orden]
  explanations<-explanations[orden]
```

Question
========
¿Cuál de las siguientes figuras representa bla, bla, bla..?

Answerlist
----------

* 
```{r grafica01, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image01, name = "grafiko1", markup = "markdown",format = typ,library = c("babel","calc","3d"),width = "6cm")
```
* 
```{r grafica02, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image02, name = "grafiko2", markup = "markdown",format = typ, library = c("3d", "babel", "angles"), packages=c("tikz-cd"),width = "8cm")
```
* 
```{r grafica03, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image01, name = "grafiko3", markup = "markdown",format = typ,library = c("babel","calc","3d"),width = "6cm")
```
* 
```{r grafica04, warning=FALSE,echo = FALSE, results = "asis"}
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 
include_tikz(image02, name = "grafiko4", markup = "markdown",format = typ, library = c("3d", "babel", "angles"), packages=c("tikz-cd"),width = "8cm")
```


Solution
========
```{r solutionlist, echo = FALSE, results = "asis"}
library(exams)
answerlist(ifelse(solutions, "True", "False"), markup = "markdown")
```

Meta-information
================
exname:3DTikZ(single-choice)
extype:schoice
exsolution:`r mchoice2string(solutions)`