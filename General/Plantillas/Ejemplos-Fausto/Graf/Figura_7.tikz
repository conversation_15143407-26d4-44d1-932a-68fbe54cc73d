﻿\usetikzlibrary{3d, calc, babel}

\begin{tikzpicture}[x={(-20:5mm)}, y={5mm}, z={(15:5mm)}, >=latex]
    \begin{scope}[canvas is xz plane at y=0]
        \draw[dashed] (4.2, 0) ++(120:4.2) -- ++(180:4.2) -- ++(240:4.2) -- +(300:4.2);
        \draw (-4.2, 0) ++(300:4.2) -- ++(0:4.2) -- ++(60:4.2) -- +(120:4.2);
    \end{scope}

    \begin{scope}[canvas is xz plane at y=5]
        \draw (4.2, 0) ++(120:4.2) -- ++(180:4.2) -- ++(240:4.2) -- +(300:4.2);
        \draw (-4.2, 0) ++(300:4.2) -- ++(0:4.2) -- ++(60:4.2) -- +(120:4.2);
    \end{scope}

	\draw[dashed] (-4.2, 0, 0) -- (-4.2, 5, 0);
   \draw[dashed] ({4.2*cos(120)}, 0, {4.2*sin(120)}) -- ({4.2*cos(120)}, 5, {4.2*sin(120)});
	\draw (4.2, 0, 0) -- (4.2, 5, 0);
   \draw ({4.2*cos(60)}, 0, {4.2*sin(60)}) -- ({4.2*cos(60)}, 5, {4.2*sin(60)});
   \draw ({4.2*cos(240)}, 0, {4.2*sin(240)}) -- ({4.2*cos(240)}, 5, {4.2*sin(240)});	
   \draw ({4.2*cos(300)}, 0, {4.2*sin(300)}) -- ({4.2*cos(300)}, 5, {4.2*sin(300)});	
	\draw[<->] ({4.5*cos(60)}, 0, {4.5*sin(60)}) -- ({4.5*cos(60)}, 5, {4.5*sin(60)}) node[midway, right] {5 cm};
	\draw[<->] ({4.5*cos(300)}, -.5, {4.5*sin(300)}) -- ({4.5*cos(0)}, -.5, {4.5*sin(0)}) node[midway, below] {4.2 cm};
	\draw[<->] (0, 0, 0) -- ({3.65*cos(30)}, 0, {3.65*sin(30)}) node[midway, above] {3.64 cm};
\end{tikzpicture}