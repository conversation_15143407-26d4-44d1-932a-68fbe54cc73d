% Pgfplots demo
% Author: <PERSON>
% Source: Pgfplots manual:
% http://www.ctan.org/tex-archive/help/Catalogue/entries/pgfplots.html
\documentclass{article}

\usepackage{tikz}
\usepackage{tkz-euclide}
\usepackage{pgfplots}
\usepackage{verbatim}

\begin{document}
\begin{tikzpicture}[scale=1.25]
\tkzDefPoint(0,0){A}
\tkzDefPoint(7,0){B}
\tkzDefPoint(1.5,2.9){C}
\tkzDefPoint(1.5,0){D}
\tkzDrawPolygon(A,B,C)
\tkzMarkRightAngle[fill=blue!40](A,C,B)
\tkzMarkRightAngle[fill=blue!40](C,D,B)
\tkzSetUpLine[line width=0.5pt,color=blue]
%\tkzLabelSegment[swap](D,C){$x$}
\tkzDrawLine[altitude](A,C,B)
    \draw[|<->|,>=latex] (1.5, -0.1) -- (7, -0.1) node[midway, below] {$x$};
    \draw[|<->|,>=latex] (0, -0.1) -- (1.5, -0.1) node[midway, below] {\ 4 cm};
    \draw(C) -- (D) node[midway, right] {\ 8 cm};
\end{tikzpicture}
\end{document}