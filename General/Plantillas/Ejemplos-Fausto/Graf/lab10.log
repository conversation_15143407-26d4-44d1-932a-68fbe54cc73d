This is LuaHBTeX, Version 1.12.0 (TeX Live 2020)  (format=lualatex 2021.6.1)  6 JUL 2021 22:01
 system commands enabled.
**lab10.tex
(./lab10.tex
LaTeX2e <2020-02-02> patch level 5
Lua module: luaotfload-main 2020-05-06 3.14 luaotfload entry point
Lua module: luaotfload-init 2020-05-06 3.14 luaotfload submodule / initializatio
n
Lua module: lualibs 2020-02-02 2.70 ConTeXt Lua standard libraries.
Lua module: lualibs-extended 2020-02-02 2.70 ConTeXt Lua libraries -- extended c
ollection.
Lua module: luaotfload-log 2020-05-06 3.14 luaotfload submodule / logging
Lua module: luaotfload-parsers 2020-05-06 3.14 luaotfload submodule / filelist
Lua module: luaotfload-configuration 2020-05-06 3.14 luaotfload submodule / conf
ig file reader
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2020/texmf-va
r/luatex-cache/generic-dev/names".
luaotfload | init : Loading fontloader "fontloader-2020-05-06.lua" from kpse-res
olved path "/usr/share/texlive/texmf-dist/tex/luatex/luaotfload/fontloader-2020-
05-06.lua".
Lua-only attribute luaotfload@noligature = 2
Lua-only attribute luaotfload@syllabe = 3
luaotfload | init : Context OpenType loader version 0x1.8e353f7ced917p+1
Lua module: luaotfload-fallback 2020-05-06 3.14 luaotfload submodule / fallback
Lua module: luaotfload-multiscript 2020-05-06 3.14 luaotfload submodule / multis
cript
Lua module: luaotfload-script 2020-05-06 3.14 luaotfload submodule / Script help
ers
Inserting `luaotfload.node_processor' at position 1 in `pre_linebreak_filter'.
Inserting `luaotfload.node_processor' at position 1 in `hpack_filter'.
Lua module: luaotfload-loaders 2020-05-06 3.14 luaotfload submodule / callback h
andling
Inserting `luaotfload.define_font' at position 1 in `define_font'.
Lua module: luaotfload-database 2020-05-06 3.14 luaotfload submodule / database
Lua module: luaotfload-unicode 2020-05-06 3.14 luaotfload submodule / Unicode he
lpers
Lua module: luaotfload-colors 2020-05-06 3.14 luaotfload submodule / color
Lua-only attribute luaotfload_color_attribute = 4
Lua module: luaotfload-resolvers 2020-05-06 3.14 luaotfload submodule / resolver
s
luaotfload | conf : Root cache directory is "/home/<USER>/.texlive2020/texmf-va
r/luatex-cache/generic-dev/names".
Lua module: luaotfload-features 2020-05-06 3.14 luaotfload submodule / features
Lua module: luaotfload-harf-define 2020-05-06 3.14 luaotfload submodule / databa
se
Inserting `luaotfload.harf.strip_prefix' at position 1 in `find_opentype_file'.
Inserting `luaotfload.harf.strip_prefix' at position 1 in `find_truetype_file'.
Lua module: luaotfload-harf-plug 2020-05-06 3.14 luaotfload submodule / database
Inserting `Harf pre_output_filter callback' at position 1 in `pre_output_filter'
.
Inserting `Harf wrapup_run callback' at position 1 in `wrapup_run'.
Inserting `Harf finish_pdffile callback' at position 1 in `finish_pdffile'.
Inserting `Harf glyph_info callback' at position 1 in `glyph_info'.
Lua module: luaotfload-letterspace 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-embolden 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-notdef 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-suppress 2020-05-06 3.14 luaotfload submodule / suppress
Lua module: luaotfload-szss 2020-05-06 3.14 luaotfload submodule / color
Lua module: luaotfload-auxiliary 2020-05-06 3.14 luaotfload submodule / auxiliar
y functions
Inserting `luaotfload.aux.set_sscale_dimens' at position 1 in `luaotfload.patch_
font'.
Inserting `luaotfload.aux.set_font_index' at position 2 in `luaotfload.patch_fon
t'.
Inserting `luaotfload.aux.patch_cambria_domh' at position 3 in `luaotfload.patch
_font'.
Inserting `luaotfload.aux.fixup_fontdata' at position 1 in `luaotfload.patch_fon
t_unsafe'.
Inserting `luaotfload.aux.set_capheight' at position 4 in `luaotfload.patch_font
'.
Inserting `luaotfload.aux.set_xheight' at position 5 in `luaotfload.patch_font'.
Lua module: luaotfload-tounicode 2020-05-06 3.14 luaotfload submodule / tounicod
e
Inserting `luaotfload.rewrite_fontname' at position 6 in `luaotfload.patch_font'
. L3 programming layer <2020-04-06>
(/usr/share/texlive/texmf-dist/tex/latex/base/article.cls
Document Class: article 2019/12/20 v1.4l Standard LaTeX document class
(/usr/share/texlive/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2019/12/20 v1.4l Standard LaTeX file (size option)
luaotfload | db : Font names database loaded from /home/<USER>/.texlive2020/tex
mf-var/luatex-cache/generic-dev/names/luaotfload-names.luc)
\c@part=\count163
\c@section=\count164
\c@subsection=\count165
\c@subsubsection=\count166
\c@paragraph=\count167
\c@subparagraph=\count168
\c@figure=\count169
\c@table=\count170
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen134
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/frontendlayer/tikz.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgf.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfrcs.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common.tex
\pgfutil@everybye=\toks15
\pgfutil@tempdima=\dimen135
\pgfutil@tempdimb=\dimen136

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-common-lists.t
ex)) (/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfutil-latex.def
\pgfutil@abb=\box45
(/usr/share/texlive/texmf-dist/tex/latex/ms/everyshi.sty
Package: everyshi 2001/05/15 v3.00 EveryShipout Package (MS)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfrcs.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/pgf.revision.tex)
Package: pgfrcs 2020/01/08 v3.1.5b (3.1.5b)
))
Package: pgf 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/latex/pgf/basiclayer/pgfcore.sty
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2019/11/30 v1.2a Enhanced LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks16
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2019/11/30 v1.4a Standard LaTeX Graphics (DPC,SPQR)

(/usr/share/texlive/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
)
(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: luatex.def on input line 105.

(/usr/share/texlive/texmf-dist/tex/latex/graphics-def/luatex.def
File: luatex.def 2018/01/08 v1.0l Graphics/color driver for luatex
))
\Gin@req@height=\dimen137
\Gin@req@width=\dimen138
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/systemlayer/pgfsys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys.code.tex
Package: pgfsys 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks17
\pgfkeys@temptoks=\toks18

(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeysfiltered.code.t
ex
\pgfkeys@tmptoks=\toks19
))
\pgf@x=\dimen139
\pgf@y=\dimen140
\pgf@xa=\dimen141
\pgf@ya=\dimen142
\pgf@xb=\dimen143
\pgf@yb=\dimen144
\pgf@xc=\dimen145
\pgf@yc=\dimen146
\pgf@xd=\dimen147
\pgf@yd=\dimen148
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count171
\c@pgf@countb=\count172
\c@pgf@countc=\count173
\c@pgf@countd=\count174
\t@pgf@toka=\toks20
\t@pgf@tokb=\toks21
\t@pgf@tokc=\toks22
\pgf@sys@id@count=\count175
 (/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgf.cfg
File: pgf.cfg 2020/01/08 v3.1.5b (3.1.5b)
)
Driver file for pgf: pgfsys-luatex.def

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-luatex.def
File: pgfsys-luatex.def 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsys-common-pdf.de
f
File: pgfsys-common-pdf.def 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsyssoftpath.code.
tex
File: pgfsyssoftpath.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfsyssoftpath@smallbuffer@items=\count176
\pgfsyssoftpath@bigbuffer@items=\count177
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/systemlayer/pgfsysprotocol.code.
tex
File: pgfsysprotocol.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)

(/usr/share/texlive/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: luatex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1352.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcore.code.tex
Package: pgfcore 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathcalc.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathutil.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathparser.code.tex
\pgfmath@dimen=\dimen149
\pgfmath@count=\count178
\pgfmath@box=\box46
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.basic.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.trigonomet
ric.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.random.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.comparison
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.base.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.round.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.misc.code.
tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfunctions.integerari
thmetics.code.tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count179
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfint.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepoints.code.te
x
File: pgfcorepoints.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@picminx=\dimen150
\pgf@picmaxx=\dimen151
\pgf@picminy=\dimen152
\pgf@picmaxy=\dimen153
\pgf@pathminx=\dimen154
\pgf@pathmaxx=\dimen155
\pgf@pathminy=\dimen156
\pgf@pathmaxy=\dimen157
\pgf@xx=\dimen158
\pgf@xy=\dimen159
\pgf@yx=\dimen160
\pgf@yy=\dimen161
\pgf@zx=\dimen162
\pgf@zy=\dimen163
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathconstruct.
code.tex
File: pgfcorepathconstruct.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@path@lastx=\dimen164
\pgf@path@lasty=\dimen165
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathusage.code
.tex
File: pgfcorepathusage.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@shorten@end@additional=\dimen166
\pgf@shorten@start@additional=\dimen167
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorescopes.code.te
x
File: pgfcorescopes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfpic=\box47
\pgf@hbox=\box48
\pgf@layerbox@main=\box49
\pgf@picture@serial@count=\count180
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoregraphicstate.c
ode.tex
File: pgfcoregraphicstate.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgflinewidth=\dimen168
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransformation
s.code.tex
File: pgfcoretransformations.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@pt@x=\dimen169
\pgf@pt@y=\dimen170
\pgf@pt@temp=\dimen171
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreobjects.code.t
ex
File: pgfcoreobjects.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepathprocessing
.code.tex
File: pgfcorepathprocessing.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorearrows.code.te
x
File: pgfcorearrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfarrowsep=\dimen172
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@max=\dimen173
\pgf@sys@shading@range@num=\count181
\pgf@shadingcount=\count182
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoreexternal.code.
tex
File: pgfcoreexternal.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfexternal@startupbox=\box50
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorelayers.code.te
x
File: pgfcorelayers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcoretransparency.c
ode.tex
File: pgfcoretransparency.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorepatterns.code.
tex
File: pgfcorepatterns.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/basiclayer/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfnodeparttextbox=\box51
) (/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-0-65
.sty
Package: pgfcomp-version-0-65 2020/01/08 v3.1.5b (3.1.5b)
\pgf@nodesepstart=\dimen174
\pgf@nodesepend=\dimen175
)
(/usr/share/texlive/texmf-dist/tex/latex/pgf/compatibility/pgfcomp-version-1-18
.sty
Package: pgfcomp-version-1-18 2020/01/08 v3.1.5b (3.1.5b)
)) (/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgffor.sty
(/usr/share/texlive/texmf-dist/tex/latex/pgf/utilities/pgfkeys.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgfkeys.code.tex))
(/usr/share/texlive/texmf-dist/tex/latex/pgf/math/pgfmath.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/utilities/pgffor.code.tex
Package: pgffor 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/math/pgfmath.code.tex)
\pgffor@iter=\dimen176
\pgffor@skip=\dimen177
\pgffor@stack=\toks26
\pgffor@toks=\toks27
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/tikz.code.tex
Package: tikz 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplothandlers
.code.tex
File: pgflibraryplothandlers.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgf@plot@mark@count=\count183
\pgfplotmarksize=\dimen178
)
\tikz@lastx=\dimen179
\tikz@lasty=\dimen180
\tikz@lastxsaved=\dimen181
\tikz@lastysaved=\dimen182
\tikz@lastmovetox=\dimen183
\tikz@lastmovetoy=\dimen184
\tikzleveldistance=\dimen185
\tikzsiblingdistance=\dimen186
\tikz@figbox=\box52
\tikz@figbox@bg=\box53
\tikz@tempbox=\box54
\tikz@tempbox@bg=\box55
\tikztreelevel=\count184
\tikznumberofchildren=\count185
\tikznumberofcurrentchild=\count186
\tikz@fig@count=\count187

(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfmatrixcurrentrow=\count188
\pgfmatrixcurrentcolumn=\count189
\pgf@matrix@numberofcolumns=\count190
)
\tikz@expandcount=\count191

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-euclide.sty
2020/03/18 3.06c tkz-euclide.sty
Package: tkz-euclide 2020/03/18 3.06c for euclidan geometry 
(/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-base.sty
2020/03/18 3.06c tkz-base.sty
Package: tkz-base 2020/03/18 3.06c tkz-base

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryangles.code.tex
File: tikzlibraryangles.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryarrows.code.tex
File: tikzlibraryarrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.code.
tex
File: pgflibraryarrows.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\arrowsize=\dimen187
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryarrows.meta.
code.tex
File: pgflibraryarrows.meta.code.tex 2020/01/08 v3.1.5b (3.1.5b)
\pgfarrowinset=\dimen188
\pgfarrowlength=\dimen189
\pgfarrowwidth=\dimen190
\pgfarrowlinewidth=\dimen191
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarybabel.code.tex
File: tikzlibrarybabel.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarycalc.code.tex
File: tikzlibrarycalc.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/modules/pgfmoduledecorations.cod
e.tex
\pgfdecoratedcompleteddistance=\dimen192
\pgfdecoratedremainingdistance=\dimen193
\pgfdecoratedinputsegmentcompleteddistance=\dimen194
\pgfdecoratedinputsegmentremainingdistance=\dimen195
\pgf@decorate@distancetomove=\dimen196
\pgf@decorate@repeatstate=\count192
\pgfdecorationsegmentamplitude=\dimen197
\pgfdecorationsegmentlength=\dimen198
)
\tikz@lib@dec@box=\box56
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.markings.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.markings.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.pathreplacing.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.pathreplacing.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.shapes.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.shapes.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.text.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.text.code.tex
\pgf@lib@dec@text@box=\box57
)
\tikz@lib@dec@te@box=\box58
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarydecorations.pathmorphing.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/decorations/pgflibrary
decorations.pathmorphing.code.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryintersections.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryintersection
s.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryfpu.code.tex
)
\pgf@intersect@solutions=\count193
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypatterns.code.tex
File: tikzlibrarypatterns.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibrarypatterns.cod
e.tex
File: pgflibrarypatterns.code.tex 2020/01/08 v3.1.5b (3.1.5b)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryplotmarks.code.tex
File: tikzlibraryplotmarks.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryplotmarks.co
de.tex
File: pgflibraryplotmarks.code.tex 2020/01/08 v3.1.5b (3.1.5b)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarypositioning.code.tex
File: tikzlibrarypositioning.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryquotes.code.tex
File: tikzlibraryquotes.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshadows.code.tex
File: tikzlibraryshadows.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryfadings.code.tex
File: tikzlibraryfadings.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/pgflibraryfadings.code
.tex
File: pgflibraryfadings.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibraryshapes.misc.code.tex
File: tikzlibraryshapes.misc.code.tex 2020/01/08 v3.1.5b (3.1.5b)

(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/shapes/pgflibraryshape
s.misc.code.tex
File: pgflibraryshapes.misc.code.tex 2020/01/08 v3.1.5b (3.1.5b)
))
(/usr/share/texlive/texmf-dist/tex/generic/pgf/frontendlayer/tikz/libraries/tik
zlibrarythrough.code.tex
File: tikzlibrarythrough.code.tex 2020/01/08 v3.1.5b (3.1.5b)
)
(/usr/share/texlive/texmf-dist/tex/latex/numprint/numprint.sty
Package: numprint 2012/08/20 v1.39 Print numbers (HH)

(/usr/share/texlive/texmf-dist/tex/latex/tools/array.sty
Package: array 2019/08/31 v2.4l Tabular extension package (FMi)
\col@sep=\dimen199
\ar@mcellbox=\box59
\extrarowheight=\dimen256
\NC@list=\toks28
\extratabsurround=\skip49
\backup@length=\skip50
\ar@cellbox=\box60
)
\c@nprt@mantissa@digitsbefore=\count194
\c@nprt@mantissa@digitsafter=\count195
\c@nprt@exponent@digitsbefore=\count196
\c@nprt@exponent@digitsafter=\count197
\nprt@digitwidth=\skip51
\nprt@sepwidth=\skip52
\nprt@decimalwidth=\skip53
\nprt@blockwidth=\skip54
\nprt@digittoks=\toks29
\nprt@pretoks=\toks30
\nprt@posttoks=\toks31
\nprt@thisdigit=\count198
\nprt@curpos=\count199
\nprt@rndpos=\count266
\c@nprt@digitsfirstblock=\count267
\c@nprt@blockcnt=\count268
\c@nprt@cntprint=\count269

No configuration file `numprint.cfg' found.)
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xfp/xfp.sty
(/usr/share/texlive/texmf-dist/tex/latex/l3kernel/expl3.sty
Package: expl3 2020-04-06 L3 programming layer (loader) 

(/usr/share/texlive/texmf-dist/tex/latex/l3backend/l3backend-pdfmode.def
File: l3backend-pdfmode.def 2020-03-12 L3 backend support: PDF mode
\l__kernel_color_stack_int=\count270
\l__pdf_internal_box=\box61
))
(/usr/share/texlive/texmf-dist/tex/latex/l3packages/xparse/xparse.sty
Package: xparse 2020-03-06 L3 Experimental document command parser
\l__xparse_current_arg_int=\count271
\g__xparse_grabber_int=\count272
\l__xparse_m_args_int=\count273
\l__xparse_v_nesting_int=\count274
)
Package: xfp 2020-03-06 L3 Floating point unit
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp.sty
Package: fp 1995/04/02

`Fixed Point Package', Version 0.8, April 2, 1995 (C) Michael Mehlich
(/usr/share/texlive/texmf-dist/tex/latex/fp/defpattern.sty
Package: defpattern 1994/10/12
\actioncount=\count275
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-basic.sty
Package: fp-basic 1996/05/13
\FP@xs=\count276
\FP@xia=\count277
\FP@xib=\count278
\FP@xfa=\count279
\FP@xfb=\count280
\FP@rega=\count281
\FP@regb=\count282
\FP@regs=\count283
\FP@times=\count284
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-addons.sty
Package: fp-addons 1995/03/15
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-snap.sty
Package: fp-snap 1995/04/05
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-exp.sty
Package: fp-exp 1995/04/03
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-trigo.sty
Package: fp-trigo 1995/04/14
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-pas.sty
Package: fp-pas 1994/08/29
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-random.sty
Package: fp-random 1995/02/23
\FPseed=\count285
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-eqn.sty
Package: fp-eqn 1995/04/03
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-upn.sty
Package: fp-upn 1996/10/21
)
(/usr/share/texlive/texmf-dist/tex/latex/fp/fp-eval.sty
Package: fp-eval 1995/04/03
))
\tkzRadius=\dimen257
\tkzLength=\dimen258
\tkz@radi=\dimen259
\tkz@ax=\dimen260
\tkz@ay=\dimen261
\tkz@bx=\dimen262
\tkz@by=\dimen263
\tkz@cx=\dimen264
\tkz@cy=\dimen265
\tkz@dx=\dimen266
\tkz@dy=\dimen267
\tkz@tax=\dimen268
\tkz@tay=\dimen269
\tkz@tbx=\dimen270
\tkz@tby=\dimen271
\tkz@tcx=\dimen272
\tkz@tcy=\dimen273
\tkz@tdx=\dimen274
\tkz@tdy=\dimen275
\tkz@cntmk=\count286

Local configuration file tkz-base.cfg found and used
(/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-base.cfg
2020/03/18 3.06c tkz-base.cfg
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-modules.tex
2020/03/18 3.06c tkz-tools-utilities.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-lib-marks.tex
2020/03/18 3.06c tkz-lib-symbols.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-base.tex
2020/03/18 3.06c tkz-tools-base.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-utilities.tex
2020/03/18 3.06c tkz-tools-utilities.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-tools-math.tex
2020/03/18 3.06c tkz-tools-math.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-text.tex
2020/03/18 3.06c tkz-tools-text.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-BB.tex
2020/03/18 3.06c tkz-obj-BB.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-arith.tex
2020/03/18 3.06c tkz-tools-arith.tex
File: tkz-tool-arith.tex tkz-tool-arith 3.02 c
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-print.tex
2020/03/18 3.06c tkz-tools-print.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-tools-misc.tex
2020/03/18 3.06c tkz-tools-misc.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-obj-axes.tex
2020/03/18 3.06c tkz-obj-axes.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-obj-grids.tex
2020/03/18 3.06c tkz-obj-grids.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-obj-marks.tex
2020/03/18 3.07c tkz-obj-marks.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-obj-points.tex
2020/03/18 3.06c tkz-obj-points.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-base/tkz-obj-rep.tex
2020/03/18 3.06c tkz-obj-rep.tex
))
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-tools-intersections.te
x
2020/03/18 3.06c tkz-tools-intersections.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-tools-angles.tex
2020/03/18 3.06c tkz-tools-angles.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-angles.tex
2020/03/18 3.06c tkz-tool-eu-angles.tex
\tkz@arcsize=\dimen276
\tkz@fillsize=\dimen277
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-arcs.tex
2020/03/23 3.06c tkz-obj-eu-arcs.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-compass.tex
2020/03/18 3.06c tkz-obj-eu-compass.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-circles.tex
2020/03/18 3.06c tkz-obj-eu-circles.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-draw-circles.te
x
2020/03/18 3.06c tkz-obj-eu-draw-circles.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-draw-lines.tex
2020/03/18 3.06c tkz-obj-eu-draw-lines.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-draw-polygons.t
ex
2020/03/18 3.06c tkz-obj-eu-polygons.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-draw-triangles.
tex) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-lines.tex
2020/03/18 3.06c tkz-obj-eu-lines.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points.tex
2020/03/18 3.06c tkz-obj-eu-points.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-by.tex
2020/03/18 3.06c tkz-tools-eu-points-by.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-rnd.tex
2020/03/18 3.06c tkz-obj-eu-points-rnd.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-points-with.tex
2020/03/18 3.06c tkz-obj-eu-points-with.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-polygons.tex
2020/03/18 3.06c tkz-obj-eu-polygons.tex
)
(/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-protractor.tex
2020/03/18 3.06c tkz-obj-eu-protractor.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-sectors.tex
2020/03/18 3.06c tkz-obj-eu-sectors.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-show.tex
2020/03/18 3.06c tkz-obj-eu-show.tex
) (/usr/share/texlive/texmf-dist/tex/latex/tkz-euclide/tkz-obj-eu-triangles.tex
2020/03/18 3.06c tkz-obj-eu-triangles.tex
)) (/usr/share/texlive/texmf-dist/tex/latex/pgfplots/pgfplots.sty
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.revision.tex)
Package: pgfplots 2020/02/29 v1.17 Data Visualization (1.17)

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotscore.code.tex
Package pgfplots info on input line 124: Initializing with LUA version Lua 5.3
\pgfplots@luabackend@table=\catcodetable5
\t@pgfplots@toka=\toks32
\t@pgfplots@tokb=\toks33
\t@pgfplots@tokc=\toks34
\pgfplots@tmpa=\dimen278
\c@pgfplots@coordindex=\count287
\c@pgfplots@scanlineindex=\count288

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/sys/pgfplotssysgeneric.code
.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/libs/pgfplotslibrary.code.t
ex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/oldpgfcompatib/pgfplotsoldp
gfsupp_loader.code.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgf/libraries/luamath/pgflibraryluam
ath.code.tex)
Package pgfplots info on input line 290: luamath library shipped with pgf 3.1.5b
 is up-to-date
)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.code.tex
Package pgfplots info on input line 82: Found new luatex: initializing lua comma
nds instead of write18 (shell-escape)

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotslists
tructure.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotslists
tructureext.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsarray
.code.tex
\c@pgfplotsarray@tmp=\count289
)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsmatri
x.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/numtable/pgfplotstableshare
d.code.tex
\c@pgfplotstable@counta=\count290
\t@pgfplotstable@a=\toks35
)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/liststructure/pgfplotsdeque
.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.code.te
x
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsbinary.data.co
de.tex))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotsutil.verb.code
.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/libs/pgflibrarypgfplots.sur
fshading.code.tex
\c@pgfplotslibrarysurf@no=\count291

(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/sys/pgflibrarypgfplots.surf
shading.pgfsys-luatex.def)))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotscolormap.code.
tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/util/pgfplotscolor.code.tex
))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsstackedplots.code.t
ex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsplothandlers.code.t
ex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplothandler.cod
e.tex
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsmeshplotimage.code.
tex)))
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.scaling.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotscoordprocessing.cod
e.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.errorbars.code.tex
) (/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.markers.code.tex
) (/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplotsticks.code.tex)
(/usr/share/texlive/texmf-dist/tex/generic/pgfplots/pgfplots.paths.code.tex)
\pgfplots@numplots=\count292
\pgfplots@xmin@reg=\dimen279
\pgfplots@xmax@reg=\dimen280
\pgfplots@ymin@reg=\dimen281
\pgfplots@ymax@reg=\dimen282
\pgfplots@zmin@reg=\dimen283
\pgfplots@zmax@reg=\dimen284
))
(/usr/share/texlive/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2019/11/10 v1.5r LaTeX2e package for verbatim enhancements
\every@verbatim=\toks36
\verbatim@line=\toks37
\verbatim@in@stream=\read3
) (./lab10.aux)
\openout1 = lab10.aux

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 12.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 12
.

(/usr/share/texlive/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2019/12/16 v2.5j Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 12.
LaTeX Font Info:    ... okay on input line 12.

ABD: EveryShipout initializing macros
(/usr/share/texlive/texmf-dist/tex/context/base/mkii/supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count293
\scratchdimen=\dimen285
\scratchbox=\box62
\nofMPsegments=\count294
\nofMParguments=\count295
\everyMPshowfont=\toks38
\MPscratchCnt=\count296
\MPscratchDim=\dimen286
\MPnumerator=\count297
\makeMPintoPDFobject=\count298
\everyMPtoPDFconversion=\toks39
) (/usr/share/texlive/texmf-dist/tex/latex/epstopdf-pkg/epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 48
5.

(/usr/share/texlive/texmf-dist/tex/latex/latexconfig/epstopdf-sys.cfg
File: epstopdf-sys.cfg 2010/07/13 v1.3 Configuration of (r)epstopdf for TeX Live

))

Package pgfplots Warning: running in backwards compatibility mode (unsuitable ti
ck labels; missing features). Consider writing \pgfplotsset{compat=1.17} into yo
ur preamble.
 on input line 12.

LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <7> on input line 1.
LaTeX Font Info:    External font `cmex10' loaded for size
(Font)              <5> on input line 1.
[1

{/usr/share/texlive/texmf-dist/fonts/map/pdftex/updmap/pdftex.map}]
(./lab10.aux))

Here is how much of LuaTeX's memory you used:
 27943 strings out of 480991
 100000,1373583 words of node,token memory allocated
 938 words of node memory still in use:
   19 hlist, 1 vlist, 9 rule, 2 glue, 3 kern, 1 glyph, 53 attribute, 49 glue_spe
c, 53 attribute_list, 2 write, 16 pdf_literal, 16 pdf_colorstack nodes
   avail lists: 2:517,3:124,4:7,5:22,6:1,7:222,8:1,9:53
 44323 multiletter control sequences out of 65536+600000
 35 fonts using 3880331 bytes
 63i,6n,98p,721b,830s stack positions out of 5000i,500n,10000p,200000b,100000s
</usr/share/texlive/texmf-dist/fonts/opentype/public/lm/lmroman10-regular.otf></
usr/share/texlive/texmf-dist/fonts/type1/public/amsfonts/cm/cmmi10.pfb>
Output written on lab10.pdf (1 page, 12787 bytes).

PDF statistics: 46 PDF objects out of 1000 (max. 8388607)
 22 compressed objects within 1 object stream
 0 named destinations out of 1000 (max. 131072)
 176 words of extra memory for PDF output out of 10000 (max. 100000000)

