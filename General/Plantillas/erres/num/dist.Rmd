```{r data generation, echo = FALSE, results = "hide"}
p <- c(sample(1:3, 1), sample(1:5, 1))
q <- c(sample(4:5, 1), sample(1:5, 1))
sol <- sqrt(sum((p - q)^2))
```

Question
========
What is the distance between the two points
$p = (`r p[1]`, `r p[2]`)$ and $q = (`r q[1]`, `r q[2]`)$
in a Cartesian coordinate system?

Solution
========
The distance $d$ of $p$ and $q$ is given by
$d^2 = (p_1 - q_1)^2 + (p_2 - q_2)^2$ (Pythagorean formula).

Hence $d = \sqrt{(p_1 - q_1)^2 + (p_2 - q_2)^2} =
  \sqrt{(`r p[1]` - `r q[1]`)^2 + (`r p[2]` - `r q[2]`)^2}
   = `r round(sol, digits = 3)`$.
\
```{r distplot, echo = FALSE, results = "hide", fig.path = "", fig.cap = ""}
par(mar = c(4, 4, 1, 1))
plot(0, type = "n", xlim = c(0, 6), ylim = c(0, 6), xlab = "x", ylab = "y")
grid(col = "slategray")
points(rbind(p, q), pch = 19)
text(rbind(p, q), c("p", "q"), pos = c(2, 4))
lines(rbind(p, q))
lines(c(p[1], p[1], q[1]), c(p[2], q[2], q[2]), lty = 2)
```

Meta-information
================
extype: num
exsolution: `r round(sol, digits = 3)`
exname: Euclidean distance
extol: 0.01
