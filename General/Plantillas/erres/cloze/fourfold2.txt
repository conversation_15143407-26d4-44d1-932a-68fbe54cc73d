```{r data generation, echo = FALSE, results = "hide"}
ok <- FALSE
while(!ok) {
  pe <- round(runif(1, 0.05, 0.15), digits = 2)
  per <- round(runif(1, 0.6, 0.8), digits = 2)
  pnenr <- round(runif(1, 0.6, 0.8), digits = 2)

  prob1 <- pe * per
  prob2 <- pe * (1 - per)
  prob3 <- (1 - pe) * (1 - pnenr)
  prob4 <- (1 - pe) * pnenr

  tab <- cbind(c(prob1, prob3), c(prob2, prob4))

  sol <- c(tab[1, 1] / sum(tab[, 1]), tab[1, 1] / sum(tab[1, ]),
    tab[2, 1] / sum(tab[2, ]), tab[1, 2] / sum(tab[1, ]))

  ok <- sum(tab) == 1 & all(tab > 0) & all(tab < 1)
}

tab2 <- cbind(rbind(tab, colSums(tab)), c(rowSums(tab), 1))
tab2 <- format(tab2 * 100, digits = 2, nsmall = 2)
tab2 <- gsub(" ", "", tab2, fixed = TRUE)

sol <- round(100 * c(tab, colSums(tab), rowSums(tab), sum(tab)), digits = 2)
lab <- c("E \\cap R", "\\overline{E} \\cap R", "E \\cap \\overline{R}", "\\overline{E} \\cap \\overline{R}",
  "R", "\\overline{R}", "E", "\\overline{E}", "\\Omega")
```


Question
========

An industry-leading company seeks a qualified candidate for a management position.
A management consultancy carries out an assessment center which concludes in making
a positive or negative recommendation for each candidate: From previous assessments they know that
of those candidates that are actually eligible for the position (event $E$) $`r per * 100`\%$
get a positive recommendation (event $R$). However, out of those candidates that are not eligible
$`r pnenr * 100`\%$ get a negative recommendation. Overall, they know that only
$`r pe * 100`\%$ of all job applicants are actually eligible.

What is the corresponding fourfold table of the joint probabilities? (Specify all entries in percent.)

|               | $R$           | $\overline{R}$ | sum           |
|:-------------:|:-------------:|:--------------:|:-------------:|
|$E$            | ##ANSWER1##\% | ##ANSWER3##\%  | ##ANSWER7##\% |
|$\overline{E}$ | ##ANSWER2##\% | ##ANSWER4##\%  | ##ANSWER8##\% |
|sum            | ##ANSWER5##\% | ##ANSWER6##\%  | ##ANSWER9##\% |

```{r questionlist, echo = FALSE, results = "asis"}
answerlist(rep("", length(sol)), markup = "markdown")
```


Solution
========

Using the information from the text, we can directly calculate the following joint probabilities:
$$
\begin{aligned}
  P(E \cap R) & =
    P(R | E) \cdot P(E) = `r per` \cdot `r pe` = `r prob1` = `r 100 * prob1`\%\\
  P(\overline{E} \cap \overline{R}) & =
    P(\overline{R} | \overline{E}) \cdot P(\overline{E}) = `r pnenr` \cdot `r 1 - pe` = `r prob4` = `r 100 * prob4`\%.
\end{aligned}
$$
The remaining probabilities can then be found by calculating sums and differences in the fourfold table:

|               | $R$                | $\overline{R}$     | sum                |
|:-------------:|:------------------:|:------------------:|:------------------:|
|$E$            | **`r tab2[1, 1]`** |  _`r tab2[1, 2]`_  | **`r tab2[1, 3]`** |
|$\overline{E}$ |  _`r tab2[2, 1]`_  | **`r tab2[2, 2]`** |  _`r tab2[2, 3]`_  |
|sum            |  _`r tab2[3, 1]`_  |  _`r tab2[3, 2]`_  | **`r tab2[3, 3]`** |


```{r solutionlist, echo = FALSE, results = "asis"}
answerlist(paste("$P(", lab, ") = ", format(sol), "\\%$", sep = ""), markup = "markdown")
```


Meta-information
================
extype: cloze
exsolution: `r paste(sol, collapse = "|")`
exclozetype: num|num|num|num|num|num|num|num|num
exname: fourfold
extol: 0.05
exextra[numwidth,logical]: TRUE
