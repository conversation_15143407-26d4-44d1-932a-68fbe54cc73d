```{r data generation, echo = FALSE, results = "hide"}
dat <- matrix(c(
  "AGO", "Angola",
  "ALB", "Albania",
  "ARG", "Argentina",
  "ARM", "Armenia",
  "ATG", "Antigua and Barbuda",
  "AUS", "Australia",
  "AUT", "Austria",
  "AZE", "Azerbaijan",
  "BDI", "Burundi",
  "BEL", "Belgium",
  "BEN", "Benin",
  "BFA", "Burkina Faso",
  "BGD", "Bangladesh",
  "BGR", "Bulgaria",
  "BHR", "Bahrain",
  "BHS", "Bahamas",
  "BIH", "Bosnia and Herzegovina",
  "BLR", "Belarus",
  "BLZ", "Belize",
  "BMU", "Bermuda",
  "BOL", "Bolivia",
  "BRA", "Brazil",
  "BRB", "Barbados",
  "BRN", "Brunei",
  "BTN", "Bhutan",
  "BWA", "Botswana",
  "CAF", "Central African Republic",
  "CAN", "Canada",
  "CHE", "Switzerland",
  "CHL", "Chile",
  "CHN", "China",
  "CIV", "Cote d'Ivoire",
  "CMR", "Cameroon",
  "COD", "Congo, Democratic Republic",
  "COG", "Congo, Republic of",
  "COL", "Colombia",
  "COM", "Comoros",
  "CPV", "Cape Verde",
  "CRI", "Costa Rica",
  "CYP", "Cyprus",
  "CZE", "Czech Republic",
  "DEU", "Germany",
  "DJI", "Djibouti",
  "DMA", "Dominica",
  "DNK", "Denmark",
  "DOM", "Dominican Republic",
  "ECU", "Ecuador",
  "EGY", "Egypt",
  "ESP", "Spain",
  "EST", "Estonia",
  "ETH", "Ethiopia",
  "FIN", "Finland",
  "FJI", "Fiji",
  "FRA", "France",
  "GAB", "Gabon",
  "GBR", "United Kingdom",
  "GEO", "Georgia",
  "GHA", "Ghana",
  "GIN", "Guinea",
  "GMB", "Gambia",
  "GNB", "Guinea-Bissau",
  "GNQ", "Equatorial Guinea",
  "GRC", "Greece",
  "GRD", "Grenada",
  "GTM", "Guatemala",
  "HKG", "Hong Kong",
  "HND", "Honduras",
  "HRV", "Croatia",
  "HUN", "Hungary",
  "IDN", "Indonesia",
  "IND", "India",
  "IRL", "Ireland",
  "IRN", "Iran",
  "IRQ", "Iraq",
  "ISL", "Iceland",
  "ISR", "Israel",
  "ITA", "Italy",
  "JAM", "Jamaica",
  "JOR", "Jordan",
  "JPN", "Japan",
  "KAZ", "Kazakhstan",
  "KEN", "Kenya",
  "KGZ", "Kyrgyzstan",
  "KHM", "Cambodia",
  "KNA", "St. Kitts & Nevis",
  "KOR", "Korea, Republic of",
  "KWT", "Kuwait",
  "LAO", "Laos",
  "LBN", "Lebanon",
  "LBR", "Liberia",
  "LCA", "St. Lucia",
  "LKA", "Sri Lanka",
  "LSO", "Lesotho",
  "LTU", "Lithuania",
  "LUX", "Luxembourg",
  "LVA", "Latvia",
  "MAC", "Macao",
  "MAR", "Morocco",
  "MDA", "Moldova",
  "MDG", "Madagascar",
  "MDV", "Maldives",
  "MEX", "Mexico",
  "MKD", "Macedonia",
  "MLI", "Mali",
  "MLT", "Malta",
  "MNE", "Montenegro",
  "MNG", "Mongolia",
  "MOZ", "Mozambique",
  "MRT", "Mauritania",
  "MUS", "Mauritius",
  "MWI", "Malawi",
  "MYS", "Malaysia",
  "NAM", "Namibia",
  "NER", "Niger",
  "NGA", "Nigeria",
  "NLD", "Netherlands",
  "NOR", "Norway",
  "NPL", "Nepal",
  "NZL", "New Zealand",
  "OMN", "Oman",
  "PAK", "Pakistan",
  "PAN", "Panama",
  "PER", "Peru",
  "PHL", "Philippines",
  "POL", "Poland",
  "PRT", "Portugal",
  "PRY", "Paraguay",
  "QAT", "Qatar",
  "ROU", "Romania",
  "RUS", "Russia",
  "RWA", "Rwanda",
  "SAU", "Saudi Arabia",
  "SDN", "Sudan",
  "SEN", "Senegal",
  "SGP", "Singapore",
  "SLE", "Sierra Leone",
  "SLV", "El Salvador",
  "SRB", "Serbia",
  "STP", "Sao Tome and Principe",
  "SUR", "Suriname",
  "SVK", "Slovakia",
  "SVN", "Slovenia",
  "SWE", "Sweden",
  "SWZ", "Swaziland",
  "SYR", "Syria",
  "TCD", "Chad",
  "TGO", "Togo",
  "THA", "Thailand",
  "TJK", "Tajikistan",
  "TKM", "Turkmenistan",
  "TTO", "Trinidad & Tobago",
  "TUN", "Tunisia",
  "TUR", "Turkey",
  "TWN", "Taiwan",
  "TZA", "Tanzania",
  "UGA", "Uganda",
  "UKR", "Ukraine",
  "URY", "Uruguay",
  "USA", "United States of America",
  "UZB", "Uzbekistan",
  "VCT", "St. Vincent & Grenadines",
  "VEN", "Venezuela",
  "VNM", "Vietnam",
  "YEM", "Yemen",
  "ZAF", "South Africa",
  "ZMB", "Zambia",
  "ZWE", "Zimbabwe"),
  nrow = 2
)
i <- sample(1:ncol(dat), 1)
iso <- dat[1, i]
ctr <- dat[2, i]
```

Question
========
What is the three-letter country code (ISO 3166-1 alpha-3) for
`r ctr`?

Solution
========
The ISO 3166-1 alpha-3 code for `r ctr` is `r iso`.

Meta-information
================
extype: string
exsolution: `r iso`
exname: ISO country codes
