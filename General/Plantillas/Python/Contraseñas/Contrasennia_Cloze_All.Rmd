---
output:
  word_document: default
  html_document: default
  pdf_document: default
---
```{r data generation, echo = FALSE, results = "hide"}
library(exams)
library(reticulate)

# Configurar el tipo de salida
output_type <- "html"  # Cambie esto a "latex" cuando genere salidas LaTeX

# Configurar Python
use_python("/usr/bin/python3", required = TRUE)

# Importar las librerías necesarias de Python
py_run_string("
import secrets
import string
import random
import re

def generate_password():
    lower = string.ascii_lowercase
    upper = string.ascii_uppercase
    digits = string.digits
    symbols = '!@#$%^&*(),.?\":{}|<>'
    
    # Decidir aleatoriamente si se incluirán símbolos, números y letras
    include_symbols = random.choice([True, False])
    include_numbers = random.choice([True, False])
    include_letters = random.choice([True, False])
    
    # Asegurar que al menos uno de los tres tipos esté incluido
    if not (include_symbols or include_numbers or include_letters):
        include_letters = True
    
    # Crear el alfabeto basado en las decisiones anteriores
    alphabet = ''
    if include_symbols:
        alphabet += symbols
    if include_numbers:
        alphabet += digits
    if include_letters:
        alphabet += lower + upper
    
    # Generar longitud aleatoria entre 2 y 15
    length = random.randint(2, 15)
    
    # Generar la contraseña
    password = ''.join(secrets.choice(alphabet) for i in range(length))
    
    # Asegurar que la longitud sea como máximo 15 y mínimo 2
    return password[:15] if len(password) > 15 else password
")

# Generar una contraseña aleatoria
password <- py$generate_password()

# Imprimir la contraseña generada para depuración
print(paste("Contraseña generada:", password))

# Función para escapar caracteres especiales de LaTeX
escape_latex <- function(x) {
  x <- gsub("([\\\\#$%&_{}])", "\\\\\\1", x)
  x <- gsub("\\^", "\\\\textasciicircum{}", x)
  x <- gsub("~", "\\\\textasciitilde{}", x)
  x
}

# Función para escapar caracteres especiales de HTML
escape_html <- function(x) {
  x <- gsub("&", "&amp;", x)
  x <- gsub("<", "&lt;", x)
  x <- gsub(">", "&gt;", x)
  x <- gsub("\"", "&quot;", x)
  x <- gsub("'", "&#39;", x)
  x
}

# Escapar la contraseña para LaTeX y HTML
password_latex <- escape_latex(password)
password_html <- escape_html(password)

# *** Función count_tips() corregida ***
count_tips <- function(password) {
  tips_count <- 0
  tips_fulfilled <- rep(FALSE, 12)

  # Consejo 1: Longitud mínima de 8 caracteres
  if (nchar(password) >= 8) {
    tips_count <- tips_count + 1
    tips_fulfilled[1] <- TRUE
  } else {
    return(list(count = 0, fulfilled = rep(FALSE, 12)))
  }

  # Consejo 2: Incluye símbolos
  has_symbols <- grepl("[!@#$%^&*(),.?\":{}|<>]", password, perl = TRUE)
  if (has_symbols) {
    tips_count <- tips_count + 1
    tips_fulfilled[2] <- TRUE
    
    # Consejo 5: No tiene símbolos iguales seguidos (se evalúa solo si Consejo 2 se cumple)
    if (!grepl("([!@#$%^&*(),.?\":{}|<>])\\1", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[5] <- TRUE
    }
  }

  # Consejo 3: Incluye números
  has_numbers <- grepl("[0-9]", password, perl = TRUE)
  if (has_numbers) {
    tips_count <- tips_count + 1
    tips_fulfilled[3] <- TRUE

    # Consejo 6: No tiene números iguales seguidos
    if (!grepl("([0-9])\\1", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[6] <- TRUE
    }

    # Consejo 7: No tiene números consecutivos seguidos
    if (!grepl("(01|12|23|34|45|56|67|78|89|90|10|21|32|43|54|65|76|87|98)", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[7] <- TRUE
    }

    # Consejo 8: No tiene solo números
    if (has_symbols || grepl("[A-Za-z]", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[8] <- TRUE
    }
  }

  # Consejo 4: Incluye letras
  has_letters <- grepl("[A-Za-z]", password, perl = TRUE)
  if (has_letters) {
    tips_count <- tips_count + 1
    tips_fulfilled[4] <- TRUE

    # Consejo 9: No tiene solo mayúsculas
    if (grepl("[a-z]", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[9] <- TRUE
    }

    # Consejo 10: No tiene solo minúsculas
    if (grepl("[A-Z]", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[10] <- TRUE
    }

    # Consejo 11: No tiene letras iguales consecutivas
    if (!grepl("([a-zA-Z])\\1", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[11] <- TRUE
    }

    # Consejo 12: No tiene letras consecutivas seguidas
    if (!grepl("(?i)(a(?=b)|b(?=c)|c(?=d)|d(?=e)|e(?=f)|f(?=g)|g(?=h)|h(?=i)|i(?=j)|j(?=k)|k(?=l)|l(?=m)|m(?=n)|n(?=o)|o(?=p)|p(?=q)|q(?=r)|r(?=s)|s(?=t)|t(?=u)|u(?=v)|v(?=w)|w(?=x)|x(?=y)|y(?=z))", password, perl = TRUE)) {
      tips_count <- tips_count + 1
      tips_fulfilled[12] <- TRUE
    }
  }

  return(list(count = tips_count, fulfilled = tips_fulfilled))
}

# Función para calcular el nivel de seguridad
calculate_security_level <- function(tips_count) {
  if (tips_count <= 2) return("Muy bajo")
  if (tips_count <= 5) return("Bajo")
  if (tips_count <= 8) return("Medio")
  if (tips_count <= 10) return("Alto")
  return("Muy alto")
}

# Contar el número de consejos cumplidos
tips_result <- count_tips(password)
tips_count <- tips_result$count
tips_fulfilled <- tips_result$fulfilled

# Imprimir el número de consejos cumplidos para depuración
print(paste("Número de consejos cumplidos:", tips_count))

# Determinar el nivel de seguridad basado en los consejos cumplidos
calculated_security_level <- calculate_security_level(tips_count)

# Imprimir el nivel de seguridad calculado para depuración
print(paste("Nivel de seguridad calculado:", calculated_security_level))

# Generar un nombre aleatorio
names <- c("Juan", "María", "Carlos", "Ana", "Pedro", "Sofía", "Luis", "Laura",
           "Martha", "Violeta", "Antonia", "Consuelo")
random_name <- sample(names, 1)

# Generar el enunciado
question <- sprintf("%s busca una contraseña segura para un sitio web de descargas de música. Para crear una contraseña segura se debe tener en cuenta 12 consejos importantes.", random_name)

question2 <- sprintf("Al usar su gestor de contraseñas se genera la contraseña %s",
                     if (output_type == "html") password_html else password_latex)

# Generar la respuesta
solution <- sprintf("%s comprueba que la contraseña cumple con %d consejos. Por tanto, su nivel de seguridad es %s.", random_name, tips_count, calculated_security_level)

# Usar el mismo tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
typ <- match_exams_device()

# Código TikZ para el diagrama de consejos de contraseña
tikz_password <- "
\\begin{tikzpicture}[
    every node/.style={align=center, draw, font=\\footnotesize},
    level 1/.style={sibling distance=120mm},
    level 2/.style={sibling distance=40mm},
    arrow/.style={-{Stealth[length=3mm]}, draw, thick}
    ]
    
    % Fila 1
    \\node[rectangle, rounded corners] (c1) at (0,0) {CONSEJO 1: \\\\ 1. La contraseña debe tener, mínimo, 8 caracteres.};
    
    % Fila 2
    \\node[rectangle, below left=2cm and 4cm of c1] (c2) {2. Debe tener símbolos.};
    \\node[rectangle, below=2cm of c1] (c3) {3. Debe tener números.};
    \\node[rectangle, below right=2cm and 4cm of c1] (c4) {4. Debe tener letras.};
    
    % Fila 3
    \\node[rectangle, below=1.5cm of c2] (c5) {5. Los símbolos iguales no \\\\ deben estar seguidos.};
    \\node[rectangle, below=1.5cm of c3] (c6) {6. Los números iguales no \\\\ deben estar seguidos.};
    \\node[rectangle, below=0.8cm of c6] (c7) {7. Evitar números consecutivos\\\\ seguidos.};
    \\node[rectangle, below=0.8cm of c7] (c8) {8. La contraseña no debe tener sólo \\\\ números.};
    \\node[rectangle, below=1.5cm of c4] (c9) {9. No sólo \\\\ mayúsculas.};
    \\node[rectangle, below=0.8cm of c9] (c10) {10. No sólo \\\\ minúsculas.};
    \\node[rectangle, below=0.8cm of c10] (c11) {11. No tener \\\\ letras iguales \\\\ consecutivas.};
    \\node[rectangle, below=0.8cm of c11] (c12) {12. Evitar colocar letras consecutivas seguidas.};
    
    % Conexiones
    \\draw [arrow] (c1.south west) -- ++(0,-0.7cm) -| (c2.north);
    \\draw [arrow] (c1.south) -- (c3.north);
    \\draw [arrow] (c1.south east) -- ++(0,-0.7cm) -| (c4.north);
    \\draw [arrow] (c2.south) -- (c5.north);
    \\draw [arrow] (c3.south) -- (c6.north);
    \\draw [arrow] (c3.south) -- (c7.north);
    \\draw [arrow] (c3.south) -- (c8.north);
    \\draw [arrow] (c4.south) -- (c9.north);
    \\draw [arrow] (c4.south) -- (c10.north);
    \\draw [arrow] (c4.south) -- (c11.north);
    \\draw [arrow] (c4.south) -- (c12.north);
    
    % Agrupaciones
    \\node[draw=none, fit=(c2) (c3) (c4), inner sep=0.2cm, label=left:{CONSEJOS 2 AL 4:}] {};
    \\node[draw=none, fit=(c5) (c6) (c7) (c8) (c9) (c10) (c11) (c12), inner sep=0.2cm, label=left:{CONSEJOS 5 AL 12:}] {};
    
\\end{tikzpicture}
"

# Configuración de knitr para TikZ
knitr::opts_chunk$set(warning = FALSE, message = FALSE) 

# Función para incluir el diagrama TikZ
include_tikz_password <- function() {
  include_tikz(tikz_password, name = "password_tips", markup = "markdown",
               format = typ, library = c("shapes", "backgrounds", "arrows.meta", "positioning", "fit"),
               packages = c("tikz"),
               width = "16cm")
}
```

Question
========

`r question`

```{r password-tips-diagram, echo=FALSE, results="asis", fig.align="center", fig.width=14}
include_tikz_password()
```

Un método para medir la seguridad de la contraseña, basado en la cantidad de consejos cumplidos, se resume en la siguiente tabla:


| N.º de consejos cumplidos | Nivel de seguridad |
|---------------------------|---------------------|
| 0-2                       | Muy bajo            |
| 3-5                       | Bajo                |
| 6-8                       | Medio               |
| 9-10                      | Alto                |
| 11-12                     | Muy alto            |


`r question2`

`r random_name` comprueba que la contraseña cumple con un total de ##ANSWER1## consejos. Por tanto, su nivel de seguridad es ##ANSWER2##

Solution
========

`r solution`

Explicación de los consejos cumplidos:

```{r, results='asis', echo=FALSE}

consejos <- c(
  "Longitud mínima de 8 caracteres",
  "Incluye símbolos",
  "Incluye números",
  "Incluye letras",
  "No tiene símbolos iguales seguidos",
  "No tiene números iguales seguidos",
  "No tiene números consecutivos seguidos",
  "No tiene solo números",
  "No tiene solo mayúsculas",
  "No tiene solo minúsculas",
  "No tiene letras iguales consecutivas",
  "No tiene letras consecutivas seguidas"
)

has_symbols <- grepl("[!@#$%^&*(),.?\":{}|<>]", password, perl = TRUE)
has_numbers <- grepl("[0-9]", password, perl = TRUE)
has_letters <- grepl("[A-Za-z]", password, perl = TRUE)

for (i in 1:12) {
  if (i == 1) {
    if (tips_fulfilled[i]) {
      cat(sprintf(" %d. %s: Sí\n", i, consejos[i]))
    } else {
      cat(sprintf(" %d. %s: No\n", i, consejos[i]))
      break  # Si el Consejo 1 no se cumple, termina el análisis
    }
  } else if (tips_fulfilled[i]) {
    cat(sprintf(" %d. %s: Sí\n", i, consejos[i]))
  } else if (i == 5 && !has_symbols) {
    cat(sprintf(" %d. %s: No evaluado (no hay símbolos)\n", i, consejos[i]))
  } else if ((i == 6 || i == 7 || i == 8) && !has_numbers) {
    cat(sprintf(" %d. %s: No evaluado (no hay números)\n", i, consejos[i]))
  } else if ((i == 9 || i == 10 || i == 11 || i == 12) && !has_letters) {
    cat(sprintf(" %d. %s: No evaluado (no hay letras)\n", i, consejos[i]))
  } else {
    cat(sprintf(" %d. %s: No\n", i, consejos[i]))
  }
}

cat(sprintf("\nContraseña evaluada: %s\n", password))

# Imprimir el número de consejos cumplidos para depuración
cat("\nNúmero de consejos cumplidos:", tips_count, "\n")

# Determinar el nivel de seguridad basado en los consejos cumplidos
calculated_security_level <- calculate_security_level(tips_count)

# Imprimir el nivel de seguridad calculado para depuración
cat("\nNivel de seguridad calculado:", calculated_security_level, "\n")
```


Número total de consejos cumplidos: `r tips_count`
Nivel de seguridad resultante: `r calculated_security_level`

Meta-information
================
exname: Seguridad de Contraseña
extype: cloze
exclozetype: num|string
exsolution: `r tips_count`|`r calculated_security_level`
extol: 0
exsection: Seguridad Informática
exextra[Type]: Conceptual