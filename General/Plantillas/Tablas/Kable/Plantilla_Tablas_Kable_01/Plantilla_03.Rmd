---
output:
  pdf_document: default
  html_document: default
---
```{r setup, include=FALSE}
library(exams)
library(knitr)

# Usa el mismo tipo de gráfico (pdf, svg, png) que la llamada actual de xweave()
typ <- match_exams_device()

# Datos para la tabla
datos_tabla <- data.frame(
  Dia = c("Lunes", "Martes", "Miércoles", "Jueves", "Viernes"),
  Fila1 = c("A", "B", "C", "D", "E"),
  Fila2 = c("F", "G", "H", "J", "K"),
  Fila3 = c("A", "B", "C", "D", "E"),
  Fila4 = c("F", "G", "H", "J", "K")
)

# Función para crear la tabla en diferentes formatos
crear_tabla <- function(datos, formato) {
  if(formato %in% c("latex", "html")) {
    return(kable(datos, format = formato, align = "c"))
  } else {
    return(kable(datos, format = "pipe", align = "c"))
  }
}
```


```{r, echo=FALSE, results="asis"}
crear_tabla(datos_tabla, typ)
```
