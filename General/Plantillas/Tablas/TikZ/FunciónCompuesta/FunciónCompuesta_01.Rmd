---
output:
  html_document: default
  word_document: default
  pdf_document: default
---
```{r data generation, echo = FALSE, results = "hide"}
library(exams)

# Generar datos aleatorios para las tablas
set.seed(sample(1:1000, 1))
x_values <- -2:2
f_values <- sapply(x_values, function(x) 2*x)
g_values <- sample(-3:0, 5, replace = TRUE)

# Crear las tablas
tabla1 <- data.frame(x = x_values, f_x = f_values)
tabla2 <- data.frame(x = x_values, g_x = g_values)

# Función para generar el código TikZ de la tabla
generate_tikz_table <- function(tabla1, tabla2) {
  tikz_code <- "\\begin{tikzpicture}
    \\node[rectangle, draw, minimum width=6cm, minimum height=0.7cm] at (0, 3) {Tabla 1};
    \\node[rectangle, draw, minimum width=6cm, minimum height=0.7cm] at (6.5, 3) {Tabla 2};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (-1.5, 2.3) {x};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (1.5, 2.3) {f(x)};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (5, 2.3) {x};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (8, 2.3) {g(x)};\n"
  
  for (i in 1:5) {
    y_pos <- 1.6 - (i-1) * 0.7
    tikz_code <- paste0(tikz_code, sprintf("
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (-1.5, %.1f) {%d};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (1.5, %.1f) {%d};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (5, %.1f) {%d};
    \\node[rectangle, draw, minimum width=3cm, minimum height=0.7cm] at (8, %.1f) {%d};",
    y_pos, tabla1$x[i], y_pos, tabla1$f_x[i], y_pos, tabla2$x[i], y_pos, tabla2$g_x[i]))
  }
  
  tikz_code <- paste0(tikz_code, "\n\\end{tikzpicture}")
  return(tikz_code)
}

# Generar el código TikZ
tikz_table <- generate_tikz_table(tabla1, tabla2)

# Configuración de knitr para TikZ
knitr::opts_chunk$set(warning = FALSE, message = FALSE)

# Función para incluir el diagrama TikZ
include_tikz_table <- function() {
  include_tikz(tikz_table, name = "funcion_compuesta", markup = "markdown",
               format = match_exams_device(),
               width = "12cm")
}

# Generar las opciones de respuesta
correct_answer <- "No, porque falta información sobre la función g(x) para completar el procedimiento"
wrong_answers <- c(
  "Sí, porque se conocen los valores de f(x) y g(x) para los mismos valores de x",
  "No, porque se desconoce la fórmula utilizada para calcular los valores de f(x)",
  "Sí, porque ese valor de x aparece en ambas columnas de las tablas de f(x) y g(x)"
)

# Mezclar las respuestas
all_answers <- c(correct_answer, wrong_answers)
shuffled_answers <- sample(all_answers)

# Encontrar la posición de la respuesta correcta
correct_position <- which(shuffled_answers == correct_answer)

# Crear la solución en formato exams (vector lógico)
solution <- rep(FALSE, length(shuffled_answers))
solution[correct_position] <- TRUE
```

Question
========

Las tablas 1 y 2 muestran algunos valores de las funciones f(x) y g(x) respectivamente.

```{r tabla-funcion-compuesta, echo=FALSE, results="asis", fig.align="center", fig.width=12}
include_tikz_table()
```

Con estas funciones se puede crear una nueva función: h(x)=g(f(x)).

Para calcular los valores de esta función, a partir de la información de las tablas 1 y 2, se debe efectuar este procedimiento:

- **Paso 1**. Escoger el valor de x con el que se va a calcular h(x).

- **Paso 2**. Ubicar el valor escogido en el paso 1, en la columna de x de la tabla 1 y tomar el valor de f(x) que se encuentra frente a este.

- **Paso 3**. Ubicar el valor hallado en el paso 2, en la columna de x de la tabla 2, y tomar el valor de g(x) que se encuentra frente a este.

- **Paso 4**. El valor encontrado en el paso 3 corresponde a h(x).

Con base en lo anterior, ¿es posible hallar el valor de h(-2)?

Answerlist
----------
* `r shuffled_answers[1]`
* `r shuffled_answers[2]`
* `r shuffled_answers[3]`
* `r shuffled_answers[4]`

Solution
========

La respuesta correcta es: `r correct_answer`

Explicación:

Para calcular h(-2), seguimos los pasos del procedimiento:

1. Escogemos x = -2
2. En la tabla 1, para x = -2, f(-2) = -4
3. Ahora debemos buscar x = -4 en la tabla 2, pero este valor no está presente en la columna x de la tabla 2.

Por lo tanto, no podemos completar el paso 3 y, en consecuencia, no podemos hallar h(-2).

El problema radica en que la función g(x) no está definida para todos los valores que produce f(x). En este caso específico, no tenemos información sobre g(-4), que es necesaria para calcular h(-2) = g(f(-2)) = g(-4).

Esta situación ilustra una limitación importante cuando trabajamos con funciones compuestas definidas por tablas de valores: necesitamos que el rango de la primera función (f) esté contenido en el dominio de la segunda función (g) para poder calcular todos los valores de la función compuesta (h).

Meta-information
================
exname: Función Compuesta
extype: schoice
exsolution: `r mchoice2string(solution)`
exshuffle: TRUE