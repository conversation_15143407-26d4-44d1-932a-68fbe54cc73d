---
output:
html_document:
df_print: paged
mathjax: true
pdf_document:
latex_engine: xelatex
keep_tex: true
word_document: default
header-includes:
- \usepackage[spanish]{babel}
- \usepackage{amsmath}
- \usepackage{fontspec}
- \usepackage{unicode-math}
- \usepackage{graphicx}
- \usepackage{adjustbox}
- \usepackage{tikz}
- \usepackage{pgfplots}
- \usetikzlibrary{3d,babel}
icfes:
competencia: formulacion_ejecucion
nivel_dificultad: 2
contenido:
categoria: algebra_calculo
tipo: generico
contexto: comunitario
eje_axial: eje2
componente: numerico_variacional
---

```{r inicio, include=FALSE}
# Librerías esenciales
library(exams)
library(ggplot2)
library(knitr)
library(reticulate)
library(testthat)
library(data.table)
library(readxl)
library(datasets)
library(stringr)
library(digest)


# ===============================================================================
# SISTEMA DE CORRECCIÓN EMBEBIDO (Compatible con R-exams)
# ===============================================================================

# ===============================================================================
# SISTEMA DE CONCORDANCIA POR GÉNERO Y NÚMERO
# ===============================================================================

# Diccionario de elementos por género
elementos_genero <- list(
  masculinos = c("vehículos", "hogares", "estudiantes", "centros médicos", "hospitales", "colegios"),
  femeninos = c("familias", "empresas", "instituciones", "organizaciones", "compañías")
)

# Diccionario de adjetivos con todas sus formas
adjetivos_formas <- list(
  "matriculado" = list(masc = "matriculados", fem = "matriculadas"),
  "registrado" = list(masc = "registrados", fem = "registradas"),
  "certificado" = list(masc = "certificados", fem = "certificadas"),
  "asegurado" = list(masc = "asegurados", fem = "aseguradas"),
  "acreditado" = list(masc = "acreditados", fem = "acreditadas"),
  "autorizado" = list(masc = "autorizados", fem = "autorizadas"),
  "habilitado" = list(masc = "habilitados", fem = "habilitadas")
)

# Función para obtener la forma correcta de un adjetivo
obtener_forma_adjetivo <- function(elemento, adjetivo_base) {
  es_femenino <- elemento %in% elementos_genero$femeninos

  if (adjetivo_base %in% names(adjetivos_formas)) {
    formas <- adjetivos_formas[[adjetivo_base]]
    return(if (es_femenino) formas$fem else formas$masc)
  }

  return(adjetivo_base)
}

# Función para crear variables textuales contextuales
crear_variables_textuales <- function(elemento, condicion) {
  es_femenino <- elemento %in% elementos_genero$femeninos

  # Crear todas las formas necesarias
  variables <- list(
    elemento = elemento,
    condicion = condicion,

    # Formas específicas para diferentes contextos
    registrados = obtener_forma_adjetivo(elemento, "registrado"),
    matriculados = obtener_forma_adjetivo(elemento, "matriculado"),
    certificados = obtener_forma_adjetivo(elemento, "certificado"),
    beneficiarios = obtener_forma_adjetivo(elemento, "beneficiario"),
    asegurados = obtener_forma_adjetivo(elemento, "asegurado"),

    # Información de género para condicionales
    es_femenino = es_femenino,
    es_masculino = !es_femenino
  )

  return(variables)
}

# Función para corregir errores de concordancia
corregir_todos_errores_concordancia <- function(texto) {
  errores_comunes <- c(
    "familias matriculados" = "familias matriculadas",
    "familias registrados" = "familias registradas",
    "familias certificados" = "familias certificadas",
    "familias asegurados" = "familias aseguradas",
    "familias acreditados" = "familias acreditadas",
    "empresas matriculados" = "empresas matriculadas",
    "empresas registrados" = "empresas registradas",
    "empresas certificados" = "empresas certificadas",
    "empresas beneficiarios" = "empresas beneficiarias",
    "empresas asegurados" = "empresas aseguradas",
    "empresas acreditados" = "empresas acreditadas"
  )

  for (error in names(errores_comunes)) {
    if (require(stringr, quietly = TRUE)) {
      texto <- str_replace_all(texto, fixed(error), errores_comunes[[error]])
    } else {
      texto <- gsub(error, errores_comunes[[error]], texto, fixed = TRUE)
    }
  }
  return(texto)
}

# Función para validar coherencia
validar_coherencia <- function(entidad, elemento, condicion, total) {
  errores <- c()
  if (entidad == "ICBF" && elemento != "familias") {
    errores <- c(errores, "ICBF debe manejar familias")
  }
  if (total < 1000 || total > 50000000) {
    errores <- c(errores, "Total fuera de rango realista")
  }
  return(errores)
}

# Función para procesar datos con correcciones
procesar_datos_con_correcciones <- function(datos) {
  errores_coherencia <- validar_coherencia(datos$entidad, datos$elemento,
                                          datos$condicion, datos$total)
  if (length(errores_coherencia) > 0) {
    warning("Errores de coherencia detectados: ",
            paste(errores_coherencia, collapse = ", "))
  }

  datos$condicion_corregida <- corregir_concordancia_genero(datos$elemento, datos$condicion)
  datos$elemento_texto <- corregir_todos_errores_concordancia(datos$elemento)
  datos$condicion_texto <- corregir_todos_errores_concordancia(datos$condicion_corregida)

  return(datos)
}

# Configurar Python si es necesario
use_python("/usr/bin/python3", required = TRUE)

# Configuración global
typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
warning = FALSE,
message = FALSE,
fig.keep = 'all',
dev = c("png", "pdf"),
dpi = 150,
echo = FALSE,
results = "hide"
)

# Semilla aleatoria para diversidad de versiones
set.seed(sample(1:100000, 1))
```

```{r data_generation, echo=FALSE, results="hide"}
# Función principal de generación de datos
generar_datos <- function() {
# Contextos posibles
contextos <- list(
list(entidad = "Ministerio de Transporte", elemento = "vehículos", condicion = "asegurados"),
list(entidad = "DANE", elemento = "hogares", condicion = "con internet"),
list(entidad = "Ministerio de Educación", elemento = "estudiantes", condicion = "becados"),
list(entidad = "Superintendencia Financiera", elemento = "empresas", condicion = "certificadas"),
list(entidad = "ICBF", elemento = "familias", condicion = "beneficiadas"),
list(entidad = "Ministerio de Salud", elemento = "centros médicos", condicion = "acreditados")
)

# Seleccionar contexto aleatorio
contexto <- sample(contextos, 1)[[1]]

# Fracciones posibles (numerador menor que denominador)
fracciones <- list(
list(num = 1, den = 3), list(num = 2, den = 5), list(num = 3, den = 7),
list(num = 4, den = 9), list(num = 2, den = 7), list(num = 3, den = 8),
list(num = 1, den = 4), list(num = 3, den = 5), list(num = 5, den = 8),
list(num = 2, den = 9), list(num = 4, den = 7), list(num = 1, den = 5)
)

fraccion <- sample(fracciones, 1)[[1]]

# Total de elementos (entre 500,000 y 5,000,000)
total <- sample(seq(500000, 5000000, 100000), 1)

# Calcular cantidad que cumple la condición
cantidad_condicion <- total * fraccion$num / fraccion$den

# Generar distractores
distractor_doble <- total * 2
distractor_promedio <- total / 2
distractor_porcentaje <- (fraccion$num / fraccion$den) * 100

return(list(
entidad = contexto$entidad,
elemento = contexto$elemento,
condicion = contexto$condicion,
total = total,
numerador = fraccion$num,
denominador = fraccion$den,
cantidad_condicion = cantidad_condicion,
distractor_doble = distractor_doble,
distractor_promedio = distractor_promedio,
distractor_porcentaje = distractor_porcentaje
))
}

# Aplicar correcciones usando el nuevo sistema
aplicar_correcciones_datos <- function(datos) {
  # Crear variables textuales contextuales
  variables_texto <- crear_variables_textuales(datos$elemento, datos$condicion)

  # Agregar variables textuales a los datos
  datos$variables_texto <- variables_texto
  datos$elemento_texto <- variables_texto$elemento
  datos$condicion_texto <- variables_texto$condicion

  return(datos)
}

# Función para corregir texto completo (para usar en Question/Solution)
corregir_texto_completo <- function(texto, elemento, condicion) {
  # Primero aplicar correcciones de concordancia
  texto <- corregir_todos_errores_concordancia(texto)

  # Corregir casos específicos donde aparece "registrados" hardcodeado
  if (elemento == "familias") {
    texto <- gsub("familias registrados", "familias registradas", texto, fixed = TRUE)
    texto <- gsub("de familias registrados", "de familias registradas", texto, fixed = TRUE)
  }

  if (elemento == "empresas") {
    texto <- gsub("empresas registrados", "empresas registradas", texto, fixed = TRUE)
    texto <- gsub("de empresas registrados", "de empresas registradas", texto, fixed = TRUE)
  }

  return(texto)
}

# Generar datos del ejercicio
datos <- generar_datos()

# Aplicar correcciones automáticas
datos <- aplicar_correcciones_datos(datos)

# Validar coherencia de los datos generados
errores_coherencia <- validar_coherencia(datos$entidad, datos$elemento,
                                        datos$condicion, datos$total)
if (length(errores_coherencia) > 0) {
  warning("Errores de coherencia detectados: ",
          paste(errores_coherencia, collapse = ", "))
}

# Extraer variables individuales para facilitar uso
entidad <- datos$entidad
elemento <- datos$elemento_texto
condicion <- datos$condicion_texto
total <- datos$total
numerador <- datos$numerador
denominador <- datos$denominador
cantidad_condicion <- datos$cantidad_condicion

# Extraer variables textuales contextuales
vt <- datos$variables_texto  # Alias para facilitar uso

# Variables específicas con concordancia correcta
elemento_registrados <- vt$registrados
elemento_matriculados <- vt$matriculados
elemento_certificados <- vt$certificados
elemento_beneficiarios <- vt$beneficiarios
```

```{r version_diversity_test, echo=FALSE, results="hide"}
# Prueba obligatoria de diversidad de versiones
test_that("Prueba de diversidad de versiones", {
versiones <- list()
for(i in 1:1000) {
datos_test <- generar_datos()
versiones[[i]] <- digest::digest(datos_test)
}

n_versiones_unicas <- length(unique(versiones))
expect_true(n_versiones_unicas >= 300,
info = paste("Solo se generaron", n_versiones_unicas,
"versiones únicas. Se requieren al menos 300."))
})
```

```{r semantic_correction_test, echo=FALSE, results="hide"}
# Pruebas básicas del nuevo sistema de concordancia
if (require(testthat, quietly = TRUE)) {
  test_that("Pruebas del sistema de concordancia", {
    # Probar obtención de formas correctas
    expect_equal(obtener_forma_adjetivo("familias", "registrado"), "registradas")
    expect_equal(obtener_forma_adjetivo("empresas", "matriculado"), "matriculadas")
    expect_equal(obtener_forma_adjetivo("vehículos", "registrado"), "registrados")

    # Probar creación de variables textuales
    vt_familias <- crear_variables_textuales("familias", "beneficiarias")
    expect_equal(vt_familias$registrados, "registradas")
    expect_equal(vt_familias$matriculados, "matriculadas")
    expect_true(vt_familias$es_femenino)

    vt_vehiculos <- crear_variables_textuales("vehículos", "asegurados")
    expect_equal(vt_vehiculos$registrados, "registrados")
    expect_true(vt_vehiculos$es_masculino)

    # Probar validación de coherencia
    errores <- validar_coherencia("ICBF", "vehículos", "beneficiarios", 1000000)
    expect_true(length(errores) > 0) # Debe detectar incoherencia

    errores_ok <- validar_coherencia("ICBF", "familias", "beneficiarias", 1000000)
    expect_true(length(errores_ok) == 0) # No debe haber errores
  })
} else {
  cat("ℹ️  Pruebas de corrección omitidas (testthat no disponible)\n")
}
```

Question
========

Según el `r entidad`, en el país solo `r numerador` de cada `r denominador` `r elemento` están `r condicion`. Si el total de `r elemento` `r elemento_registrados` es `r format(total, big.mark = ".", decimal.mark = ",")`, al realizar la operación `r format(total, big.mark = ".", decimal.mark = ",")` por `r numerador`/`r denominador` se calcularía:

Answerlist
----------
- el doble de `r elemento` `r elemento_registrados`.
- la cantidad de `r elemento` `r condicion`.
- el promedio de `r elemento` `r elemento_registrados`.
- el porcentaje de `r elemento` `r condicion`.

Solution
========

Para resolver este problema, debemos analizar qué representa la operación matemática `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador`.

**Análisis del problema:**

- Total de `r elemento` `r elemento_registrados`: `r format(total, big.mark = ".", decimal.mark = ",")`
- Proporción de `r elemento` `r condicion`: `r numerador` de cada `r denominador`
- Operación a analizar: `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador`

**Interpretación matemática:**

Cuando multiplicamos el total por una fracción, estamos calculando qué parte del total representa esa fracción.

En este caso:

- `r format(total, big.mark = ".", decimal.mark = ",")` × `r numerador`/`r denominador` = `r format(cantidad_condicion, big.mark = ".", decimal.mark = ",")`

Este resultado representa la **cantidad de `r elemento` `r condicion`**.

**Verificación de las opciones:**

- **Opción A:** El doble sería `r format(total * 2, big.mark = ".", decimal.mark = ",")`
- **Opción B:** La cantidad de `r elemento` `r condicion` es `r format(cantidad_condicion, big.mark = ".", decimal.mark = ",")`
- **Opción C:** El promedio sería `r format(total / 2, big.mark = ".", decimal.mark = ",")`
- **Opción D:** El porcentaje sería `r round((numerador/denominador)*100, 1)`%

Answerlist
----------
- Falso. El doble de `r elemento` registrados sería `r format(total * 2, big.mark = ".", decimal.mark = ",")`.
- Verdadero. La operación calcula exactamente la cantidad de `r elemento` `r condicion`.
- Falso. El promedio de `r elemento` registrados sería `r format(total / 2, big.mark = ".", decimal.mark = ",")`.
- Falso. El porcentaje de `r elemento` `r condicion` es `r round((numerador/denominador)*100, 1)`%, no el resultado de la multiplicación.

Meta-information
================
exname: Interpretación de operaciones con fracciones en contexto
extype: schoice
exsolution: 0100
exshuffle: TRUE
exsection: Proporciones y Fracciones
