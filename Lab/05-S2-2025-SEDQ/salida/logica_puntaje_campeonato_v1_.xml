<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/logica_puntaje_campeonato_v1_/Lógica Matemática/Interpretación de Datos/Sistemas Numéricos/Fútbol</text>
</category>
</question>


<question type="multichoice">
<name>
<text> Q1 : logica_puntaje_campeonato_v1 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>En el campeonato de fútbol, un equipo está en la fase de grupos y necesita 22 puntos para clasificar a la segunda etapa. En la fase de grupos, cada equipo juega 10 partidos. Los puntos se asignan de la siguiente manera: 3 puntos por ganar, 1 punto por empatar y 0 puntos por perder.</p>
<table class="table_shade">
<thead>
<tr>
<th style="text-align: left;"><strong>Resultado del Partido</strong></th>
<th style="text-align: center;"><strong>Puntos Obtenidos</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td style="text-align: left;">Ganar</td>
<td style="text-align: center;">3</td>
</tr>
<tr>
<td style="text-align: left;">Empatar</td>
<td style="text-align: center;">1</td>
</tr>
<tr>
<td style="text-align: left;">Perder</td>
<td style="text-align: center;">0</td>
</tr>
</tbody>
</table>
<p>Con base en la información suministrada, ¿qué pregunta de las listadas abajo tiene una respuesta derivable?</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>La pregunta que se puede responder con la información proporcionada es la que indaga sobre el mínimo de partidos que se deben ganar para clasificar. Analicemos cada opción:</p>
<p><strong>Opción Correcta:</strong></p>
<p><strong>¿Cuál es el mínimo de partidos que se deben ganar para clasificar?</strong></p>
<p>Esta pregunta SÍ se puede responder. Se necesita determinar el menor número de victorias (V) tal que 3V + 1E <span class="math inline">\(\geq\)</span> 22, con la restricción de que V + E <span class="math inline">\(\leq\)</span> 10 (donde E es el número de empates).</p>
<p>Con los datos del problema:</p>
<ul>
<li>Puntos necesarios para clasificar: 22.</li>
<li>Partidos totales en la ronda: 10.</li>
<li>Puntos por victoria: 3.</li>
<li>Puntos por empate: 1.</li>
</ul>
<p>El cálculo muestra que el mínimo de victorias necesarias es <strong>6</strong>.</p>
<p>Esto se logra, por ejemplo, con 6 victorias (18 puntos) y 4 empates (4 puntos), sumando un total de 22 puntos en 10 partidos. Dado que 10 es menor o igual que 10, este escenario es viable.</p>
<p><strong>Otras Opciones (Distractores):</strong></p>
<ul>
<li><p><strong>Para que el equipo avance, ¿cuántas derrotas deben sufrir sus rivales directos?</strong></p>
<p>Esta pregunta NO se puede responder. La información dada detalla cómo un equipo obtiene sus puntos, pero no proporciona reglas o datos sobre cómo el rendimiento (derrotas) de los equipos contrincantes influye en su clasificación, más allá del resultado directo de los partidos jugados contra ellos.</p></li>
<li><p><strong>Considerando todas las fases, ¿cuál es el número global de partidos del campeonato?</strong></p>
<p>Esta pregunta NO se puede responder. Se conoce el número de partidos que juega cada equipo en la fase de grupos (10 partidos), pero no se dispone de información sobre el número total de equipos en el campeonato ni sobre el número total de rondas o fases que componen todo el campeonato.</p></li>
<li><p><strong>¿Cuál es el número de plazas disponibles para la segunda etapa?</strong></p>
<p>Esta pregunta NO se puede responder. Aunque se sabe que un equipo necesita 22 puntos para clasificar, no se especifica cuántos equipos en total avanzarán a la segunda etapa (por ejemplo, si es un número fijo de equipos, los mejores de cada grupo, etc.).</p></li>
</ul>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="100" format="html">
<text><![CDATA[<p>
¿Cuál es el mínimo de partidos que se deben ganar para clasificar?
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Verdadero
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Para que el equipo avance, ¿cuántas derrotas deben sufrir sus rivales directos?
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
Considerando todas las fases, ¿cuál es el número global de partidos del campeonato?
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
¿Cuál es el número de plazas disponibles para la segunda etapa?
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
