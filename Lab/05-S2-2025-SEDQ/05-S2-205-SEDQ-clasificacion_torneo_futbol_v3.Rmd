---
output:
  html_document: default
  word_document: default
  pdf_document:
    latex_engine: xelatex
---

# Metadatos ICFES
icfes:
  competencia:
    - formulacion_ejecucion
  nivel_dificultad: 4
  contenido:
    categoria: estadistica
    tipo: no_generico
  contexto: cotidiano
  eje_axial: eje3
  componente: aleatorio

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "xelatex")
options(tikzXelatex = TRUE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage[utf8]{inputenc}",
  "\\usepackage[T1]{fontenc}",
  "\\usepackage{amsmath,amssymb}",
  "\\usepackage{colortbl}"
))

library(exams)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.width = 6,
  fig.height = 5
)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria para reproducibilidad
set.seed(sample(1:10000, 1))

# Aleatorizar deportes para el contexto del problema
deportes <- c("fútbol", "baloncesto", "voleibol", "hockey", "balonmano", 
              "rugby", "waterpolo", "béisbol", "futsal", "handball")
deporte <- sample(deportes, 1)

# Aleatorizar nombres de competiciones
competiciones <- c("campeonato", "torneo", "liga", "copa", "certamen", 
                   "competición", "mundial", "clasificatoria", "eliminatoria")
competicion <- sample(competiciones, 1)

# Aleatorizar nombres de países/regiones
regiones <- c("internacional", "europeo", "asiático", "suramericano", "norteamericano",
              "africano", "nacional", "regional", "continental", "mundial")
region <- sample(regiones, 1)

# Aleatorizar tipos de competiciones
tipos_competicion <- c("profesional", "juvenil", "amateur", "universitario", "escolar",
                      "sub-21", "olímpico", "paralímpico", "senior", "élite")
tipo_competicion <- sample(tipos_competicion, 1)

# Aleatorizar fases de competición
fases_inicial <- c("primera ronda", "fase inicial", "fase de grupos", 
                  "etapa preliminar", "fase clasificatoria")
fase_inicial <- sample(fases_inicial, 1)

fases_siguiente <- c("siguiente ronda", "segunda fase", "siguiente etapa", 
                     "fase final", "etapa eliminatoria", "siguiente instancia")
fase_siguiente <- sample(fases_siguiente, 1)

# Sistema de puntuación complejo con bonificaciones
# Parámetros base del sistema
puntos_victoria <- sample(c(2, 3), 1)
puntos_empate <- 1
puntos_derrota <- 0

# Bonificaciones adicionales (para aumentar complejidad nivel 4)
puntos_bonus_goles <- sample(c(1, 2), 1)
umbral_goles_bonus <- sample(c(2, 3, 4), 1)

puntos_bonus_victorias_consecutivas <- sample(c(1, 2), 1)
umbral_victorias_consecutivas <- sample(c(2, 3), 1)

puntos_penalizacion_tarjetas <- -1
umbral_tarjetas_penalizacion <- sample(c(3, 4, 5), 1)

# Aleatorizar partidos por equipo (entre 10 y 20)
partidos_por_equipo <- sample(10:20, 1)

# Aleatorizar puntos necesarios para clasificar (valor desafiante)
max_puntos_sin_bonus <- puntos_victoria * partidos_por_equipo
min_puntos_posibles <- puntos_derrota * partidos_por_equipo
puntos_necesarios <- round(max_puntos_sin_bonus * sample(seq(0.6, 0.8, by = 0.05), 1))

# Parámetros para el equipo a analizar
# Definir rangos realistas de situaciones para el equipo
min_goles_por_partido <- 0
max_goles_por_partido <- 5
promedio_goles_por_partido <- sample(seq(1.5, 2.5, by = 0.1), 1)

min_tarjetas_por_partido <- 0
max_tarjetas_por_partido <- 5
promedio_tarjetas_por_partido <- sample(seq(1.0, 2.0, by = 0.1), 1)

# Probabilidad de conseguir bonus por goles (basado en distribución histórica)
prob_bonus_goles <- promedio_goles_por_partido / max_goles_por_partido * 0.7

# Probabilidad de recibir penalización por tarjetas
prob_penalizacion_tarjetas <- promedio_tarjetas_por_partido / max_tarjetas_por_partido * 0.6

# Probabilidad de obtener victorias consecutivas suficientes para el bonus
prob_bonus_victorias <- (puntos_victoria/3) * 0.5  # Ajustar según dificultad

# Modelo bayesiano simplificado para el análisis
# Nota: estos cálculos sirven como base para responder las preguntas y generar distractores

# Función para calcular combinaciones de victorias, empates, derrotas y bonificaciones
calcular_combinaciones_avanzadas <- function(total_partidos, puntos_objetivo, 
                                            pts_victoria, pts_empate, pts_derrota,
                                            pts_bonus_goles, umbral_goles,
                                            pts_bonus_victorias, umbral_victorias,
                                            pts_penalizacion, umbral_tarjetas,
                                            prob_bonus_goles, prob_bonus_victorias, 
                                            prob_penalizacion) {
  
  combinaciones <- list()
  combinacion_id <- 1
  
  # Función para estimar bonificaciones esperadas
  calcular_bonificaciones_esperadas <- function(victorias, empates, derrotas) {
    # Partidos con bonus por goles (estimación probabilística)
    partidos_con_bonus_goles <- round(total_partidos * prob_bonus_goles)
    bonus_goles_total <- min(partidos_con_bonus_goles, round(victorias * prob_bonus_goles * 1.5)) * pts_bonus_goles
    
    # Bonus por victorias consecutivas (estimación probabilística)
    # Asumimos distribución de victorias a lo largo de la temporada
    max_rachas_posibles <- max(0, victorias - umbral_victorias + 1)
    rachas_esperadas <- min(max_rachas_posibles, round(victorias / (umbral_victorias + 1) * prob_bonus_victorias))
    bonus_victorias_total <- rachas_esperadas * pts_bonus_victorias
    
    # Penalizaciones por tarjetas (estimación probabilística)
    partidos_con_penalizacion <- round(total_partidos * prob_penalizacion)
    penalizacion_total <- min(partidos_con_penalizacion, round(total_partidos * 0.3)) * pts_penalizacion
    
    # Total bonificaciones
    return(bonus_goles_total + bonus_victorias_total + penalizacion_total)
  }
  
  for (victorias in 0:total_partidos) {
    for (empates in 0:(total_partidos - victorias)) {
      derrotas <- total_partidos - victorias - empates
      puntos_base <- victorias * pts_victoria + empates * pts_empate + derrotas * pts_derrota
      
      # Calcular bonificaciones esperadas
      bonificaciones <- calcular_bonificaciones_esperadas(victorias, empates, derrotas)
      puntos_totales <- puntos_base + bonificaciones
      
      # Si esta combinación permite clasificar, la guardamos
      if (puntos_totales >= puntos_objetivo) {
        combinaciones[[combinacion_id]] <- list(
          victorias = victorias,
          empates = empates,
          derrotas = derrotas,
          puntos_base = puntos_base,
          bonificaciones = bonificaciones,
          puntos_totales = puntos_totales,
          partidos_bonus_goles = round(victorias * prob_bonus_goles),
          partidos_bonus_victorias = floor(victorias / umbral_victorias),
          partidos_penalizados = round(total_partidos * prob_penalizacion)
        )
        combinacion_id <- combinacion_id + 1
      }
    }
  }
  
  return(combinaciones)
}

# Obtener combinaciones válidas
combinaciones_validas <- calcular_combinaciones_avanzadas(
  partidos_por_equipo, 
  puntos_necesarios, 
  puntos_victoria, 
  puntos_empate, 
  puntos_derrota,
  puntos_bonus_goles,
  umbral_goles_bonus,
  puntos_bonus_victorias_consecutivas,
  umbral_victorias_consecutivas,
  puntos_penalizacion_tarjetas,
  umbral_tarjetas_penalizacion,
  prob_bonus_goles,
  prob_bonus_victorias,
  prob_penalizacion_tarjetas
)

# Ordenar combinaciones por número de victorias (ascendente)
# Priorizar combinaciones con menos victorias necesarias
if (length(combinaciones_validas) > 0) {
  # Extraer los valores de victorias y diferencias de puntos para ordenar
  victorias_vector <- sapply(combinaciones_validas, function(x) x$victorias)
  puntos_diff_vector <- sapply(combinaciones_validas, function(x) x$puntos_totales - puntos_necesarios)

  # Crear el índice de ordenamiento
  indices_ordenados <- order(victorias_vector, puntos_diff_vector, decreasing = FALSE)

  # Reordenar la lista de combinaciones
  combinaciones_ordenadas <- combinaciones_validas[indices_ordenados]
  
  # Extraer la combinación con el mínimo de victorias
  min_combinacion <- combinaciones_ordenadas[[1]]
  min_victorias <- min_combinacion$victorias
  min_empates <- min_combinacion$empates
  min_derrotas <- min_combinacion$derrotas
  min_bonificaciones <- min_combinacion$bonificaciones
  min_puntos_totales <- min_combinacion$puntos_totales
  
  # Extracción de escenarios específicos para análisis
  escenario_optimo <- min_combinacion
  
  # Escenario con más victorias pero menos bonificaciones
  victorias_vector_max <- sapply(combinaciones_validas, function(x) -x$victorias)  # Negativo para orden descendente
  bonif_vector_min <- sapply(combinaciones_validas, function(x) x$bonificaciones)
  indices_alta_victoria <- order(victorias_vector_max, bonif_vector_min, decreasing = FALSE)
  escenarios_alta_victoria <- combinaciones_validas[indices_alta_victoria]
  escenario_alta_victoria <- if(length(escenarios_alta_victoria) > 0) escenarios_alta_victoria[[1]] else escenario_optimo

  # Escenario con menos victorias pero más bonificaciones
  bonif_vector_max <- sapply(combinaciones_validas, function(x) -x$bonificaciones)  # Negativo para orden descendente
  victorias_vector_min <- sapply(combinaciones_validas, function(x) x$victorias)
  indices_alta_bonif <- order(bonif_vector_max, victorias_vector_min, decreasing = FALSE)
  escenarios_alta_bonificacion <- combinaciones_validas[indices_alta_bonif]
  escenario_alta_bonificacion <- if(length(escenarios_alta_bonificacion) > 0) escenarios_alta_bonificacion[[1]] else escenario_optimo

  # Escenario equilibrado (ni min victorias ni max bonificaciones)
  # Calcular las medianas primero
  victorias_mediana <- median(sapply(combinaciones_validas, function(x) x$victorias))
  bonificaciones_mediana <- median(sapply(combinaciones_validas, function(x) x$bonificaciones))

  # Calcular distancias a la mediana
  dist_victorias <- sapply(combinaciones_validas, function(x) abs(x$victorias - victorias_mediana))
  dist_bonificaciones <- sapply(combinaciones_validas, function(x) abs(x$bonificaciones - bonificaciones_mediana))

  # Ordenar por distancia mínima a la mediana
  indices_equilibrados <- order(dist_victorias, dist_bonificaciones)
  escenarios_equilibrados <- combinaciones_validas[indices_equilibrados]
  escenario_equilibrado <- if(length(escenarios_equilibrados) > 0) escenarios_equilibrados[[1]] else escenario_optimo
} else {
  # Si no hay combinaciones válidas, ajustamos parámetros y recalculamos
  puntos_necesarios <- round(max_puntos_sin_bonus * 0.5)
  
  combinaciones_validas <- calcular_combinaciones_avanzadas(
    partidos_por_equipo, 
    puntos_necesarios, 
    puntos_victoria, 
    puntos_empate, 
    puntos_derrota,
    puntos_bonus_goles,
    umbral_goles_bonus,
    puntos_bonus_victorias_consecutivas,
    umbral_victorias_consecutivas,
    puntos_penalizacion_tarjetas,
    umbral_tarjetas_penalizacion,
    prob_bonus_goles,
    prob_bonus_victorias,
    prob_penalizacion_tarjetas
  )
  
  # Extraer los valores de victorias y diferencias de puntos para ordenar
  victorias_vector <- sapply(combinaciones_validas, function(x) x$victorias)
  puntos_diff_vector <- sapply(combinaciones_validas, function(x) x$puntos_totales - puntos_necesarios)

  # Crear el índice de ordenamiento
  indices_ordenados <- order(victorias_vector, puntos_diff_vector, decreasing = FALSE)

  # Reordenar la lista de combinaciones
  combinaciones_ordenadas <- combinaciones_validas[indices_ordenados]
  
  min_combinacion <- combinaciones_ordenadas[[1]]
  min_victorias <- min_combinacion$victorias
  min_empates <- min_combinacion$empates
  min_derrotas <- min_combinacion$derrotas
  min_bonificaciones <- min_combinacion$bonificaciones
  min_puntos_totales <- min_combinacion$puntos_totales
  
  escenario_optimo <- min_combinacion
  escenario_alta_victoria <- min_combinacion
  escenario_alta_bonificacion <- min_combinacion
  escenario_equilibrado <- min_combinacion
}

# Definir la relación matemática que vincula las variables para la solución
# Este será el principio matemático que los estudiantes deben deducir
# Fórmula general: P = Vx + Ey + Dz + B - T
# donde P: puntos necesarios, V: victorias, E: empates, D: derrotas, B: bonificaciones, T: penalizaciones

# Para el problema, deben identificar la minimización de V sujeto a condiciones específicas
formula_matematica <- paste0("P_min = min(V) | (V * ", puntos_victoria,
                            " + E * ", puntos_empate,
                            " + D * ", puntos_derrota,
                            " + B - T >= ", puntos_necesarios,
                            " y V + E + D = ", partidos_por_equipo, ")")

# Explicación del razonamiento matemático complejo
explicacion_bonificaciones <- paste0(
  "Las bonificaciones juegan un papel crucial y pueden obtenerse: (1) ", puntos_bonus_goles, 
  " punto", if(puntos_bonus_goles != 1) "s" else "", " adicional", if(puntos_bonus_goles != 1) "es" else "", " por anotar al menos ", umbral_goles_bonus, 
  " goles en un partido; (2) ", puntos_bonus_victorias_consecutivas, 
  " punto", if(puntos_bonus_victorias_consecutivas != 1) "s" else "", " adicional", if(puntos_bonus_victorias_consecutivas != 1) "es" else "", " por conseguir ", umbral_victorias_consecutivas, 
  " victorias consecutivas; y (3) Una penalización de ", abs(puntos_penalizacion_tarjetas), 
  " punto", if(abs(puntos_penalizacion_tarjetas) != 1) "s" else "", " por acumular ", umbral_tarjetas_penalizacion, 
  " tarjetas en un mismo partido."
)

# Probabilidades asociadas para el análisis estadístico
prob_escenario_optimo <- prob_bonus_goles * (1 - prob_penalizacion_tarjetas) * 0.8
prob_escenario_alta_victoria <- (1 - prob_bonus_goles) * (1 - prob_penalizacion_tarjetas) * 0.6
prob_escenario_alta_bonificacion <- prob_bonus_goles * prob_bonus_victorias * (1 - prob_penalizacion_tarjetas) * 0.4
prob_escenario_equilibrado <- 1 - (prob_escenario_optimo + prob_escenario_alta_victoria + prob_escenario_alta_bonificacion)
if (prob_escenario_equilibrado < 0) prob_escenario_equilibrado <- 0.2

# Generación de opciones de respuesta
# Opción correcta: Mínimo de victorias considerando bonificaciones
respuesta_correcta <- min_victorias

# Distractor 1: Mínimo de victorias sin considerar bonificaciones (error conceptual)
min_victorias_sin_bonus <- ceiling((puntos_necesarios - (partidos_por_equipo - respuesta_correcta) * puntos_empate) / puntos_victoria)
if (min_victorias_sin_bonus == respuesta_correcta) min_victorias_sin_bonus <- respuesta_correcta + 1

# Distractor 2: Cálculo considerando un error en la fórmula de bonificaciones (confusión)
distractor_confusion <- ceiling((puntos_necesarios - min_bonificaciones/2) / puntos_victoria)
if (distractor_confusion == respuesta_correcta || distractor_confusion == min_victorias_sin_bonus) {
  distractor_confusion <- respuesta_correcta + 2
}

# Distractor 3: Valor que parece razonable pero es incorrecto (error en las restricciones)
distractor_restricciones <- floor((puntos_necesarios / puntos_victoria) * 0.85)
if (distractor_restricciones == respuesta_correcta || 
    distractor_restricciones == min_victorias_sin_bonus || 
    distractor_restricciones == distractor_confusion) {
  distractor_restricciones <- respuesta_correcta - 1
}
if (distractor_restricciones < 0) distractor_restricciones <- 1

# Verificar que todas las opciones sean diferentes
opciones_num <- c(respuesta_correcta, min_victorias_sin_bonus, distractor_confusion, distractor_restricciones)
while (length(unique(opciones_num)) < 4) {
  # Ajustar cualquier duplicado
  if (sum(opciones_num == min_victorias_sin_bonus) > 1) min_victorias_sin_bonus <- min_victorias_sin_bonus + 1
  if (sum(opciones_num == distractor_confusion) > 1) distractor_confusion <- distractor_confusion + 2
  if (sum(opciones_num == distractor_restricciones) > 1) distractor_restricciones <- distractor_restricciones - 1
  if (distractor_restricciones < 0) distractor_restricciones <- respuesta_correcta + 3
  
  opciones_num <- c(respuesta_correcta, min_victorias_sin_bonus, distractor_confusion, distractor_restricciones)
}

# Colores para las tablas tikz
colores_disponibles <- c("red", "blue", "green", "orange", "purple", "teal", "brown", "gray")
color_fondo_header <- sample(colores_disponibles, 1)
intensidad_header <- sample(c(15, 20, 25, 30), 1)
color_header <- paste0(color_fondo_header, "!", intensidad_header)

color_fondo_filas <- sample(colores_disponibles[colores_disponibles != color_fondo_header], 1)
intensidad_filas <- sample(c(10, 15, 20), 1)
color_filas <- paste0(color_fondo_filas, "!", intensidad_filas)

# Vector de solución para r-exams
solucion <- c(1, 0, 0, 0)
```

```{r tablas_tikz, message=FALSE, warning=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Función para generar tabla con sistema de puntuación básico
generar_tabla_puntuacion_basica <- function(puntos_victoria, puntos_empate, puntos_derrota, color_header, color_filas) {
  tabla_code <- c("\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|c|c|}",
    "    \\hline",
    paste0("    \\rowcolor{", color_header, "}"),
    paste0("    \\multicolumn{2}{|c|}{\\textbf{Sistema de puntuación básico}} \\\\"),
    "    \\hline",
    paste0("    \\rowcolor{", color_filas, "}"),
    paste0("    Victoria & ", puntos_victoria, " puntos \\\\"),
    "    \\hline",
    paste0("    Empate & ", puntos_empate, " punto", if(puntos_empate != 1) "s" else "", " \\\\"),
    "    \\hline",
    paste0("    Derrota & ", puntos_derrota, " punto", if(puntos_derrota != 1) "s" else "", " \\\\"),
    "    \\hline",
    "  \\end{tabular}",
    "};",
    "\\end{tikzpicture}")
  
  return(tabla_code)
}

# Función para generar tabla con sistema de bonificaciones
generar_tabla_bonificaciones <- function(puntos_bonus_goles, umbral_goles_bonus, 
                                       puntos_bonus_victorias_consecutivas, umbral_victorias_consecutivas,
                                       puntos_penalizacion_tarjetas, umbral_tarjetas_penalizacion,
                                       color_header, color_filas) {
  tabla_code <- c("\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|p{7cm}|c|}",
    "    \\hline",
    paste0("    \\rowcolor{", color_header, "}"),
    paste0("    \\multicolumn{2}{|c|}{\\textbf{Sistema de bonificaciones}} \\\\"),
    "    \\hline",
    paste0("    \\rowcolor{", color_filas, "}"),
    paste0("    Anotar ", umbral_goles_bonus, " o más goles en un partido & +", puntos_bonus_goles, " punto", if(puntos_bonus_goles != 1) "s" else "", " \\\\"),
    "    \\hline",
    paste0("    Conseguir ", umbral_victorias_consecutivas, " victorias consecutivas & +", puntos_bonus_victorias_consecutivas, " punto", if(puntos_bonus_victorias_consecutivas != 1) "s" else "", " \\\\"),
    "    \\hline",
    paste0("    Acumular ", umbral_tarjetas_penalizacion, " tarjetas en un partido & ", puntos_penalizacion_tarjetas, " punto", if(abs(puntos_penalizacion_tarjetas) != 1) "s" else "", " \\\\"),
    "    \\hline",
    "  \\end{tabular}",
    "};",
    "\\end{tikzpicture}")
  
  return(tabla_code)
}

# Función para generar tabla con escenario óptimo
generar_tabla_escenario <- function(victorias, empates, derrotas, puntos_base, 
                                  bonificaciones, puntos_total, color_header, color_filas) {
  tabla_code <- c("\\begin{tikzpicture}",
    "\\node[inner sep=0pt] {",
    "  \\begin{tabular}{|c|c|c|c|c|c|}",
    "    \\hline",
    paste0("    \\rowcolor{", color_header, "}"),
    "    \\textbf{Victorias} & \\textbf{Empates} & \\textbf{Derrotas} & \\textbf{Puntos base} & \\textbf{Bonif.} & \\textbf{Total} \\\\",
    "    \\hline",
    paste0("    \\rowcolor{", color_filas, "}"),
    paste0("    ", victorias, " & ", empates, " & ", derrotas, " & ", puntos_base, " & ", bonificaciones, " & ", puntos_total, " \\\\"),
    "    \\hline",
    "  \\end{tabular}",
    "};",
    "\\end{tikzpicture}")
  
  return(tabla_code)
}

# Generar tablas para el problema
tabla_puntuacion_basica <- generar_tabla_puntuacion_basica(
  puntos_victoria, 
  puntos_empate, 
  puntos_derrota, 
  color_header, 
  color_filas
)

tabla_bonificaciones <- generar_tabla_bonificaciones(
  puntos_bonus_goles,
  umbral_goles_bonus,
  puntos_bonus_victorias_consecutivas,
  umbral_victorias_consecutivas,
  puntos_penalizacion_tarjetas,
  umbral_tarjetas_penalizacion,
  color_header,
  color_filas
)

tabla_escenario_optimo <- generar_tabla_escenario(
  escenario_optimo$victorias,
  escenario_optimo$empates,
  escenario_optimo$derrotas,
  escenario_optimo$puntos_base,
  escenario_optimo$bonificaciones,
  escenario_optimo$puntos_totales,
  color_header,
  color_filas
)

# Tabla para escenario alta victoria
tabla_escenario_alta_victoria <- generar_tabla_escenario(
  escenario_alta_victoria$victorias,
  escenario_alta_victoria$empates,
  escenario_alta_victoria$derrotas,
  escenario_alta_victoria$puntos_base,
  escenario_alta_victoria$bonificaciones,
  escenario_alta_victoria$puntos_totales,
  color_header,
  color_filas
)

# Tabla para escenario alta bonificación
tabla_escenario_alta_bonificacion <- generar_tabla_escenario(
  escenario_alta_bonificacion$victorias,
  escenario_alta_bonificacion$empates,
  escenario_alta_bonificacion$derrotas,
  escenario_alta_bonificacion$puntos_base,
  escenario_alta_bonificacion$bonificaciones,
  escenario_alta_bonificacion$puntos_totales,
  color_header,
  color_filas
)
```

Question
========

Un `r competicion` `r region` de `r deporte` `r tipo_competicion` ha implementado un sistema de puntuación innovador. Para clasificar a la `r fase_siguiente`, un equipo debe acumular al menos **`r puntos_necesarios` puntos** durante la `r fase_inicial`, donde cada equipo disputa exactamente `r partidos_por_equipo` partidos.

El sistema de puntuación consta de dos componentes:
\

**Sistema de puntuación básico:**

```{r tabla_puntuacion_basica, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la tabla de puntuación básica usando TikZ
include_tikz(tabla_puntuacion_basica, 
             name = "tabla_puntuacion_basica", 
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "7cm")
```

**Sistema de bonificaciones:**

```{r tabla_bonificaciones, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la tabla de bonificaciones usando TikZ
include_tikz(tabla_bonificaciones, 
             name = "tabla_bonificaciones", 
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "10cm")
```

Los analistas deportivos, tomando en cuenta el rendimiento histórico del equipo, estiman que en promedio:

- Anotarán `r promedio_goles_por_partido` goles por partido
- Recibirán `r promedio_tarjetas_por_partido` tarjetas por partido
- La probabilidad de obtener el bonus por victorias consecutivas es del `r round(prob_bonus_victorias*100)` por ciento

Suponiendo que el equipo optimiza sus resultados para clasificar con el mínimo esfuerzo, ¿cuál es el número mínimo de partidos que necesita ganar para asegurar la clasificación a la `r fase_siguiente`?

Answerlist
----------
- `r respuesta_correcta` partidos
- `r min_victorias_sin_bonus` partidos
- `r distractor_confusion` partidos
- `r distractor_restricciones` partidos

Solution
========

La respuesta correcta es **`r respuesta_correcta` partidos**.

Para resolver este problema de optimización con restricciones, debemos identificar la estrategia que minimiza el número de victorias necesarias para clasificar, considerando el sistema de puntuación complejo que incluye bonificaciones y penalizaciones.

### Enfoque analítico:

Primero, planteamos el modelo matemático. Necesitamos encontrar el valor mínimo de $V$ (victorias) tal que:

$$(V \times `r puntos_victoria`) + (E \times `r puntos_empate`) + (D \times `r puntos_derrota`) + B - P \ge `r puntos_necesarios`$$

Con las restricciones:

1. $V + E + D = `r partidos_por_equipo`$ (balance de partidos)
2. $V, E, D \geq 0$ (no negatividad)
3. $B$ representa bonificaciones y $P$ penalizaciones (ambas variables aleatorias)

### Análisis probabilístico de bonificaciones y penalizaciones:

Dadas las estadísticas del equipo:

- Promedio de `r promedio_goles_por_partido` goles por partido
- Probabilidad de obtener bonificación por goles: aproximadamente `r round(prob_bonus_goles*100)`%
- Promedio de `r promedio_tarjetas_por_partido` tarjetas por partido
- Probabilidad de recibir penalización: aproximadamente `r round(prob_penalizacion_tarjetas*100)`%
- Probabilidad de bonus por victorias consecutivas: aproximadamente `r round(prob_bonus_victorias*100)`%

### Análisis combinatorio para encontrar la solución óptima:

Evaluando sistemáticamente todas las combinaciones posibles de $(V,E,D)$ que cumplen $V + E + D = `r partidos_por_equipo`$, y calculando para cada una la puntuación esperada incluyendo bonificaciones, encontramos que el escenario óptimo es:

```{r tabla_escenario_optimo, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la tabla con el escenario óptimo
include_tikz(tabla_escenario_optimo, 
             name = "tabla_escenario_optimo", 
             markup = "markdown",
             format = typ, 
             packages = c("tikz", "colortbl"),
             width = "12cm")
```

Con **`r escenario_optimo$victorias` victorias**, `r escenario_optimo$empates` empates y `r escenario_optimo$derrotas` derrotas, se obtienen:

- Puntos base: $(`r escenario_optimo$victorias` \times `r puntos_victoria`) + (`r escenario_optimo$empates` \times `r puntos_empate`) + (`r escenario_optimo$derrotas` \times `r puntos_derrota`) = `r escenario_optimo$puntos_base`$ puntos
- Bonificaciones netas esperadas: `r escenario_optimo$bonificaciones` puntos
- Total: `r escenario_optimo$puntos_totales` puntos (mayor o igual a `r puntos_necesarios` puntos requeridos)

### Verificación de escenarios alternativos:

1. **Escenario con mayor dependencia de victorias:**

```{r tabla_escenario_alta_victoria, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la tabla con escenario de alta victoria
include_tikz(tabla_escenario_alta_victoria, 
             name = "tabla_escenario_alta_victoria", 
             markup = "markdown",
             format = typ, 
             packages = c("tikz", "colortbl"),
             width = "12cm")
```

2. **Escenario con mayor dependencia de bonificaciones:**

```{r tabla_escenario_alta_bonificacion, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la tabla con escenario de alta bonificación
include_tikz(tabla_escenario_alta_bonificacion, 
             name = "tabla_escenario_alta_bonificacion", 
             markup = "markdown",
             format = typ, 
             packages = c("tikz", "colortbl"),
             width = "12cm")
```

### Análisis de probabilidades para cada escenario:

La probabilidad de éxito con cada estrategia es:

- Escenario óptimo: `r round(prob_escenario_optimo*100)`%
- Escenario alta victoria: `r round(prob_escenario_alta_victoria*100)`%
- Escenario alta bonificación: `r round(prob_escenario_alta_bonificacion*100)`%

El escenario óptimo minimiza el número de victorias necesarias y maximiza la probabilidad de éxito, por lo que la respuesta correcta es 

**`r respuesta_correcta` partidos**.

### Análisis de distractores:

b. `r min_victorias_sin_bonus` partidos: Este valor resulta de no considerar las bonificaciones en el cálculo.

c. `r distractor_confusion` partidos: Resultado de una aplicación incorrecta de la fórmula de bonificaciones, donde se subestima su efecto.

d. `r distractor_restricciones` partidos: Este valor surge de un error en la interpretación de las restricciones del problema, donde se considera un porcentaje arbitrario del total de puntos necesarios.

Answerlist
----------
- Verdadero
- Falso
- Falso
- Falso

Meta-information
================
exname: clasificacion_torneo_futbol_v3
extype: schoice
exsolution: 1000
exshuffle: TRUE
exsection: Análisis combinatorio|Probabilidad condicional|Investigación de operaciones
