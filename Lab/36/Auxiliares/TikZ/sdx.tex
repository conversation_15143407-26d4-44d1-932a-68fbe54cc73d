\documentclass[border=0.2cm]{standalone}

\usepackage{tikz}
\usetikzlibrary{decorations.pathmorphing,arrows.meta,calc,positioning}

\begin{document}
	
	\begin{tikzpicture}
		
		% Definir las dimensiones del cilindro
		\def\radiointerno{1} % Radio interno = 1m
		\def\grosor{0.5} % Grosor = 0,5m
		\def\radioexterno{\radiointerno+\grosor} % Radio externo = 1,5m
		\def\altura{4} % Altura del cilindro
		
		% Dibujar la parte superior del cilindro (elipses)
		% Círculo exterior superior
		\draw (0,\altura) ellipse (\radioexterno and \radioexterno*0.4);
		% Círculo interior superior
		\draw (0,\altura) ellipse (\radiointerno and \radiointerno*0.4);
		
		% Dibujar las líneas verticales del cilindro
		% Línea vertical externa izquierda
		\draw (-\radioexterno,\altura) -- (-\radioexterno,0);
		% Línea vertical externa derecha
		\draw (\radioexterno,\altura) -- (\radioexterno,0);
		% Línea vertical interna izquierda (punteada)
		\draw[dashed] (-\radiointerno,\altura) -- (-\radiointerno,0);
		% Línea vertical interna derecha (punteada)
		\draw[dashed] (\radiointerno,\altura) -- (\radiointerno,0);
		
		% Dibujar la parte inferior del cilindro (elipses)
		% Círculo exterior inferior (punteado)
		\draw[dashed] (0,0) ellipse (\radioexterno and \radioexterno*0.4);
		% Círculo interior inferior (punteado)
		\draw[dashed] (0,0) ellipse (\radiointerno and \radiointerno*0.4);
		
		% Añadir un punto central en la parte superior
		\fill (0,\altura) circle (1.5pt);
		% Añadir un punto central en la línea media superior
		\fill (\radiointerno,\altura) circle (1pt);
		\fill (\radioexterno,\altura) circle (1pt);
		\fill (-\radiointerno,\altura) circle (1pt);
		\fill (-\radioexterno,\altura) circle (1pt);
		
		% Añadir un punto central en la parte inferior
		\fill (0,0) circle (1.5pt);
		
		% Etiquetas
		% Diámetro externo
		\draw[<->] (-\radioexterno*1.2,\altura+\radioexterno*0.6) -- (\radioexterno*1.2,\altura+\radioexterno*0.6) 
		node[midway, above] {Diámetro externo};
		
		% Grosor
		\draw[->,red!60!orange] (-\radioexterno+0.1,\altura+0.3) -- (-\radiointerno-0.1,\altura+0.3);
		\node[red!60!orange, above left] at (-\radiointerno-0.3,\altura+0.3) {Grosor = 0,5 m};
		
		% Radio interno
		\draw[->,red!60!orange] (\radiointerno-0.1,\altura+0.3) -- (0.1,\altura+0.3);
		\node[red!60!orange, above right] at (0.1,\altura+0.3) {Radio interno = 1 m};
		
		% Radio externo
		\draw[->,blue] (0.1,0-0.1) -- (\radioexterno-0.1,0-0.1);
		\node[blue, below] at (\radiointerno,0-0.3) {Radio externo};
		
		% Altura
		\draw[<->,blue] (\radioexterno+0.5,0) -- (\radioexterno+0.5,\altura) 
		node[midway, right] {Altura};
		
	\end{tikzpicture}
	
\end{document}
