---
output:
  html_document: default
  word_document: default
  pdf_document: default
---

# Metadatos ICFES
icfes:
  competencia:
    - formulacion_ejecucion
  nivel_dificultad: 3
  contenido:
    categoria: geometria
    tipo: no_generico
  contexto: matematico
  eje_axial: eje2
  componente: geometrico_metrico

```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage[utf8]{inputenc}",
  "\\usepackage[T1]{fontenc}",
  "\\usepackage{amsmath,amssymb}",
  "\\usetikzlibrary{calc}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Aleatorizamos los nombres para contextualizar el problema
nombres <- c("Camilo", "Andrés", "Sofía", "Manuel", "Laura", "Carlos",
             "Daniela", "Miguel", "Valentina", "Eduardo", "Natalia",
             "José", "Isabella", "Gabriel", "Mariana", "Santiago", "Lucía",
             "Alejandro", "Catalina", "Mateo", "Valeria", "Sebastián", "Juliana")
nombre <- sample(nombres, 1)

# Aleatorizamos los tipos de recipientes
recipientes <- c("cilindro", "tubo", "conducto", "tanque cilíndrico",
                 "recipiente cilíndrico", "contenedor cilíndrico",
                 "ducto cilíndrico", "depósito cilíndrico", "cañería cilíndrica")
recipiente <- sample(recipientes, 1)

# Aleatorizar adjetivos para el cilindro interno
adjetivos_interno <- c("interno", "hueco", "vacío", "interior", "central", "medio")
adjetivo_interno <- sample(adjetivos_interno, 1)

# Aleatorizar líquidos
liquidos <- c("aceite", "combustible", "líquido", "fluido", "agua", "refrigerante",
              "solución", "mezcla", "sustancia")
liquido <- sample(liquidos, 1)

# Aleatorizar lo que se desea calcular
calculos <- c("la cantidad de", "el volumen de", "cuánto", "qué cantidad de",
              "cuántos litros de", "qué volumen de", "la capacidad de")
calculo <- sample(calculos, 1)

# Aleatorizar verbos para la acción
verbos <- c("llenar", "contener", "almacenar", "ocupar", "requerir")
verbo <- sample(verbos, 1)

# Aleatorizar medidas del cilindro (manteniendo consistencia matemática)
# Primero generamos el radio interno (entre 0.1 y 0.4 metros)
r_int <- round(runif(1, 0.1, 0.4), 2)

# Luego generamos el radio externo, asegurando que sea mayor que el interno
# Radio externo entre 1.5 y 2.5 veces el radio interno
factor_radio <- runif(1, 1.5, 2.5)
r_ext <- round(r_int * factor_radio, 2)

# Calculamos el diámetro externo (exactamente 2 veces el radio externo)
d_ext <- 2 * r_ext

# Altura del cilindro (entre 0.5 y 4 metros)
altura <- round(runif(1, 0.5, 4), 2)

# Calcular el grosor de la pared del cilindro
grosor <- r_ext - r_int

# Verificar coherencia física
if (grosor < 0.05) {
  # Asegurar un grosor mínimo de 0.05 unidades
  grosor <- round(runif(1, 0.05, 0.2), 2)
  r_ext <- r_int + grosor
  # Asegurar que el diámetro externo sea exactamente 2 veces el radio externo
  d_ext <- 2 * r_ext
}

# Calcular el volumen necesario (es el volumen del cilindro interior)
volumen <- round(pi * (r_int^2) * altura, 2)

# Aleatorizar unidades de medida
unidades_longitud <- c("m", "cm", "dm")
unidad <- sample(unidades_longitud, 1)

# Ajustar valores según la unidad
factor_conversion <- 1
if (unidad == "cm") {
  factor_conversion <- 100
  d_ext <- d_ext * factor_conversion
  r_int <- r_int * factor_conversion
  altura <- altura * factor_conversion
  volumen <- volumen * (factor_conversion^3)
  r_ext <- r_ext * factor_conversion
} else if (unidad == "dm") {
  factor_conversion <- 10
  d_ext <- d_ext * factor_conversion
  r_int <- r_int * factor_conversion
  altura <- altura * factor_conversion
  volumen <- volumen * (factor_conversion^3)
  r_ext <- r_ext * factor_conversion
}

# Redondear todos los valores para mayor claridad
d_ext <- round(d_ext, 1)
r_int <- round(r_int, 1)
altura <- round(altura, 1)
r_ext <- round(r_ext, 1)
volumen <- round(volumen, 1)

# La respuesta correcta es "C) Altura del cilindro"
# Ya que para calcular el volumen necesitaríamos conocer la altura
opcion_correcta <- 3 # Índice de "Altura del cilindro" en el vector de opciones

# Vector de solución para r-exams (1 para la correcta, 0 para las incorrectas)
solucion <- c(0, 0, 1, 0)
```

```{r generar_cilindro_r, message=FALSE, warning=FALSE, fig.width=8, fig.height=8}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Crear una función para dibujar el cilindro hueco usando R base
dibujar_cilindro_hueco <- function(r_int, r_ext, altura, unidad) {
  # Configurar el gráfico
  par(mar = c(2, 2, 2, 2))
  plot(0, 0, type = "n", xlim = c(-r_ext*1.5, r_ext*1.5), ylim = c(-r_ext*0.5, altura*1.2),
       xlab = "", ylab = "", axes = FALSE)

  # Factor de compresión para la perspectiva
  factor_compresion <- 0.4

  # Dibujar el cilindro externo
  # Base inferior (parte trasera - línea punteada)
  theta <- seq(0, pi, length.out = 100)
  x_ext_inf_tras <- r_ext * cos(theta)
  y_ext_inf_tras <- r_ext * factor_compresion * sin(theta)
  lines(x_ext_inf_tras, y_ext_inf_tras, lty = 2)

  # Base inferior (parte delantera - línea sólida)
  theta <- seq(pi, 2*pi, length.out = 100)
  x_ext_inf_del <- r_ext * cos(theta)
  y_ext_inf_del <- r_ext * factor_compresion * sin(theta)
  lines(x_ext_inf_del, y_ext_inf_del)

  # Base superior (elipse completa)
  theta <- seq(0, 2*pi, length.out = 200)
  x_ext_sup <- r_ext * cos(theta)
  y_ext_sup <- altura + r_ext * factor_compresion * sin(theta)
  lines(x_ext_sup, y_ext_sup)

  # Laterales externos
  lines(c(-r_ext, -r_ext), c(0, altura))
  lines(c(r_ext, r_ext), c(0, altura))

  # Dibujar el cilindro interno
  # Base inferior interna (parte trasera - línea punteada)
  theta <- seq(0, pi, length.out = 100)
  x_int_inf_tras <- r_int * cos(theta)
  y_int_inf_tras <- r_int * factor_compresion * sin(theta)
  lines(x_int_inf_tras, y_int_inf_tras, lty = 2)

  # Base inferior interna (parte delantera - línea sólida)
  theta <- seq(pi, 2*pi, length.out = 100)
  x_int_inf_del <- r_int * cos(theta)
  y_int_inf_del <- r_int * factor_compresion * sin(theta)
  lines(x_int_inf_del, y_int_inf_del)

  # Base superior interna (elipse completa)
  theta <- seq(0, 2*pi, length.out = 200)
  x_int_sup <- r_int * cos(theta)
  y_int_sup <- altura + r_int * factor_compresion * sin(theta)
  lines(x_int_sup, y_int_sup)

  # Laterales internos (líneas punteadas)
  lines(c(-r_int, -r_int), c(0, altura), lty = 2)
  lines(c(r_int, r_int), c(0, altura), lty = 2)

  # Añadir etiquetas y medidas
  # Altura
  arrows(r_ext + r_ext*0.2, 0, r_ext + r_ext*0.2, altura, code = 3, length = 0.1)
  text(r_ext + r_ext*0.4, altura/2, "Altura", pos = 4)

  # Radio interno
  arrows(0, altura, r_int, altura, code = 2, length = 0.1)
  text(r_int/2, altura + r_ext*0.2, paste("Radio interno =", r_int, unidad))

  # Radio externo
  arrows(0, 0, r_ext, 0, code = 2, length = 0.1)
  text(r_ext/2, -r_ext*0.2, paste("Radio externo =", r_ext, unidad))

  # Diámetro externo
  arrows(-r_ext, altura + r_ext*0.5, r_ext, altura + r_ext*0.5, code = 3, length = 0.1)
  text(0, altura + r_ext*0.7, paste("Diámetro externo =", 2*r_ext, unidad))

  # Grosor
  arrows(-r_ext, altura/2, -r_int, altura/2, code = 3, length = 0.1)
  text(-r_ext - r_ext*0.2, altura/2, paste("Grosor =", r_ext - r_int, unidad), pos = 2)
}

# Guardar la imagen del cilindro hueco
png("cilindro_hueco.png", width = 800, height = 800, res = 120)
dibujar_cilindro_hueco(r_int, r_ext, altura, unidad)
dev.off()
```

Question
========

`r nombre` desea saber cuánto(a) `r liquido` se necesita para llenar un `r recipiente` interno, pero solamente cuenta con las medidas de las dimensiones que muestra la figura.

```{r mostrar_cilindro, echo=FALSE, results='asis', fig.align='center'}
# Mostrar la imagen del cilindro generada con R
cat("![](cilindro_hueco.png)")
```

```{r generar_tabla_tikz, echo=FALSE, results='asis'}
# Aleatorizar colores de la tabla
color_fondo_tabla <- sample(c("orange", "blue", "green", "cyan"), 1)
intensidad_color <- sample(c(10, 15, 20, 25), 1)
color_tabla <- paste0(color_fondo_tabla, "!", intensidad_color)

# Crear tabla con TikZ
tabla_tikz <- c(
  "\\begin{tikzpicture}",
  "\\node[inner sep=0pt] {",
  "  \\begin{tabular}{|c|c|c|}",
  "    \\hline",
  paste0("    \\rowcolor{", color_tabla, "}"),
  "    \\textbf{Dimensión} & \\textbf{Valor} & \\textbf{Unidad} \\\\",
  "    \\hline",
  paste0("    Radio interno & ", r_int, " & ", unidad, " \\\\"),
  "    \\hline",
  paste0("    Radio externo & ", r_ext, " & ", unidad, " \\\\"),
  "    \\hline",
  paste0("    Diámetro externo & ", d_ext, " & ", unidad, " \\\\"),
  "    \\hline",
  paste0("    Grosor & ", r_ext - r_int, " & ", unidad, " \\\\"),
  "    \\hline",
  "  \\end{tabular}",
  "};",
  "\\end{tikzpicture}"
)

# Incluir la tabla TikZ
include_tikz(tabla_tikz,
             name = "tabla_datos",
             markup = "markdown",
             format = typ,
             packages = c("tikz", "colortbl"),
             width = "8cm")
```

¿Cuál medida le falta a `r nombre` para hallar la cantidad deseada?

Answerlist
----------
- Radio externo.
- Diámetro externo.
- Altura del cilindro.
- Perímetro del cilindro.

Solution
========

La respuesta correcta es: **Altura del cilindro**.

Para calcular el volumen de `r liquido` necesario para llenar el `r recipiente` `r adjetivo_interno`, necesitamos calcular el volumen de un cilindro, cuya fórmula es:

$V = \pi \cdot r^2 \cdot h$

Donde:

- $r$ es el radio (en este caso, el radio interno = `r r_int` `r unidad`)
- $h$ es la altura del cilindro

En el problema se nos proporciona:

- Diámetro externo = `r d_ext` `r unidad`
- Radio interno = `r r_int` `r unidad`

Sin embargo, no se nos proporciona la altura del cilindro, que es esencial para calcular el volumen. Por lo tanto, a `r nombre` le falta conocer la altura del cilindro para poder calcular la cantidad de `r liquido` necesaria.

Si tuviéramos la altura, el volumen se calcularía así:

$V = \pi \cdot (`r r_int`)^2 \cdot h = `r round(pi * (r_int^2), 2)` \cdot h$ `r unidad`³

Answerlist
----------
- Falso
- Falso
- Verdadero
- Falso

Meta-information
================
exname: volumen_cilindro_hueco
extype: schoice
exsolution: 0010
exshuffle: TRUE
exsection: Geometría|Volumen|Cilindro
