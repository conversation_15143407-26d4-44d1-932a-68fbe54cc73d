---
output:
  html_document: default
  word_document: default
  pdf_document:
    keep_tex: true
    extra_dependencies: ["graphicx", "float"]
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usepackage{graphicx}",
  "\\usepackage{float}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)
library(knitr)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150,
  fig.pos = "H"
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Aleatorización del contexto del problema
contextos <- c(
  "empresa", "organización", "compañía", "institución", "corporación",
  "entidad", "firma", "negocio", "sociedad", "grupo empresarial"
)
contexto <- sample(contextos, 1)

# Aleatorización de los tipos de bienes
tipos_bien1 <- c("carro", "auto", "vehículo", "automóvil", "coche")
tipos_bien2 <- c("casa", "vivienda", "residencia", "hogar", "domicilio")
tipos_bien3 <- c("apartamento", "departamento", "piso", "condominio")

bien1 <- sample(tipos_bien1, 1)
bien2 <- sample(tipos_bien2, 1)
bien3 <- sample(tipos_bien3, 1)

# Aleatorización de términos para el enunciado
terminos_encuesta <- c("encuesta", "sondeo", "estudio", "investigación", "consulta")
termino_encuesta <- sample(terminos_encuesta, 1)

terminos_personas <- c("personas", "empleados", "trabajadores", "colaboradores", "miembros")
termino_personas <- sample(terminos_personas, 1)

terminos_bienes <- c("bienes", "posesiones", "propiedades", "activos", "patrimonios")
termino_bienes <- sample(terminos_bienes, 1)

terminos_resultados <- c("resultados", "datos", "estadísticas", "cifras")
termino_resultados <- sample(terminos_resultados, 1)

# Aleatorización de colores para el gráfico circular (múltiples estilos)
paleta_colores <- list(
  # Paletas oscuras (originales)
  c("#C62828", "#2E7D32", "#1565C0", "#EF6C00", "#6A1B9A"),  # Paleta oscura 1
  c("#D32F2F", "#388E3C", "#1976D2", "#F57C00", "#7B1FA2"),  # Paleta saturada
  c("#B71C1C", "#1B5E20", "#0D47A1", "#E65100", "#4A148C"),  # Paleta oscura 2
  c("#AD1457", "#00695C", "#283593", "#FF6F00", "#4A148C"),  # Paleta oscura 3
  c("#880E4F", "#1B5E20", "#0D47A1", "#BF360C", "#4A148C"),  # Paleta oscura 4

  # Paletas brillantes
  c("#FF5252", "#69F0AE", "#448AFF", "#FFAB40", "#E040FB"),  # Brillante 1
  c("#FF1744", "#00E676", "#2979FF", "#FF9100", "#D500F9"),  # Brillante 2
  c("#F44336", "#4CAF50", "#2196F3", "#FF9800", "#9C27B0"),  # Brillante 3

  # Paletas pastel
  c("#FFCDD2", "#C8E6C9", "#BBDEFB", "#FFE0B2", "#E1BEE7"),  # Pastel 1
  c("#EF9A9A", "#A5D6A7", "#90CAF9", "#FFCC80", "#CE93D8"),  # Pastel 2
  c("#FFAB91", "#80CBC4", "#9FA8DA", "#FFE082", "#F48FB1"),  # Pastel 3

  # Paletas monocromáticas
  c("#B71C1C", "#D32F2F", "#F44336", "#E57373", "#FFCDD2"),  # Rojos
  c("#1B5E20", "#2E7D32", "#4CAF50", "#81C784", "#C8E6C9"),  # Verdes
  c("#0D47A1", "#1976D2", "#2196F3", "#64B5F6", "#BBDEFB"),  # Azules

  # Paletas contrastantes
  c("#E53935", "#43A047", "#1E88E5", "#FB8C00", "#8E24AA"),  # Contraste 1
  c("#C2185B", "#00897B", "#303F9F", "#FFB300", "#6A1B9A"),  # Contraste 2
  c("#D81B60", "#00BFA5", "#3949AB", "#FFA000", "#7B1FA2"),  # Contraste 3

  # Paletas temáticas
  c("#004D40", "#00796B", "#009688", "#4DB6AC", "#B2DFDB"),  # Turquesa
  c("#311B92", "#512DA8", "#673AB7", "#9575CD", "#D1C4E9"),  # Púrpura
  c("#BF360C", "#E64A19", "#FF5722", "#FF8A65", "#FFCCBC")   # Naranja
)

# Opción para aleatorizar colores individualmente o usar una paleta predefinida
modo_color <- sample(c("paleta", "individual"), 1, prob = c(0.7, 0.3))

if (modo_color == "paleta") {
  # Seleccionar una paleta completa
  paleta_seleccionada <- sample(paleta_colores, 1)[[1]]
} else {
  # Crear una paleta personalizada seleccionando colores individuales
  # Definir grupos de colores por tonalidad
  rojos <- c("#B71C1C", "#C62828", "#D32F2F", "#E53935", "#F44336", "#EF5350", "#E57373", "#FF5252", "#FF1744")
  verdes <- c("#1B5E20", "#2E7D32", "#388E3C", "#43A047", "#4CAF50", "#66BB6A", "#81C784", "#69F0AE", "#00E676")
  azules <- c("#0D47A1", "#1565C0", "#1976D2", "#1E88E5", "#2196F3", "#42A5F5", "#64B5F6", "#448AFF", "#2979FF")
  naranjas <- c("#BF360C", "#E64A19", "#F57C00", "#FB8C00", "#FF9800", "#FFA726", "#FFB74D", "#FFAB40", "#FF9100")
  purpuras <- c("#4A148C", "#6A1B9A", "#7B1FA2", "#8E24AA", "#9C27B0", "#AB47BC", "#BA68C8", "#E040FB", "#D500F9")

  # Seleccionar un color aleatorio de cada grupo
  paleta_seleccionada <- c(
    sample(rojos, 1),
    sample(verdes, 1),
    sample(azules, 1),
    sample(naranjas, 1),
    sample(purpuras, 1)
  )

  # Opcionalmente mezclar el orden de los colores
  if (sample(c(TRUE, FALSE), 1, prob = c(0.7, 0.3))) {
    paleta_seleccionada <- sample(paleta_seleccionada)
  }
}

# Aleatorización de los porcentajes manteniendo coherencia matemática
# Generación de porcentajes iniciales para cada categoría
set_porcentajes <- function() {
  repeat {
    # Generar valores base aleatorios
    p_bien1_bien3 <- sample(15:35, 1)  # Porcentaje de bien1 y bien3
    p_solo_bien3 <- sample(15:30, 1)   # Porcentaje solo bien3
    p_solo_bien2 <- sample(25:40, 1)   # Porcentaje solo bien2
    p_solo_bien1 <- sample(5:10, 1)    # Porcentaje solo bien1

    # Calcular el porcentaje restante para bien1 y bien2
    p_bien1_bien2 <- 100 - (p_bien1_bien3 + p_solo_bien3 + p_solo_bien2 + p_solo_bien1)

    # Validar que el porcentaje restante sea positivo y razonable
    if (p_bien1_bien2 >= 10 && p_bien1_bien2 <= 25) {
      return(c(p_bien1_bien3, p_solo_bien3, p_solo_bien2, p_solo_bien1, p_bien1_bien2))
    }
  }
}

# Obtener porcentajes válidos
porcentajes_base <- set_porcentajes()

p_bien1_bien3 <- porcentajes_base[1]  # Porcentaje de bien1 y bien3
p_solo_bien3 <- porcentajes_base[2]   # Porcentaje solo bien3
p_solo_bien2 <- porcentajes_base[3]   # Porcentaje solo bien2
p_solo_bien1 <- porcentajes_base[4]   # Porcentaje solo bien1
p_bien1_bien2 <- porcentajes_base[5]  # Porcentaje de bien1 y bien2

# Guardar los porcentajes originales para visualización
porcentajes_grafico <- c(p_bien1_bien3, p_solo_bien3, p_solo_bien2, p_solo_bien1, p_bien1_bien2)

# Verificar que suman 100%
test_that("Los porcentajes suman 100%", {
  expect_equal(sum(porcentajes_base), 100)
})

# ENFOQUE MEJORADO: Primero determinamos el total y luego las categorías sin redondear
# Número total de personas - elegimos un número que sea divisible por 100 para evitar errores de redondeo
total_personas <- sample(c(500, 600, 700, 800, 900, 1000, 1200, 1500, 2000), 1)

# Calculamos la cantidad exacta de personas para cada categoría (sin redondeo)
# Usamos división por enteros para evitar problemas decimales
personas_exactas <- list(
  bien1_bien3 = (total_personas * p_bien1_bien3) %/% 100,
  solo_bien3 = (total_personas * p_solo_bien3) %/% 100,
  solo_bien2 = (total_personas * p_solo_bien2) %/% 100,
  solo_bien1 = (total_personas * p_solo_bien1) %/% 100,
  bien1_bien2 = (total_personas * p_bien1_bien2) %/% 100
)

# Calculamos el residuo (personas no asignadas por la división entera)
personas_no_asignadas <- total_personas - sum(unlist(personas_exactas))

# Distribuimos las personas no asignadas a las categorías en orden
# hasta completar exactamente el total
if (personas_no_asignadas > 0) {
  categorias <- c("bien1_bien3", "solo_bien3", "solo_bien2", "solo_bien1", "bien1_bien2")
  for (i in 1:personas_no_asignadas) {
    idx <- ((i-1) %% 5) + 1  # Cicla entre 1 y 5
    personas_exactas[[categorias[idx]]] <- personas_exactas[[categorias[idx]]] + 1
  }
}

# Asignamos los valores finales de personas por categoría
personas_bien1_bien3 <- personas_exactas$bien1_bien3
personas_solo_bien3 <- personas_exactas$solo_bien3
personas_solo_bien2 <- personas_exactas$solo_bien2
personas_solo_bien1 <- personas_exactas$solo_bien1
personas_bien1_bien2 <- personas_exactas$bien1_bien2

# Verificamos que la suma sea exactamente igual al total
personas <- c(personas_bien1_bien3, personas_solo_bien3, personas_solo_bien2,
              personas_solo_bien1, personas_bien1_bien2)
test_that("La suma de todas las categorías es igual al total", {
  expect_equal(sum(personas), total_personas)
})

# Recalculamos los porcentajes exactos basados en las cantidades finales de personas
# Estos son los porcentajes que usaremos para todos los cálculos en la solución
porcentajes_exactos <- list(
  bien1_bien3 = (personas_bien1_bien3 * 100) / total_personas,
  solo_bien3 = (personas_solo_bien3 * 100) / total_personas,
  solo_bien2 = (personas_solo_bien2 * 100) / total_personas,
  solo_bien1 = (personas_solo_bien1 * 100) / total_personas,
  bien1_bien2 = (personas_bien1_bien2 * 100) / total_personas
)

# Y los porcentajes redondeados para mostrar en pantalla
porcentajes_mostrar <- list(
  bien1_bien3 = round(porcentajes_exactos$bien1_bien3),
  solo_bien3 = round(porcentajes_exactos$solo_bien3),
  solo_bien2 = round(porcentajes_exactos$solo_bien2),
  solo_bien1 = round(porcentajes_exactos$solo_bien1),
  bien1_bien2 = round(porcentajes_exactos$bien1_bien2)
)

# Verificamos que el total de los porcentajes exactos sea 100%
test_that("Los porcentajes exactos suman 100%", {
  expect_true(abs(sum(unlist(porcentajes_exactos)) - 100) < 0.001)
})

# Para visualización y verificación
cat("Total de personas:", total_personas, "\n")
cat("Distribución por categoría:\n")
cat("- bien1_bien3:", personas_bien1_bien3, "personas,",
    porcentajes_exactos$bien1_bien3, "% (exacto),",
    porcentajes_mostrar$bien1_bien3, "% (mostrado)\n")
cat("- solo_bien3:", personas_solo_bien3, "personas,",
    porcentajes_exactos$solo_bien3, "% (exacto),",
    porcentajes_mostrar$solo_bien3, "% (mostrado)\n")
cat("- solo_bien2:", personas_solo_bien2, "personas,",
    porcentajes_exactos$solo_bien2, "% (exacto),",
    porcentajes_mostrar$solo_bien2, "% (mostrado)\n")
cat("- solo_bien1:", personas_solo_bien1, "personas,",
    porcentajes_exactos$solo_bien1, "% (exacto),",
    porcentajes_mostrar$solo_bien1, "% (mostrado)\n")
cat("- bien1_bien2:", personas_bien1_bien2, "personas,",
    porcentajes_exactos$bien1_bien2, "% (exacto),",
    porcentajes_mostrar$bien1_bien2, "% (mostrado)\n")

# Aleatorizar el tipo de pregunta y la condición inicial
tipos_pregunta <- c(
  "solo_bien1",
  "solo_bien2",
  "solo_bien3",
  "bien1_bien2",
  "bien1_bien3"
)

# Aleatorizar la condición inicial (dato conocido)
tipos_condicion <- c(
  "bien1_bien3",
  "solo_bien3",
  "solo_bien2",
  "solo_bien1",
  "bien1_bien2"
)

# Seleccionar aleatoriamente el tipo de pregunta y condición (asegurando que sean diferentes)
tipo_pregunta <- sample(tipos_pregunta, 1)
# Seleccionamos un tipo de condición que sea diferente al tipo de pregunta
tipos_condicion_filtrados <- tipos_condicion[tipos_condicion != tipo_pregunta]
tipo_condicion <- sample(tipos_condicion_filtrados, 1)

# Mapa de datos para acceso uniforme a todas las categorías
datos_categorias <- list(
  solo_bien1 = list(
    personas = personas_solo_bien1,
    texto = paste0("solo ", bien1),
    porcentaje_exacto = porcentajes_exactos$solo_bien1,
    porcentaje_mostrar = porcentajes_mostrar$solo_bien1
  ),
  solo_bien2 = list(
    personas = personas_solo_bien2,
    texto = paste0("solo ", bien2),
    porcentaje_exacto = porcentajes_exactos$solo_bien2,
    porcentaje_mostrar = porcentajes_mostrar$solo_bien2
  ),
  solo_bien3 = list(
    personas = personas_solo_bien3,
    texto = paste0("solo ", bien3),
    porcentaje_exacto = porcentajes_exactos$solo_bien3,
    porcentaje_mostrar = porcentajes_mostrar$solo_bien3
  ),
  bien1_bien2 = list(
    personas = personas_bien1_bien2,
    texto = paste0(bien1, " y ", bien2),
    porcentaje_exacto = porcentajes_exactos$bien1_bien2,
    porcentaje_mostrar = porcentajes_mostrar$bien1_bien2
  ),
  bien1_bien3 = list(
    personas = personas_bien1_bien3,
    texto = paste0(bien1, " y ", bien3),
    porcentaje_exacto = porcentajes_exactos$bien1_bien3,
    porcentaje_mostrar = porcentajes_mostrar$bien1_bien3
  )
)

# Asignar valores de la respuesta correcta (pregunta)
respuesta_correcta <- datos_categorias[[tipo_pregunta]]$personas
texto_pregunta <- datos_categorias[[tipo_pregunta]]$texto
porcentaje_pregunta_exacto <- datos_categorias[[tipo_pregunta]]$porcentaje_exacto
porcentaje_pregunta_mostrar <- datos_categorias[[tipo_pregunta]]$porcentaje_mostrar

# Asignar valores de la condición
valor_condicion <- datos_categorias[[tipo_condicion]]$personas
texto_condicion <- datos_categorias[[tipo_condicion]]$texto
porcentaje_condicion_exacto <- datos_categorias[[tipo_condicion]]$porcentaje_exacto
porcentaje_condicion_mostrar <- datos_categorias[[tipo_condicion]]$porcentaje_mostrar

# Generar distractores plausibles
# Método 1: Usar el valor correcto como base para crear variaciones
# Método 2: Usar otros valores del problema que no sean la respuesta
# Método 3: Generar valores completamente diferentes pero plausibles

# Asegurar que los distractores sean suficientemente diferentes del valor correcto
min_diferencia <- max(5, round(respuesta_correcta * 0.05))  # Al menos 5 unidades o 5% de diferencia

# Distractor 1: Aplicar una variación porcentual al valor correcto
variacion_porcentaje <- sample(c(0.7, 0.75, 0.8, 1.2, 1.25, 1.3), 1)
distractor1 <- round(respuesta_correcta * variacion_porcentaje)
# Asegurar diferencia mínima
if (abs(distractor1 - respuesta_correcta) < min_diferencia) {
  if (distractor1 < respuesta_correcta) {
    distractor1 <- respuesta_correcta - min_diferencia
  } else {
    distractor1 <- respuesta_correcta + min_diferencia
  }
}

# Distractor 2: Usar un porcentaje aleatorio del total
porcentaje_aleatorio <- sample(c(12, 15, 18, 22, 25, 28, 32, 35), 1)
distractor2 <- round(total_personas * porcentaje_aleatorio / 100)
# Asegurar diferencia mínima
if (abs(distractor2 - respuesta_correcta) < min_diferencia) {
  distractor2 <- distractor2 + sample(c(-1, 1), 1) * (min_diferencia + sample(5:15, 1))
}

# Distractor 3: Usar el valor de la condición si es diferente, o generar otro valor plausible
if (abs(valor_condicion - respuesta_correcta) >= min_diferencia) {
  distractor3 <- valor_condicion
} else {
  # Generar un valor completamente diferente pero plausible
  distractor3 <- round(total_personas * sample(c(8, 10, 12, 15, 20, 25, 30, 35, 40), 1) / 100)
  # Asegurar diferencia mínima
  if (abs(distractor3 - respuesta_correcta) < min_diferencia) {
    distractor3 <- distractor3 + sample(c(-1, 1), 1) * (min_diferencia + sample(5:15, 1))
  }
}

# Asegurar que todos los distractores sean diferentes entre sí
while (length(unique(c(distractor1, distractor2, distractor3))) < 3) {
  if (distractor1 == distractor2) {
    distractor1 <- distractor1 + sample(c(-1, 1), 1) * (min_diferencia + sample(5:10, 1))
  }
  if (distractor2 == distractor3) {
    distractor2 <- distractor2 + sample(c(-1, 1), 1) * (min_diferencia + sample(5:10, 1))
  }
  if (distractor1 == distractor3) {
    distractor3 <- distractor3 + sample(c(-1, 1), 1) * (min_diferencia + sample(5:10, 1))
  }
}

# Asegurar que todos los distractores sean valores positivos y plausibles
distractor1 <- max(distractor1, 1)
distractor2 <- max(distractor2, 1)
distractor3 <- max(distractor3, 1)
if (distractor1 > total_personas * 0.5) distractor1 <- round(total_personas * sample(c(0.2, 0.25, 0.3, 0.35), 1))
if (distractor2 > total_personas * 0.5) distractor2 <- round(total_personas * sample(c(0.15, 0.2, 0.25, 0.3), 1))
if (distractor3 > total_personas * 0.5) distractor3 <- round(total_personas * sample(c(0.1, 0.15, 0.2, 0.25), 1))

# Crear un vector con todas las opciones y mezclarlas
opciones <- c(respuesta_correcta, distractor1, distractor2, distractor3)
names(opciones) <- c("correcta", "distractor1", "distractor2", "distractor3")
opciones_mezcladas <- sample(opciones)

# Identificar la posición de la respuesta correcta en las opciones mezcladas
indice_correcto <- which(opciones_mezcladas == respuesta_correcta)

# Crear el vector de solución para r-exams
solucion <- rep(0, 4)
solucion[indice_correcto] <- 1

# Para verificación
cat("\nPregunta y Condición seleccionadas:\n")
cat("- Pregunta:", tipo_pregunta, "=", respuesta_correcta, "personas,",
    porcentaje_pregunta_exacto, "% (exacto),", porcentaje_pregunta_mostrar, "% (mostrado)\n")
cat("- Condición:", tipo_condicion, "=", valor_condicion, "personas,",
    porcentaje_condicion_exacto, "% (exacto),", porcentaje_condicion_mostrar, "% (mostrado)\n")
cat("- Opciones de respuesta:", paste(opciones_mezcladas, collapse=", "), "\n")
cat("- Índice correcto:", indice_correcto, "\n")
```

```{r generar_grafico_circular, message=FALSE, warning=FALSE}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Aleatorizar estilos visuales adicionales para el gráfico
estilo_grafico <- list(
  # Aleatorizar el ángulo de inicio
  angulo_inicio = sample(c(0, 45, 90, 135, 180, 225, 270, 315), 1),

  # Aleatorizar el estilo de borde
  borde = sample(list(
    list(color = "white", grosor = 1),
    list(color = "white", grosor = 1.5),
    list(color = "#DDDDDD", grosor = 1),
    list(color = "#333333", grosor = 0.5),
    list(color = "none", grosor = 0)
  ), 1)[[1]],

  # Aleatorizar el estilo de sombra
  sombra = sample(c(TRUE, FALSE), 1, prob = c(0.7, 0.3)),

  # Aleatorizar el estilo de etiquetas de porcentaje
  estilo_porcentaje = sample(list(
    list(color_fondo = "black", alpha = 0.7, estilo = "round", padding = 0.2),
    list(color_fondo = "#333333", alpha = 0.8, estilo = "round", padding = 0.3),
    list(color_fondo = "#555555", alpha = 0.7, estilo = "round,pad=0.2", padding = 0.2),
    list(color_fondo = "none", alpha = 1, estilo = "round", padding = 0)
  ), 1)[[1]],

  # Aleatorizar el estilo de las etiquetas externas
  estilo_etiquetas = sample(list(
    list(color_fondo = "white", color_borde = "gray", alpha = 0.9, estilo = "round", padding = 0.3),
    list(color_fondo = "white", color_borde = "#AAAAAA", alpha = 0.95, estilo = "round", padding = 0.4),
    list(color_fondo = "#F5F5F5", color_borde = "#CCCCCC", alpha = 0.9, estilo = "round", padding = 0.3),
    list(color_fondo = "#EEEEEE", color_borde = "#999999", alpha = 0.9, estilo = "round", padding = 0.3)
  ), 1)[[1]],

  # Aleatorizar el sector destacado (explode)
  sector_destacado = sample(0:4, 1)
)

# Aleatorizar el formato de porcentaje
formato_porcentaje <- sample(c("'%d%%'", "'%.1f%%'"), 1)

# Código Python para generar el gráfico circular
codigo_python <- paste0("
import matplotlib
matplotlib.use('Agg')  # Usar backend no interactivo
import matplotlib.pyplot as plt
import numpy as np
from matplotlib.colors import LinearSegmentedColormap

# Datos para el gráfico - usamos los porcentajes para mostrar que se calcularon para asegurar coherencia
labels = ['", bien1, " y ", bien3, "', 'Solo ", bien3, "', 'Solo ", bien2, "',
          'Solo ", bien1, "', '", bien1, " y ", bien2, "']

# Usamos porcentajes_mostrar para asegurar coherencia visual con los cálculos
sizes = [", porcentajes_mostrar$bien1_bien3, ", ", porcentajes_mostrar$solo_bien3, ", ", porcentajes_mostrar$solo_bien2, ",
         ", porcentajes_mostrar$solo_bien1, ", ", porcentajes_mostrar$bien1_bien2, "]

colors = ['", paleta_seleccionada[1], "', '", paleta_seleccionada[2], "',
          '", paleta_seleccionada[3], "', '", paleta_seleccionada[4], "',
          '", paleta_seleccionada[5], "']

# Crear explode para destacar un sector específico
explode = [0, 0, 0, 0, 0]
explode[", estilo_grafico$sector_destacado, "] = 0.03  # Destacar el sector seleccionado

# Crear figura con más espacio para acomodar las etiquetas externas
plt.figure(figsize=(7, 6))  # Aumentar tamaño para dar espacio a las etiquetas

# Crear gráfico circular con estilos personalizados
wedges, texts, autotexts = plt.pie(
    sizes,
    explode=explode,
    colors=colors,
    shadow=", ifelse(estilo_grafico$sombra, "True", "False"), ",
    startangle=", estilo_grafico$angulo_inicio, ",  # Ángulo de inicio aleatorizado
    autopct='%d%%',  # Siempre mostrar porcentajes enteros para evitar confusiones
    pctdistance=0.75,  # Ubicar porcentajes más cerca del borde exterior
    wedgeprops={'edgecolor': '", estilo_grafico$borde$color, "', 'linewidth': ", estilo_grafico$borde$grosor, "},
    textprops={'fontsize': 10, 'fontweight': 'bold', 'color': 'white'}
)

# Configuración estética de los textos de porcentaje con recuadros
for autotext in autotexts:
    autotext.set_fontsize(9)  # Tamaño de fuente para porcentajes
    autotext.set_weight('bold')
    # Crear un recuadro semitransparente para los porcentajes
", if(estilo_grafico$estilo_porcentaje$color_fondo != "none") paste0("    bbox_props = {'boxstyle': '", estilo_grafico$estilo_porcentaje$estilo, "',
                 'facecolor': '", estilo_grafico$estilo_porcentaje$color_fondo, "',
                 'alpha': ", estilo_grafico$estilo_porcentaje$alpha, ",
                 'edgecolor': 'none'}
    autotext.set_bbox(bbox_props)"), "

# Eliminar los textos del pie (los reemplazaremos con etiquetas externas)
for text in texts:
    text.set_visible(False)

# Crear etiquetas externas con líneas conectoras
bbox_props = {'boxstyle': '", estilo_grafico$estilo_etiquetas$estilo, "',
             'facecolor': '", estilo_grafico$estilo_etiquetas$color_fondo, "',
             'edgecolor': '", estilo_grafico$estilo_etiquetas$color_borde, "',
             'alpha': ", estilo_grafico$estilo_etiquetas$alpha, "}

# Calcular posiciones de etiquetas externas optimizadas para evitar solapamiento
def get_label_position(angle_rad, wedge_size):
    # Ajustar distancia basada en el tamaño del sector
    # Sectores más pequeños tienen etiquetas más alejadas para evitar solapamiento
    distance_factor = 1.25 if wedge_size < 15 else 1.15

    # Determinar coordenadas
    x = distance_factor * np.cos(angle_rad)
    y = distance_factor * np.sin(angle_rad)

    # Ajustar alineación basada en cuadrante
    if x < 0:
        ha = 'right'
    else:
        ha = 'left'

    if y < 0:
        va = 'top'
    else:
        va = 'bottom'

    # Para sectores muy pequeños, ajustar más la posición para evitar solapamiento
    if wedge_size < 10:
        x *= 1.1
        y *= 1.1

    return x, y, ha, va

# Variables de Python (no podemos acceder a las variables de R directamente)
# Añadimos un marcador de categoría simple
labels_with_info = [
    f'{labels[0]} (categoría 1)',
    f'{labels[1]} (categoría 2)',
    f'{labels[2]} (categoría 3)',
    f'{labels[3]} (categoría 4)',
    f'{labels[4]} (categoría 5)'
]

# Colocar etiquetas externas con líneas conectoras
for i, wedge in enumerate(wedges):
    # Calcular ángulo medio del sector
    ang = (wedge.theta1 + wedge.theta2) / 2
    ang_rad = np.radians(ang)

    # Calcular coordenadas para el inicio de la línea conectora (en el borde del sector)
    # El factor 0.85 asegura que la línea empiece cerca del borde del sector
    x_edge = 0.85 * np.cos(ang_rad)
    y_edge = 0.85 * np.sin(ang_rad)

    # Obtener posición optimizada para la etiqueta
    x_label, y_label, ha, va = get_label_position(ang_rad, sizes[i])

    # Dibujar línea conectora
    con = plt.annotate('',
                      xy=(x_edge, y_edge),  # Inicio (en el borde del sector)
                      xytext=(x_label * 0.95, y_label * 0.95),  # Fin (cerca de la etiqueta)
                      arrowprops=dict(arrowstyle='-', color='", estilo_grafico$estilo_etiquetas$color_borde, "', lw=0.8))

    # Añadir etiqueta con recuadro personalizado - mostramos solo el texto básico sin el conteo
    plt.text(x_label, y_label, labels[i],
            fontsize=8,
            fontweight='bold',
            ha=ha,
            va=va,
            bbox=bbox_props,
            zorder=10)

# Asegurar que el gráfico sea un círculo
plt.axis('equal')

# Añadir título en la parte inferior del gráfico
plt.figtext(0.5, 0.01,  # Posición x=0.5 (centro), y=0.01 (parte inferior)
           'Distribución de bienes (total: ", total_personas, " personas)',
           fontsize=12,
           fontweight='bold',
           color='#333333',
           ha='center')  # Alineación horizontal centrada

# Ajustar los márgenes para dejar espacio a las etiquetas externas
plt.tight_layout(pad=1.5, rect=[0, 0.05, 1, 0.95])  # Ajustar rect para dar más espacio

# Guardar en múltiples formatos para asegurar compatibilidad
plt.savefig('grafico_circular.png', dpi=150, bbox_inches='tight',
           transparent=True, format='png')
plt.savefig('grafico_circular.pdf', dpi=150, bbox_inches='tight',
           transparent=True, format='pdf')
plt.close()
")

# Ejecutar código Python para generar la figura
py_run_string(codigo_python)
```

Question
========

En La Tebaida se realizó un(a) `r termino_encuesta` a un grupo de `r termino_personas` de un(a) `r contexto` sobre el tipo de `r termino_bienes` que poseen. Los(las) `r termino_resultados` se presentan en la gráfica.

```{r mostrar_grafico_circular, echo=FALSE, results='asis', fig.align="center"}
# Detectar si se está generando para Moodle
# Formatos para Moodle y similares
formatos_moodle <- c("exams2moodle", "exams2qti12",
                     "exams2qti21", "exams2openolat")
es_moodle <- (match_exams_call() %in% formatos_moodle)

# Mostrar la imagen del gráfico circular
if (es_moodle) {
  # Tamaño para Moodle
  cat("![](grafico_circular.png){width=60%}")
} else {
  # Tamaño para PDF/Word
  cat("![](grafico_circular.png){width=70%}")
}
```

Si `r valor_condicion` `r termino_personas` de el(la) `r contexto` poseen `r texto_condicion`, ¿cuántas personas poseen `r texto_pregunta`?

Answerlist
----------
- `r opciones_mezcladas[1]`
- `r opciones_mezcladas[2]`
- `r opciones_mezcladas[3]`
- `r opciones_mezcladas[4]`

Solution
========

Para resolver este problema, necesitamos aplicar proporciones y regla de tres a partir de la información dada en el gráfico circular y el enunciado. Seguiremos un proceso paso a paso:

### Paso 1: Identificar los datos conocidos:

* Sabemos que `r valor_condicion` `r termino_personas` poseen `r texto_condicion`.
* Según el gráfico circular, este grupo representa el `r porcentajes_mostrar[[tipo_condicion]]`% del total.
* También observamos en el gráfico que las personas que poseen `r texto_pregunta` representan el `r porcentajes_mostrar[[tipo_pregunta]]`% del total.

### Paso 2: Calcular el número total de personas:

Para encontrar el total de `r termino_personas` en la `r contexto`, utilizamos la siguiente relación de proporcionalidad:

Si `r porcentajes_mostrar[[tipo_condicion]]`% del total = `r valor_condicion` `r termino_personas`.

Entonces 100% del total = X `r termino_personas`

Aplicando regla de tres:

```
X = (valor_conocido × 100%) ÷ porcentaje_conocido
X = (`r valor_condicion` × 100%) ÷ `r porcentajes_mostrar[[tipo_condicion]]`%
X = (`r valor_condicion` × 100) ÷ `r porcentajes_mostrar[[tipo_condicion]]`
X = `r valor_condicion * 100` ÷ `r porcentajes_mostrar[[tipo_condicion]]`
X = `r sprintf("%.4f", (valor_condicion * 100) / porcentajes_mostrar[[tipo_condicion]])`
X = `r total_personas` `r termino_personas`
```

Por lo tanto, el total de `r termino_personas` en la `r contexto` es `r total_personas`.

### Paso 3: Calcular el número de personas que poseen `r texto_pregunta`
Una vez conocido el total, podemos calcular cuántas personas poseen `r texto_pregunta` utilizando el porcentaje correspondiente del gráfico circular:

Si 100% del total = `r total_personas` `r termino_personas`.

Entonces `r porcentajes_mostrar[[tipo_pregunta]]`% del total = Y `r termino_personas`

Aplicando regla de tres:

```
Y = (porcentaje_buscado × total_personas) ÷ 100%
Y = (`r porcentajes_mostrar[[tipo_pregunta]]`% × `r total_personas`) ÷ 100%
Y = (`r porcentajes_mostrar[[tipo_pregunta]]` × `r total_personas`) ÷ 100
Y = `r porcentajes_mostrar[[tipo_pregunta]] * total_personas` ÷ 100
Y = `r sprintf("%.4f", (porcentajes_mostrar[[tipo_pregunta]] * total_personas) / 100)`
Y = `r respuesta_correcta` `r termino_personas`
```

### Paso 4: Verificación de la respuesta
Podemos verificar nuestra respuesta comprobando que los números calculados son coherentes con los porcentajes del gráfico:

* `r bien1` y `r bien3`: `r personas_bien1_bien3` `r termino_personas` (`r porcentajes_mostrar$bien1_bien3`% del total)
* Solo `r bien3`: `r personas_solo_bien3` `r termino_personas` (`r porcentajes_mostrar$solo_bien3`% del total)
* Solo `r bien2`: `r personas_solo_bien2` `r termino_personas` (`r porcentajes_mostrar$solo_bien2`% del total)
* Solo `r bien1`: `r personas_solo_bien1` `r termino_personas` (`r porcentajes_mostrar$solo_bien1`% del total)
* `r bien1` y `r bien2`: `r personas_bien1_bien2` `r termino_personas` (`r porcentajes_mostrar$bien1_bien2`% del total)

La suma de todas estas categorías es `r sum(c(personas_bien1_bien3, personas_solo_bien3, personas_solo_bien2, personas_solo_bien1, personas_bien1_bien2))` `r termino_personas`, que coincide exactamente con nuestro total calculado de `r total_personas` `r termino_personas`.

### Conclusión
Por lo tanto, `r respuesta_correcta` `r termino_personas` de la `r contexto` poseen `r texto_pregunta`.

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: proporciones_diagrama_circular
extype: schoice
exsolution: `r paste(as.integer(solucion), collapse="")`
exshuffle: TRUE
extol: 0.0001
exsection: Estadística|Proporciones|Interpretación de gráficos
