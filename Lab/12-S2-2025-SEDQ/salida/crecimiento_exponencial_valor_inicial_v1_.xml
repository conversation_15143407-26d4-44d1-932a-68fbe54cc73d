<?xml version="1.0" encoding="UTF-8"?>
<quiz>


<question type="category">
<category>
<text>$course$/crecimiento_exponencial_valor_inicial_v1_/Álgebra y Cálculo/Funciones Exponenciales/Modelado de Poblaciones</text>
</category>
</question>


<question type="multichoice">
<name>
<text> Q1 : crecimiento_exponencial_valor_inicial_v1 </text>
</name>
<questiontext format="html">
<text><![CDATA[
<style type="text/css" rel="stylesheet">
/* tables with alternating shading */
.table_shade {
    border-collapse: collapse;
    border-spacing: 0;
    border:1px solid #FFFFFF;
    background-color: #FFFFFF;
}
.table_shade th {
    border:1px solid #FFFFFF;
    background: #D5D5D5;
}
.table_shade td {
    border:1px solid #FFFFFF;
}
.table_shade .odd {
    background: #EEEEEE;
}
.table_shade .even {
    background: #FBFBFB;
}
</style>
<p>
<p>una exótica especie de plantas aumenta su población según la función <span class="math inline">\(Q(t) = 20 \cdot (2.5)^{0.5 \cdot t}\)</span>, donde <span class="math inline">\(t\)</span> se mide en horas y <span class="math inline">\(Q\)</span> es la cantidad de plantas.</p>
<p>De acuerdo con la información anterior, la cantidad de plantas que había al iniciar un análisis poblacional, es:</p>
</p>]]></text>
</questiontext>
<generalfeedback format="html">
<text><![CDATA[<p>
<p>Para determinar la cantidad de plantas que había al iniciar un análisis poblacional, necesitamos evaluar la función de población <span class="math inline">\(Q(t) = 20(2.5)^{0.5t}\)</span> en el momento inicial, es decir, cuando el tiempo <span class="math inline">\(t = 0\)</span>.</p>
<p>La función dada es:
<span class="math inline">\(Q(t) = 20(2.5)^{0.5t}\)</span></p>
<p>Sustituimos <span class="math inline">\(t = 0\)</span> en la función:
<span class="math inline">\(Q(0) = 20(2.5)^{0.5 \cdot 0}\)</span>
<span class="math inline">\(Q(0) = 20(2.5)^0\)</span></p>
<p>Cualquier número (distinto de cero) elevado a la potencia 0 es igual a 1. Por lo tanto, <span class="math inline">\((2.5)^0 = 1\)</span>.
<span class="math inline">\(Q(0) = 20 \cdot 1\)</span>
<span class="math inline">\(Q(0) = 20\)</span></p>
<p>Así, la cantidad de plantas que había al iniciar un análisis poblacional es 20.</p>
</p>]]></text>
</generalfeedback>
<penalty>0</penalty>
<defaultgrade>1</defaultgrade>
<shuffleanswers>true</shuffleanswers>
<single>true</single>
<answernumbering>ABCD</answernumbering>
<answer fraction="100" format="html">
<text><![CDATA[<p>
20 plantas.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Verdadero
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
15 plantas.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
30 plantas.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
<answer fraction="0" format="html">
<text><![CDATA[<p>
18 plantas.
</p>]]></text>
<feedback format="html">
<text><![CDATA[<p>
Falso
</p>]]></text>
</feedback>
</answer>
</question>

</quiz>
