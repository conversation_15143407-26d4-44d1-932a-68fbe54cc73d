% Options for packages loaded elsewhere
\PassOptionsToPackage{unicode}{hyperref}
\PassOptionsToPackage{hyphens}{url}
%
\documentclass[
]{article}
\usepackage{amsmath,amssymb}
\usepackage{iftex}
\ifPDFTeX
  \usepackage[T1]{fontenc}
  \usepackage[utf8]{inputenc}
  \usepackage{textcomp} % provide euro and other symbols
\else % if luatex or xetex
  \usepackage{unicode-math} % this also loads fontspec
  \defaultfontfeatures{Scale=MatchLowercase}
  \defaultfontfeatures[\rmfamily]{Ligatures=TeX,Scale=1}
\fi
\usepackage{lmodern}
\ifPDFTeX\else
  % xetex/luatex font selection
\fi
% Use upquote if available, for straight quotes in verbatim environments
\IfFileExists{upquote.sty}{\usepackage{upquote}}{}
\IfFileExists{microtype.sty}{% use microtype if available
  \usepackage[]{microtype}
  \UseMicrotypeSet[protrusion]{basicmath} % disable protrusion for tt fonts
}{}
\makeatletter
\@ifundefined{KOMAClassName}{% if non-KOMA class
  \IfFileExists{parskip.sty}{%
    \usepackage{parskip}
  }{% else
    \setlength{\parindent}{0pt}
    \setlength{\parskip}{6pt plus 2pt minus 1pt}}
}{% if KOMA class
  \KOMAoptions{parskip=half}}
\makeatother
\usepackage{xcolor}
\usepackage[margin=1in]{geometry}
\usepackage{color}
\usepackage{fancyvrb}
\newcommand{\VerbBar}{|}
\newcommand{\VERB}{\Verb[commandchars=\\\{\}]}
\DefineVerbatimEnvironment{Highlighting}{Verbatim}{commandchars=\\\{\}}
% Add ',fontsize=\small' for more characters per line
\usepackage{framed}
\definecolor{shadecolor}{RGB}{248,248,248}
\newenvironment{Shaded}{\begin{snugshade}}{\end{snugshade}}
\newcommand{\AlertTok}[1]{\textcolor[rgb]{0.94,0.16,0.16}{#1}}
\newcommand{\AnnotationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\AttributeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\BaseNTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\BuiltInTok}[1]{#1}
\newcommand{\CharTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\CommentTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\CommentVarTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ConstantTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\ControlFlowTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\DataTypeTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{#1}}
\newcommand{\DecValTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\DocumentationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\ErrorTok}[1]{\textcolor[rgb]{0.64,0.00,0.00}{\textbf{#1}}}
\newcommand{\ExtensionTok}[1]{#1}
\newcommand{\FloatTok}[1]{\textcolor[rgb]{0.00,0.00,0.81}{#1}}
\newcommand{\FunctionTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\ImportTok}[1]{#1}
\newcommand{\InformationTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\newcommand{\KeywordTok}[1]{\textcolor[rgb]{0.13,0.29,0.53}{\textbf{#1}}}
\newcommand{\NormalTok}[1]{#1}
\newcommand{\OperatorTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\OtherTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{#1}}
\newcommand{\PreprocessorTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textit{#1}}}
\newcommand{\RegionMarkerTok}[1]{#1}
\newcommand{\SpecialCharTok}[1]{\textcolor[rgb]{0.81,0.36,0.00}{\textbf{#1}}}
\newcommand{\SpecialStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\StringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\VariableTok}[1]{\textcolor[rgb]{0.00,0.00,0.00}{#1}}
\newcommand{\VerbatimStringTok}[1]{\textcolor[rgb]{0.31,0.60,0.02}{#1}}
\newcommand{\WarningTok}[1]{\textcolor[rgb]{0.56,0.35,0.01}{\textbf{\textit{#1}}}}
\usepackage{graphicx}
\makeatletter
\newsavebox\pandoc@box
\newcommand*\pandocbounded[1]{% scales image to fit in text height/width
  \sbox\pandoc@box{#1}%
  \Gscale@div\@tempa{\textheight}{\dimexpr\ht\pandoc@box+\dp\pandoc@box\relax}%
  \Gscale@div\@tempb{\linewidth}{\wd\pandoc@box}%
  \ifdim\@tempb\p@<\@tempa\p@\let\@tempa\@tempb\fi% select the smaller of both
  \ifdim\@tempa\p@<\p@\scalebox{\@tempa}{\usebox\pandoc@box}%
  \else\usebox{\pandoc@box}%
  \fi%
}
% Set default figure placement to htbp
\def\fps@figure{htbp}
\makeatother
\setlength{\emergencystretch}{3em} % prevent overfull lines
\providecommand{\tightlist}{%
  \setlength{\itemsep}{0pt}\setlength{\parskip}{0pt}}
\setcounter{secnumdepth}{-\maxdimen} % remove section numbering
\usepackage{bookmark}
\IfFileExists{xurl.sty}{\usepackage{xurl}}{} % add URL line breaks if available
\urlstyle{same}
\hypersetup{
  hidelinks,
  pdfcreator={LaTeX via pandoc}}

\author{}
\date{\vspace{-2.5em}}

\begin{document}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{options}\NormalTok{(}\AttributeTok{OutDec =} \StringTok{"."}\NormalTok{)}
\CommentTok{\# REFORZAR CONFIGURACIÓN ANTI{-}NOTACIÓN CIENTÍFICA}
\FunctionTok{options}\NormalTok{(}\AttributeTok{scipen =} \DecValTok{999}\NormalTok{)}
\FunctionTok{options}\NormalTok{(}\AttributeTok{digits =} \DecValTok{10}\NormalTok{)}

\FunctionTok{set.seed}\NormalTok{(}\FunctionTok{sample}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{10000}\NormalTok{, }\DecValTok{1}\NormalTok{))}

\CommentTok{\# FUNCIÓN RADICAL DE FORMATEO ENTERO}
\NormalTok{formatear\_entero }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{(numero) \{}
  \CommentTok{\# Forzar formato entero sin notación científica JAMÁS}
  \FunctionTok{formatC}\NormalTok{(}\FunctionTok{as.numeric}\NormalTok{(numero), }\AttributeTok{format =} \StringTok{"d"}\NormalTok{, }\AttributeTok{big.mark =} \StringTok{""}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Aleatorización del contexto}
\NormalTok{contextos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"canal de deportes"}\NormalTok{, }\StringTok{"plataforma de streaming"}\NormalTok{, }\StringTok{"revista deportiva"}\NormalTok{,}
               \StringTok{"portal web deportivo"}\NormalTok{, }\StringTok{"aplicación móvil"}\NormalTok{, }\StringTok{"blog de fútbol"}\NormalTok{)}
\NormalTok{contexto }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(contextos, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Determinar género del contexto para concordancia}
\NormalTok{contextos\_femeninos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"plataforma de streaming"}\NormalTok{, }\StringTok{"revista deportiva"}\NormalTok{, }\StringTok{"aplicación móvil"}\NormalTok{)}
\NormalTok{es\_contexto\_femenino }\OtherTok{\textless{}{-}}\NormalTok{ contexto }\SpecialCharTok{\%in\%}\NormalTok{ contextos\_femeninos}
\NormalTok{articulo\_contexto }\OtherTok{\textless{}{-}} \ControlFlowTok{if}\NormalTok{(es\_contexto\_femenino) }\StringTok{"Una"} \ControlFlowTok{else} \StringTok{"Un"}

\CommentTok{\# Aleatorización de términos para encuesta}
\NormalTok{terminos\_encuesta }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"encuesta"}\NormalTok{, }\StringTok{"sondeo"}\NormalTok{, }\StringTok{"consulta"}\NormalTok{, }\StringTok{"estudio"}\NormalTok{, }\StringTok{"investigación"}\NormalTok{)}
\NormalTok{termino\_encuesta }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_encuesta, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Determinar género del término de encuesta para concordancia}
\NormalTok{terminos\_femeninos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"encuesta"}\NormalTok{, }\StringTok{"consulta"}\NormalTok{, }\StringTok{"investigación"}\NormalTok{)}
\NormalTok{es\_femenino }\OtherTok{\textless{}{-}}\NormalTok{ termino\_encuesta }\SpecialCharTok{\%in\%}\NormalTok{ terminos\_femeninos}
\NormalTok{articulo\_encuesta }\OtherTok{\textless{}{-}} \ControlFlowTok{if}\NormalTok{(es\_femenino) }\StringTok{"la"} \ControlFlowTok{else} \StringTok{"el"}

\NormalTok{terminos\_usuarios }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"suscriptores"}\NormalTok{, }\StringTok{"seguidores"}\NormalTok{, }\StringTok{"usuarios"}\NormalTok{, }\StringTok{"miembros"}\NormalTok{, }\StringTok{"aficionados"}\NormalTok{)}
\NormalTok{termino\_usuarios }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(terminos\_usuarios, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Definir competiciones y equipos compatibles}
\NormalTok{competiciones\_clubes\_europeos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Champions League"}\NormalTok{, }\StringTok{"Liga Europa"}\NormalTok{)}
\NormalTok{competiciones\_clubes\_sudamericanos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Copa Libertadores"}\NormalTok{)}
\NormalTok{competiciones\_selecciones\_europeas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Eurocopa"}\NormalTok{)}
\NormalTok{competiciones\_selecciones\_sudamericanas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Copa América"}\NormalTok{)}
\NormalTok{competiciones\_selecciones\_mundiales }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Copa del Mundo"}\NormalTok{)}

\CommentTok{\# Equipos por región}
\NormalTok{equipos\_europeos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Manchester City"}\NormalTok{, }\StringTok{"FC Barcelona"}\NormalTok{, }\StringTok{"Real Madrid"}\NormalTok{, }\StringTok{"Liverpool"}\NormalTok{,}
                     \StringTok{"Bayern de Múnich"}\NormalTok{, }\StringTok{"Paris Saint{-}Germain"}\NormalTok{, }\StringTok{"Chelsea"}\NormalTok{,}
                     \StringTok{"Manchester United"}\NormalTok{, }\StringTok{"Arsenal"}\NormalTok{, }\StringTok{"Juventus"}\NormalTok{, }\StringTok{"AC Milan"}\NormalTok{,}
                     \StringTok{"Atlético de Madrid"}\NormalTok{, }\StringTok{"Borussia Dortmund"}\NormalTok{, }\StringTok{"Ajax"}\NormalTok{, }\StringTok{"Benfica"}\NormalTok{)}

\NormalTok{equipos\_sudamericanos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Boca Juniors"}\NormalTok{, }\StringTok{"River Plate"}\NormalTok{, }\StringTok{"Flamengo"}\NormalTok{, }\StringTok{"Palmeiras"}\NormalTok{,}
                          \StringTok{"São Paulo"}\NormalTok{, }\StringTok{"Santos"}\NormalTok{, }\StringTok{"Corinthians"}\NormalTok{, }\StringTok{"Atlético Nacional"}\NormalTok{,}
                          \StringTok{"Millonarios"}\NormalTok{, }\StringTok{"Colo{-}Colo"}\NormalTok{, }\StringTok{"Universidad de Chile"}\NormalTok{, }\StringTok{"Peñarol"}\NormalTok{,}
                          \StringTok{"Nacional"}\NormalTok{, }\StringTok{"Olimpia"}\NormalTok{, }\StringTok{"Cerro Porteño"}\NormalTok{)}

\NormalTok{selecciones\_europeas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"España"}\NormalTok{, }\StringTok{"Francia"}\NormalTok{, }\StringTok{"Alemania"}\NormalTok{, }\StringTok{"Italia"}\NormalTok{, }\StringTok{"Inglaterra"}\NormalTok{,}
                         \StringTok{"Portugal"}\NormalTok{, }\StringTok{"Países Bajos"}\NormalTok{, }\StringTok{"Bélgica"}\NormalTok{, }\StringTok{"Croacia"}\NormalTok{, }\StringTok{"Polonia"}\NormalTok{,}
                         \StringTok{"Suiza"}\NormalTok{, }\StringTok{"Austria"}\NormalTok{, }\StringTok{"Dinamarca"}\NormalTok{, }\StringTok{"Suecia"}\NormalTok{, }\StringTok{"Ucrania"}\NormalTok{)}

\NormalTok{selecciones\_sudamericanas }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Brasil"}\NormalTok{, }\StringTok{"Argentina"}\NormalTok{, }\StringTok{"Uruguay"}\NormalTok{, }\StringTok{"Colombia"}\NormalTok{, }\StringTok{"Chile"}\NormalTok{,}
                              \StringTok{"Perú"}\NormalTok{, }\StringTok{"Ecuador"}\NormalTok{, }\StringTok{"Paraguay"}\NormalTok{, }\StringTok{"Bolivia"}\NormalTok{, }\StringTok{"Venezuela"}\NormalTok{)}

\NormalTok{selecciones\_mundiales }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"Brasil"}\NormalTok{, }\StringTok{"Argentina"}\NormalTok{, }\StringTok{"Francia"}\NormalTok{, }\StringTok{"España"}\NormalTok{, }\StringTok{"Inglaterra"}\NormalTok{,}
                          \StringTok{"Portugal"}\NormalTok{, }\StringTok{"Alemania"}\NormalTok{, }\StringTok{"Italia"}\NormalTok{, }\StringTok{"Países Bajos"}\NormalTok{, }\StringTok{"Croacia"}\NormalTok{,}
                          \StringTok{"Uruguay"}\NormalTok{, }\StringTok{"Colombia"}\NormalTok{, }\StringTok{"México"}\NormalTok{, }\StringTok{"Estados Unidos"}\NormalTok{, }\StringTok{"Japón"}\NormalTok{)}

\CommentTok{\# Seleccionar competición y equipos compatibles}
\NormalTok{tipo\_competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{5}\NormalTok{, }\DecValTok{1}\NormalTok{)}

\ControlFlowTok{if}\NormalTok{ (tipo\_competicion }\SpecialCharTok{==} \DecValTok{1}\NormalTok{) \{}
  \CommentTok{\# Competiciones de clubes europeos}
\NormalTok{  competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(competiciones\_clubes\_europeos, }\DecValTok{1}\NormalTok{)}
\NormalTok{  equipos\_disponibles }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_europeos}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_competicion }\SpecialCharTok{==} \DecValTok{2}\NormalTok{) \{}
  \CommentTok{\# Competiciones de clubes sudamericanos}
\NormalTok{  competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(competiciones\_clubes\_sudamericanos, }\DecValTok{1}\NormalTok{)}
\NormalTok{  equipos\_disponibles }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_sudamericanos}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_competicion }\SpecialCharTok{==} \DecValTok{3}\NormalTok{) \{}
  \CommentTok{\# Competiciones de selecciones europeas}
\NormalTok{  competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(competiciones\_selecciones\_europeas, }\DecValTok{1}\NormalTok{)}
\NormalTok{  equipos\_disponibles }\OtherTok{\textless{}{-}}\NormalTok{ selecciones\_europeas}
\NormalTok{\} }\ControlFlowTok{else} \ControlFlowTok{if}\NormalTok{ (tipo\_competicion }\SpecialCharTok{==} \DecValTok{4}\NormalTok{) \{}
  \CommentTok{\# Competiciones de selecciones sudamericanas}
\NormalTok{  competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(competiciones\_selecciones\_sudamericanas, }\DecValTok{1}\NormalTok{)}
\NormalTok{  equipos\_disponibles }\OtherTok{\textless{}{-}}\NormalTok{ selecciones\_sudamericanas}
\NormalTok{\} }\ControlFlowTok{else}\NormalTok{ \{}
  \CommentTok{\# Competiciones de selecciones mundiales}
\NormalTok{  competicion }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(competiciones\_selecciones\_mundiales, }\DecValTok{1}\NormalTok{)}
\NormalTok{  equipos\_disponibles }\OtherTok{\textless{}{-}}\NormalTok{ selecciones\_mundiales}
\NormalTok{\}}

\CommentTok{\# Seleccionar 5 equipos/selecciones de la lista compatible}
\NormalTok{equipos\_seleccionados }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(equipos\_disponibles, }\DecValTok{5}\NormalTok{)}
\NormalTok{equipo1 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{1}\NormalTok{]}
\NormalTok{equipo2 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{2}\NormalTok{]}
\NormalTok{equipo3 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{3}\NormalTok{]}
\NormalTok{equipo4 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{4}\NormalTok{]}
\NormalTok{equipo5 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{5}\NormalTok{]}

\CommentTok{\# Población y muestra (usando números enteros simples)}
\NormalTok{poblacion\_total }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\DecValTok{256}\NormalTok{, }\DecValTok{1296}\NormalTok{, }\DecValTok{25000}\NormalTok{, }\DecValTok{30000}\NormalTok{, }\DecValTok{40000}\NormalTok{, }\DecValTok{50000}\NormalTok{, }\DecValTok{60000}\NormalTok{, }\DecValTok{75000}\NormalTok{, }\DecValTok{81000}\NormalTok{, }\DecValTok{90000}\NormalTok{, }\DecValTok{100000}\NormalTok{), }\DecValTok{1}\NormalTok{)}
\NormalTok{tamano\_muestra }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\DecValTok{80}\NormalTok{, }\DecValTok{90}\NormalTok{, }\DecValTok{100}\NormalTok{, }\DecValTok{110}\NormalTok{, }\DecValTok{120}\NormalTok{, }\DecValTok{130}\NormalTok{, }\DecValTok{140}\NormalTok{, }\DecValTok{150}\NormalTok{), }\DecValTok{1}\NormalTok{)}

\CommentTok{\# CREAR VARIABLES FORMATEADAS PARA MOSTRAR (SOLUCIÓN RADICAL)}
\NormalTok{poblacion\_total\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(poblacion\_total)}
\NormalTok{tamano\_muestra\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(tamano\_muestra)}

\CommentTok{\# FUNCIÓN ROBUSTA PARA GENERAR VALORES CON VARIABILIDAD GARANTIZADA}
\NormalTok{generar\_valores\_coherentes }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{(total, }\AttributeTok{max\_intentos =} \DecValTok{100}\NormalTok{) \{}
  \CommentTok{\# Definir rangos apropiados basados en el total}
\NormalTok{  min\_valor }\OtherTok{\textless{}{-}} \FunctionTok{max}\NormalTok{(}\DecValTok{8}\NormalTok{, }\FunctionTok{round}\NormalTok{(total }\SpecialCharTok{*} \FloatTok{0.08}\NormalTok{))  }\CommentTok{\# Mínimo 8\% del total}
\NormalTok{  max\_valor }\OtherTok{\textless{}{-}} \FunctionTok{min}\NormalTok{(}\DecValTok{40}\NormalTok{, }\FunctionTok{round}\NormalTok{(total }\SpecialCharTok{*} \FloatTok{0.35}\NormalTok{)) }\CommentTok{\# Máximo 35\% del total}

  \CommentTok{\# VERIFICACIÓN INICIAL}
  \ControlFlowTok{if}\NormalTok{ (min\_valor }\SpecialCharTok{*} \DecValTok{5} \SpecialCharTok{\textgreater{}}\NormalTok{ total) \{}
    \FunctionTok{stop}\NormalTok{(}\StringTok{"ERROR: Imposible generar 5 valores con min\_valor = "}\NormalTok{, min\_valor, }\StringTok{" y total = "}\NormalTok{, total)}
\NormalTok{  \}}

  \ControlFlowTok{for}\NormalTok{ (intento }\ControlFlowTok{in} \DecValTok{1}\SpecialCharTok{:}\NormalTok{max\_intentos) \{}
    \CommentTok{\# ESTRATEGIA ROBUSTA: Generar valores diversos desde el inicio}

    \CommentTok{\# Método 1: Distribución escalonada (primeros 50 intentos)}
    \ControlFlowTok{if}\NormalTok{ (intento }\SpecialCharTok{\textless{}=} \DecValTok{50}\NormalTok{) \{}
\NormalTok{      valor\_base }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(total }\SpecialCharTok{/} \DecValTok{5}\NormalTok{)}
\NormalTok{      variacion }\OtherTok{\textless{}{-}} \FunctionTok{min}\NormalTok{(}\DecValTok{6}\NormalTok{, }\FunctionTok{round}\NormalTok{(valor\_base }\SpecialCharTok{*} \FloatTok{0.3}\NormalTok{))}

      \CommentTok{\# Crear valores escalonados para garantizar variabilidad}
\NormalTok{      valores }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}
\NormalTok{        valor\_base }\SpecialCharTok{{-}}\NormalTok{ variacion,}
\NormalTok{        valor\_base }\SpecialCharTok{{-}} \FunctionTok{round}\NormalTok{(variacion}\SpecialCharTok{/}\DecValTok{2}\NormalTok{),}
\NormalTok{        valor\_base,}
\NormalTok{        valor\_base }\SpecialCharTok{+} \FunctionTok{round}\NormalTok{(variacion}\SpecialCharTok{/}\DecValTok{2}\NormalTok{),}
\NormalTok{        valor\_base }\SpecialCharTok{+}\NormalTok{ variacion}
\NormalTok{      )}

      \CommentTok{\# Asegurar que están en rango válido}
\NormalTok{      valores }\OtherTok{\textless{}{-}} \FunctionTok{pmax}\NormalTok{(valores, min\_valor)}
\NormalTok{      valores }\OtherTok{\textless{}{-}} \FunctionTok{pmin}\NormalTok{(valores, max\_valor)}

\NormalTok{    \} }\ControlFlowTok{else}\NormalTok{ \{}
      \CommentTok{\# Método 2: Distribución completamente aleatoria (últimos 50 intentos)}
\NormalTok{      valores }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(min\_valor}\SpecialCharTok{:}\NormalTok{max\_valor, }\DecValTok{5}\NormalTok{, }\AttributeTok{replace =} \ConstantTok{TRUE}\NormalTok{)}
\NormalTok{    \}}

    \CommentTok{\# Ajustar para que sumen exactamente el total}
\NormalTok{    diferencia }\OtherTok{\textless{}{-}}\NormalTok{ total }\SpecialCharTok{{-}} \FunctionTok{sum}\NormalTok{(valores)}

    \CommentTok{\# Distribuir la diferencia de manera inteligente}
\NormalTok{    intentos\_ajuste }\OtherTok{\textless{}{-}} \DecValTok{0}
    \ControlFlowTok{while}\NormalTok{ (diferencia }\SpecialCharTok{!=} \DecValTok{0} \SpecialCharTok{\&\&}\NormalTok{ intentos\_ajuste }\SpecialCharTok{\textless{}} \DecValTok{30}\NormalTok{) \{}
      \ControlFlowTok{if}\NormalTok{ (diferencia }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
        \CommentTok{\# Necesitamos aumentar algunos valores}
\NormalTok{        indices\_validos }\OtherTok{\textless{}{-}} \FunctionTok{which}\NormalTok{(valores }\SpecialCharTok{\textless{}}\NormalTok{ max\_valor)}
        \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(indices\_validos) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
\NormalTok{          idx }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(indices\_validos, }\DecValTok{1}\NormalTok{)}
\NormalTok{          incremento }\OtherTok{\textless{}{-}} \FunctionTok{min}\NormalTok{(diferencia, max\_valor }\SpecialCharTok{{-}}\NormalTok{ valores[idx])}
\NormalTok{          valores[idx] }\OtherTok{\textless{}{-}}\NormalTok{ valores[idx] }\SpecialCharTok{+}\NormalTok{ incremento}
\NormalTok{          diferencia }\OtherTok{\textless{}{-}}\NormalTok{ diferencia }\SpecialCharTok{{-}}\NormalTok{ incremento}
\NormalTok{        \} }\ControlFlowTok{else}\NormalTok{ \{}
          \ControlFlowTok{break}  \CommentTok{\# No se puede ajustar más}
\NormalTok{        \}}
\NormalTok{      \} }\ControlFlowTok{else}\NormalTok{ \{}
        \CommentTok{\# Necesitamos disminuir algunos valores}
\NormalTok{        indices\_validos }\OtherTok{\textless{}{-}} \FunctionTok{which}\NormalTok{(valores }\SpecialCharTok{\textgreater{}}\NormalTok{ min\_valor)}
        \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(indices\_validos) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
\NormalTok{          idx }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(indices\_validos, }\DecValTok{1}\NormalTok{)}
\NormalTok{          decremento }\OtherTok{\textless{}{-}} \FunctionTok{min}\NormalTok{(}\FunctionTok{abs}\NormalTok{(diferencia), valores[idx] }\SpecialCharTok{{-}}\NormalTok{ min\_valor)}
\NormalTok{          valores[idx] }\OtherTok{\textless{}{-}}\NormalTok{ valores[idx] }\SpecialCharTok{{-}}\NormalTok{ decremento}
\NormalTok{          diferencia }\OtherTok{\textless{}{-}}\NormalTok{ diferencia }\SpecialCharTok{+}\NormalTok{ decremento}
\NormalTok{        \} }\ControlFlowTok{else}\NormalTok{ \{}
          \ControlFlowTok{break}  \CommentTok{\# No se puede ajustar más}
\NormalTok{        \}}
\NormalTok{      \}}
\NormalTok{      intentos\_ajuste }\OtherTok{\textless{}{-}}\NormalTok{ intentos\_ajuste }\SpecialCharTok{+} \DecValTok{1}
\NormalTok{    \}}

    \CommentTok{\# Verificar si el resultado es válido}
    \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{sum}\NormalTok{(valores) }\SpecialCharTok{==}\NormalTok{ total }\SpecialCharTok{\&\&}
        \FunctionTok{all}\NormalTok{(valores }\SpecialCharTok{\textgreater{}=}\NormalTok{ min\_valor) }\SpecialCharTok{\&\&}
        \FunctionTok{all}\NormalTok{(valores }\SpecialCharTok{\textless{}=}\NormalTok{ max\_valor) }\SpecialCharTok{\&\&}
        \FunctionTok{length}\NormalTok{(}\FunctionTok{unique}\NormalTok{(valores)) }\SpecialCharTok{\textgreater{}=} \DecValTok{3}\NormalTok{) \{  }\CommentTok{\# Al menos 3 valores únicos}

      \CommentTok{\# Mezclar el orden para mayor aleatoriedad}
\NormalTok{      valores }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(valores)}

      \CommentTok{\# VERIFICACIÓN FINAL}
      \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{sum}\NormalTok{(valores) }\SpecialCharTok{!=}\NormalTok{ total) \{}
        \FunctionTok{stop}\NormalTok{(}\StringTok{"ERROR FINAL: suma = "}\NormalTok{, }\FunctionTok{sum}\NormalTok{(valores), }\StringTok{", esperado = "}\NormalTok{, total)}
\NormalTok{      \}}

      \FunctionTok{return}\NormalTok{(valores)}
\NormalTok{    \}}
\NormalTok{  \}}

  \CommentTok{\# Si llegamos aquí, usar método de emergencia}
  \FunctionTok{cat}\NormalTok{(}\StringTok{"ADVERTENCIA: Usando método de emergencia para total ="}\NormalTok{, total, }\StringTok{"}\SpecialCharTok{\textbackslash{}n}\StringTok{"}\NormalTok{)}

  \CommentTok{\# Método de emergencia: distribución simple pero garantizada}
\NormalTok{  valor\_base }\OtherTok{\textless{}{-}} \FunctionTok{floor}\NormalTok{(total }\SpecialCharTok{/} \DecValTok{5}\NormalTok{)}
\NormalTok{  resto }\OtherTok{\textless{}{-}}\NormalTok{ total }\SpecialCharTok{\%\%} \DecValTok{5}

\NormalTok{  valores }\OtherTok{\textless{}{-}} \FunctionTok{rep}\NormalTok{(valor\_base, }\DecValTok{5}\NormalTok{)}

  \CommentTok{\# Distribuir resto para crear variabilidad mínima}
  \ControlFlowTok{for}\NormalTok{ (i }\ControlFlowTok{in} \DecValTok{1}\SpecialCharTok{:}\NormalTok{resto) \{}
\NormalTok{    valores[i] }\OtherTok{\textless{}{-}}\NormalTok{ valores[i] }\SpecialCharTok{+} \DecValTok{1}
\NormalTok{  \}}

  \CommentTok{\# Añadir variabilidad mínima si es posible}
  \ControlFlowTok{if}\NormalTok{ (valores[}\DecValTok{1}\NormalTok{] }\SpecialCharTok{\textgreater{}}\NormalTok{ min\_valor }\SpecialCharTok{\&\&}\NormalTok{ valores[}\DecValTok{5}\NormalTok{] }\SpecialCharTok{\textless{}}\NormalTok{ max\_valor) \{}
\NormalTok{    valores[}\DecValTok{1}\NormalTok{] }\OtherTok{\textless{}{-}}\NormalTok{ valores[}\DecValTok{1}\NormalTok{] }\SpecialCharTok{{-}} \DecValTok{1}
\NormalTok{    valores[}\DecValTok{5}\NormalTok{] }\OtherTok{\textless{}{-}}\NormalTok{ valores[}\DecValTok{5}\NormalTok{] }\SpecialCharTok{+} \DecValTok{1}
\NormalTok{  \}}

  \CommentTok{\# VERIFICACIÓN FINAL DE EMERGENCIA}
  \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{sum}\NormalTok{(valores) }\SpecialCharTok{!=}\NormalTok{ total) \{}
    \FunctionTok{stop}\NormalTok{(}\StringTok{"ERROR CRÍTICO DE EMERGENCIA: suma = "}\NormalTok{, }\FunctionTok{sum}\NormalTok{(valores), }\StringTok{", esperado = "}\NormalTok{, total)}
\NormalTok{  \}}

  \FunctionTok{return}\NormalTok{(valores)}
\NormalTok{\}}

\NormalTok{valores\_equipos }\OtherTok{\textless{}{-}} \FunctionTok{generar\_valores\_coherentes}\NormalTok{(tamano\_muestra)}

\CommentTok{\# SELECCIÓN ALEATORIA DEL EQUIPO CORRECTO (SOLUCIÓN ANTI{-}PATRÓN)}
\CommentTok{\# En lugar de siempre usar el equipo con más votos, seleccionar aleatoriamente}
\CommentTok{\# cualquiera de los 5 equipos como respuesta correcta}
\NormalTok{indice\_equipo\_correcto }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{5}\NormalTok{, }\DecValTok{1}\NormalTok{)}

\CommentTok{\# No reordenar {-} mantener orden original aleatorio para evitar patrones}

\CommentTok{\# Asignar variables individuales DESPUÉS del ordenamiento}
\NormalTok{valor1 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[}\DecValTok{1}\NormalTok{]}
\NormalTok{valor2 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[}\DecValTok{2}\NormalTok{]}
\NormalTok{valor3 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[}\DecValTok{3}\NormalTok{]}
\NormalTok{valor4 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[}\DecValTok{4}\NormalTok{]}
\NormalTok{valor5 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[}\DecValTok{5}\NormalTok{]}

\CommentTok{\# CREAR VARIABLES FORMATEADAS PARA VALORES DE EQUIPOS}
\NormalTok{valor1\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor1)}
\NormalTok{valor2\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor2)}
\NormalTok{valor3\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor3)}
\NormalTok{valor4\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor4)}
\NormalTok{valor5\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor5)}

\NormalTok{equipo1 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{1}\NormalTok{]}
\NormalTok{equipo2 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{2}\NormalTok{]}
\NormalTok{equipo3 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{3}\NormalTok{]}
\NormalTok{equipo4 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{4}\NormalTok{]}
\NormalTok{equipo5 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[}\DecValTok{5}\NormalTok{]}

\CommentTok{\# VARIABLES DINÁMICAS PARA EL EQUIPO CORRECTO SELECCIONADO ALEATORIAMENTE}
\NormalTok{equipo\_correcto }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[indice\_equipo\_correcto]}
\NormalTok{valor\_correcto }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[indice\_equipo\_correcto]}
\NormalTok{valor\_correcto\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor\_correcto)}

\CommentTok{\# Validaciones matemáticas robustas}
\CommentTok{\# Verificar que los valores suman exactamente el tamaño de muestra}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{sum}\NormalTok{(valores\_equipos) }\SpecialCharTok{!=}\NormalTok{ tamano\_muestra) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Los valores no suman el tamaño de muestra. Suma: "}\NormalTok{, }\FunctionTok{sum}\NormalTok{(valores\_equipos),}
       \StringTok{", Esperado: "}\NormalTok{, tamano\_muestra)}
\NormalTok{\}}

\CommentTok{\# Verificar rangos apropiados}
\NormalTok{min\_esperado }\OtherTok{\textless{}{-}} \FunctionTok{max}\NormalTok{(}\DecValTok{8}\NormalTok{, }\FunctionTok{round}\NormalTok{(tamano\_muestra }\SpecialCharTok{*} \FloatTok{0.08}\NormalTok{))}
\NormalTok{max\_esperado }\OtherTok{\textless{}{-}} \FunctionTok{min}\NormalTok{(}\DecValTok{40}\NormalTok{, }\FunctionTok{round}\NormalTok{(tamano\_muestra }\SpecialCharTok{*} \FloatTok{0.35}\NormalTok{))}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{all}\NormalTok{(valores\_equipos }\SpecialCharTok{\textgreater{}=}\NormalTok{ min\_esperado) }\SpecialCharTok{||} \SpecialCharTok{!}\FunctionTok{all}\NormalTok{(valores\_equipos }\SpecialCharTok{\textless{}=}\NormalTok{ max\_esperado)) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Algunos valores están fuera del rango esperado ["}\NormalTok{, min\_esperado, }\StringTok{", "}\NormalTok{, max\_esperado, }\StringTok{"]"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# VALIDACIÓN ROBUSTA DE VARIABILIDAD}
\NormalTok{valores\_unicos }\OtherTok{\textless{}{-}} \FunctionTok{unique}\NormalTok{(valores\_equipos)}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(valores\_unicos) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Variabilidad insuficiente. Solo "}\NormalTok{, }\FunctionTok{length}\NormalTok{(valores\_unicos),}
       \StringTok{" valores únicos de 5. Valores: ["}\NormalTok{, }\FunctionTok{paste}\NormalTok{(valores\_equipos, }\AttributeTok{collapse=}\StringTok{", "}\NormalTok{),}
       \StringTok{"]. Se requieren al menos 3 valores únicos."}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Verificar que todos los valores son positivos}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\FunctionTok{all}\NormalTok{(valores\_equipos }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{)) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Algunos valores no son positivos"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Verificar que ningún equipo domina excesivamente}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{max}\NormalTok{(valores\_equipos) }\SpecialCharTok{\textgreater{}}\NormalTok{ tamano\_muestra }\SpecialCharTok{*} \FloatTok{0.4}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Un equipo tiene demasiados votos (\textgreater{}40\% del total)"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Colores para el gráfico}
\NormalTok{colores }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(}\StringTok{"\#2E8B57"}\NormalTok{, }\StringTok{"\#4682B4"}\NormalTok{, }\StringTok{"\#CD853F"}\NormalTok{, }\StringTok{"\#9370DB"}\NormalTok{, }\StringTok{"\#DC143C"}\NormalTok{)}

\CommentTok{\# FUNCIÓN PARA CALCULAR EL MÁXIMO COMÚN DIVISOR (MCD)}
\NormalTok{calcular\_mcd }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{(a, b) \{}
  \ControlFlowTok{while}\NormalTok{ (b }\SpecialCharTok{!=} \DecValTok{0}\NormalTok{) \{}
\NormalTok{    temp }\OtherTok{\textless{}{-}}\NormalTok{ b}
\NormalTok{    b }\OtherTok{\textless{}{-}}\NormalTok{ a }\SpecialCharTok{\%\%}\NormalTok{ b}
\NormalTok{    a }\OtherTok{\textless{}{-}}\NormalTok{ temp}
\NormalTok{  \}}
  \FunctionTok{return}\NormalTok{(a)}
\NormalTok{\}}

\CommentTok{\# FUNCIÓN PARA GENERAR FRACCIÓN REDUCIDA}
\NormalTok{generar\_fraccion\_reducida }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{(numerador, denominador) \{}
\NormalTok{  mcd }\OtherTok{\textless{}{-}} \FunctionTok{calcular\_mcd}\NormalTok{(numerador, denominador)}
  \FunctionTok{return}\NormalTok{(}\FunctionTok{c}\NormalTok{(numerador }\SpecialCharTok{/}\NormalTok{ mcd, denominador }\SpecialCharTok{/}\NormalTok{ mcd))}
\NormalTok{\}}

\CommentTok{\# Generar opciones de respuesta MEJORADAS Y MÁS DESAFIANTES}
\CommentTok{\# La respuesta correcta es sobre el equipo seleccionado ALEATORIAMENTE}
\NormalTok{proporcion\_correcta }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(valor\_correcto }\SpecialCharTok{/}\NormalTok{ tamano\_muestra }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{respuesta\_correcta }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_correcto\_fmt, }\StringTok{" de cada "}\NormalTok{, tamano\_muestra\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{                            termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_correcto, }\StringTok{"."}\NormalTok{)}

\CommentTok{\# GENERAR FRACCIÓN REDUCIDA PARA DISTRACTOR DESAFIANTE}
\NormalTok{fraccion\_reducida }\OtherTok{\textless{}{-}} \FunctionTok{generar\_fraccion\_reducida}\NormalTok{(valor\_correcto, tamano\_muestra)}
\NormalTok{numerador\_reducido }\OtherTok{\textless{}{-}}\NormalTok{ fraccion\_reducida[}\DecValTok{1}\NormalTok{]}
\NormalTok{denominador\_reducido }\OtherTok{\textless{}{-}}\NormalTok{ fraccion\_reducida[}\DecValTok{2}\NormalTok{]}

\CommentTok{\# Formatear números de la fracción reducida}
\NormalTok{numerador\_reducido\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(numerador\_reducido)}
\NormalTok{denominador\_reducido\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(denominador\_reducido)}

\CommentTok{\# GENERACIÓN ROBUSTA DE DISTRACTORES ÚNICOS}
\CommentTok{\# Sistema mejorado que garantiza exactamente 4 opciones únicas (1 correcta + 3 distractores)}

\CommentTok{\# Función para generar distractores de respaldo únicos}
\NormalTok{generar\_distractores\_respaldo }\OtherTok{\textless{}{-}} \ControlFlowTok{function}\NormalTok{(base\_valor, base\_muestra, base\_poblacion, base\_equipo, base\_contexto, base\_usuarios) \{}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{()}

  \CommentTok{\# Respaldo 1: Usar porcentaje directo}
\NormalTok{  porcentaje }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(base\_valor }\SpecialCharTok{/}\NormalTok{ base\_muestra }\SpecialCharTok{*} \DecValTok{100}\NormalTok{)}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_respaldo,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"el "}\NormalTok{, porcentaje, }\StringTok{"\% de los "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(base\_muestra), }\StringTok{" "}\NormalTok{, base\_usuarios,}
           \StringTok{" del "}\NormalTok{, base\_contexto, }\StringTok{" da por favorito al "}\NormalTok{, base\_equipo, }\StringTok{"."}\NormalTok{))}

  \CommentTok{\# Respaldo 2: Usar valor absoluto con población}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_respaldo,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"exactamente "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(base\_valor), }\StringTok{" de los "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(base\_poblacion), }\StringTok{" "}\NormalTok{,}
\NormalTok{           base\_usuarios, }\StringTok{" del "}\NormalTok{, base\_contexto, }\StringTok{" da por favorito al "}\NormalTok{, base\_equipo, }\StringTok{"."}\NormalTok{))}

  \CommentTok{\# Respaldo 3: Usar fracción con denominador diferente}
\NormalTok{  nuevo\_denominador }\OtherTok{\textless{}{-}}\NormalTok{ base\_muestra }\SpecialCharTok{+} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\DecValTok{10}\NormalTok{, }\DecValTok{20}\NormalTok{, }\DecValTok{30}\NormalTok{, }\DecValTok{50}\NormalTok{), }\DecValTok{1}\NormalTok{)}
\NormalTok{  nuevo\_numerador }\OtherTok{\textless{}{-}} \FunctionTok{round}\NormalTok{(base\_valor }\SpecialCharTok{*}\NormalTok{ nuevo\_denominador }\SpecialCharTok{/}\NormalTok{ base\_muestra)}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_respaldo,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"aproximadamente "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(nuevo\_numerador), }\StringTok{" de cada "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(nuevo\_denominador), }\StringTok{" "}\NormalTok{,}
\NormalTok{           base\_usuarios, }\StringTok{" del "}\NormalTok{, base\_contexto, }\StringTok{" da por favorito al "}\NormalTok{, base\_equipo, }\StringTok{"."}\NormalTok{))}

  \CommentTok{\# Respaldo 4: Usar múltiplo de la fracción original}
\NormalTok{  multiplicador }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{c}\NormalTok{(}\DecValTok{2}\NormalTok{, }\DecValTok{3}\NormalTok{, }\DecValTok{4}\NormalTok{, }\DecValTok{5}\NormalTok{), }\DecValTok{1}\NormalTok{)}
\NormalTok{  mult\_numerador }\OtherTok{\textless{}{-}}\NormalTok{ base\_valor }\SpecialCharTok{*}\NormalTok{ multiplicador}
\NormalTok{  mult\_denominador }\OtherTok{\textless{}{-}}\NormalTok{ base\_muestra }\SpecialCharTok{*}\NormalTok{ multiplicador}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_respaldo,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(mult\_numerador), }\StringTok{" de cada "}\NormalTok{, }\FunctionTok{formatear\_entero}\NormalTok{(mult\_denominador), }\StringTok{" "}\NormalTok{,}
\NormalTok{           base\_usuarios, }\StringTok{" del "}\NormalTok{, base\_contexto, }\StringTok{" da por favorito al "}\NormalTok{, base\_equipo, }\StringTok{"."}\NormalTok{))}

  \FunctionTok{return}\NormalTok{(distractores\_respaldo)}
\NormalTok{\}}

\CommentTok{\# SISTEMA ANTI{-}PATRÓN: DIVERSIFICACIÓN OBLIGATORIA DE EQUIPOS}
\CommentTok{\# Garantiza que cada opción mencione un equipo diferente para evitar patrones detectables}

\CommentTok{\# Seleccionar equipos únicos para cada distractor}
\NormalTok{equipos\_otros\_indices }\OtherTok{\textless{}{-}} \FunctionTok{setdiff}\NormalTok{(}\DecValTok{1}\SpecialCharTok{:}\DecValTok{5}\NormalTok{, indice\_equipo\_correcto)}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(equipos\_otros\_indices) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Se necesitan al menos 4 equipos diferentes en la muestra para evitar patrones"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Seleccionar 3 equipos diferentes para los distractores}
\NormalTok{equipos\_distractores\_indices }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(equipos\_otros\_indices, }\DecValTok{3}\NormalTok{)}
\NormalTok{equipo\_distractor1\_idx }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_distractores\_indices[}\DecValTok{1}\NormalTok{]}
\NormalTok{equipo\_distractor2\_idx }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_distractores\_indices[}\DecValTok{2}\NormalTok{]}
\NormalTok{equipo\_distractor3\_idx }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_distractores\_indices[}\DecValTok{3}\NormalTok{]}

\CommentTok{\# Obtener datos de los equipos seleccionados para distractores}
\NormalTok{equipo\_distractor1 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[equipo\_distractor1\_idx]}
\NormalTok{valor\_distractor1 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[equipo\_distractor1\_idx]}
\NormalTok{valor\_distractor1\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor\_distractor1)}

\NormalTok{equipo\_distractor2 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[equipo\_distractor2\_idx]}
\NormalTok{valor\_distractor2 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[equipo\_distractor2\_idx]}
\NormalTok{valor\_distractor2\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor\_distractor2)}

\NormalTok{equipo\_distractor3 }\OtherTok{\textless{}{-}}\NormalTok{ equipos\_seleccionados[equipo\_distractor3\_idx]}
\NormalTok{valor\_distractor3 }\OtherTok{\textless{}{-}}\NormalTok{ valores\_equipos[equipo\_distractor3\_idx]}
\NormalTok{valor\_distractor3\_fmt }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(valor\_distractor3)}

\CommentTok{\# POOL DE DISTRACTORES CON EQUIPOS ÚNICOS GARANTIZADOS}
\NormalTok{distractores\_por\_tipo }\OtherTok{\textless{}{-}} \FunctionTok{list}\NormalTok{()}

\CommentTok{\# TIPO 1: Distractores con equipo\_distractor1}
\NormalTok{distractores\_tipo1 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{()}

\CommentTok{\# 1A: Proporción correcta con equipo incorrecto}
\NormalTok{distractores\_tipo1 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo1,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_distractor1\_fmt, }\StringTok{" de cada "}\NormalTok{, tamano\_muestra\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor1, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 1B: Confusión muestra{-}población con equipo incorrecto}
\NormalTok{distractores\_tipo1 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo1,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_distractor1\_fmt, }\StringTok{" de cada "}\NormalTok{, poblacion\_total\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor1, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 1C: Fracción reducida con equipo incorrecto}
\NormalTok{fraccion\_dist1 }\OtherTok{\textless{}{-}} \FunctionTok{generar\_fraccion\_reducida}\NormalTok{(valor\_distractor1, tamano\_muestra)}
\ControlFlowTok{if}\NormalTok{ (fraccion\_dist1[}\DecValTok{1}\NormalTok{] }\SpecialCharTok{!=}\NormalTok{ valor\_distractor1 }\SpecialCharTok{||}\NormalTok{ fraccion\_dist1[}\DecValTok{2}\NormalTok{] }\SpecialCharTok{!=}\NormalTok{ tamano\_muestra) \{}
\NormalTok{  num\_dist1\_red }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(fraccion\_dist1[}\DecValTok{1}\NormalTok{])}
\NormalTok{  den\_dist1\_red }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(fraccion\_dist1[}\DecValTok{2}\NormalTok{])}
\NormalTok{  distractores\_tipo1 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo1,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, num\_dist1\_red, }\StringTok{" de cada "}\NormalTok{, den\_dist1\_red, }\StringTok{" "}\NormalTok{,}
\NormalTok{           termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor1, }\StringTok{"."}\NormalTok{))}
\NormalTok{\}}

\NormalTok{distractores\_por\_tipo[[}\StringTok{"tipo1"}\NormalTok{]] }\OtherTok{\textless{}{-}}\NormalTok{ distractores\_tipo1}

\CommentTok{\# TIPO 2: Distractores con equipo\_distractor2}
\NormalTok{distractores\_tipo2 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{()}

\CommentTok{\# 2A: Proporción correcta con equipo incorrecto}
\NormalTok{distractores\_tipo2 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo2,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_distractor2\_fmt, }\StringTok{" de cada "}\NormalTok{, tamano\_muestra\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor2, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 2B: Confusión muestra{-}población con equipo incorrecto}
\NormalTok{distractores\_tipo2 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo2,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_distractor2\_fmt, }\StringTok{" de cada "}\NormalTok{, poblacion\_total\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor2, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 2C: Fracción reducida con equipo incorrecto}
\NormalTok{fraccion\_dist2 }\OtherTok{\textless{}{-}} \FunctionTok{generar\_fraccion\_reducida}\NormalTok{(valor\_distractor2, tamano\_muestra)}
\ControlFlowTok{if}\NormalTok{ (fraccion\_dist2[}\DecValTok{1}\NormalTok{] }\SpecialCharTok{!=}\NormalTok{ valor\_distractor2 }\SpecialCharTok{||}\NormalTok{ fraccion\_dist2[}\DecValTok{2}\NormalTok{] }\SpecialCharTok{!=}\NormalTok{ tamano\_muestra) \{}
\NormalTok{  num\_dist2\_red }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(fraccion\_dist2[}\DecValTok{1}\NormalTok{])}
\NormalTok{  den\_dist2\_red }\OtherTok{\textless{}{-}} \FunctionTok{formatear\_entero}\NormalTok{(fraccion\_dist2[}\DecValTok{2}\NormalTok{])}
\NormalTok{  distractores\_tipo2 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo2,}
    \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, num\_dist2\_red, }\StringTok{" de cada "}\NormalTok{, den\_dist2\_red, }\StringTok{" "}\NormalTok{,}
\NormalTok{           termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor2, }\StringTok{"."}\NormalTok{))}
\NormalTok{\}}

\NormalTok{distractores\_por\_tipo[[}\StringTok{"tipo2"}\NormalTok{]] }\OtherTok{\textless{}{-}}\NormalTok{ distractores\_tipo2}

\CommentTok{\# TIPO 3: Distractores con equipo\_distractor3 o conceptuales}
\NormalTok{distractores\_tipo3 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{()}

\CommentTok{\# 3A: Proporción correcta con equipo incorrecto}
\NormalTok{distractores\_tipo3 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo3,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"alrededor de "}\NormalTok{, valor\_distractor3\_fmt, }\StringTok{" de cada "}\NormalTok{, tamano\_muestra\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_distractor3, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 3B: Confusión conceptual (sin equipo específico)}
\NormalTok{distractores\_tipo3 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo3,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"sólo "}\NormalTok{, tamano\_muestra\_fmt, }\StringTok{" de los "}\NormalTok{, poblacion\_total\_fmt, }\StringTok{" "}\NormalTok{,}
\NormalTok{         termino\_usuarios, }\StringTok{" del "}\NormalTok{, contexto,}
         \StringTok{" tienen preferencia por un equipo para ganar la "}\NormalTok{, competicion, }\StringTok{"."}\NormalTok{))}

\CommentTok{\# 3C: Equipo no incluido en muestra}
\NormalTok{equipo\_no\_incluido }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(}\FunctionTok{setdiff}\NormalTok{(equipos\_disponibles, equipos\_seleccionados), }\DecValTok{1}\NormalTok{)}
\NormalTok{distractores\_tipo3 }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_tipo3,}
  \FunctionTok{paste0}\NormalTok{(}\StringTok{"ninguno de los "}\NormalTok{, poblacion\_total\_fmt, }\StringTok{" "}\NormalTok{, termino\_usuarios,}
         \StringTok{" del "}\NormalTok{, contexto, }\StringTok{" da por favorito al "}\NormalTok{, equipo\_no\_incluido, }\StringTok{"."}\NormalTok{))}

\NormalTok{distractores\_por\_tipo[[}\StringTok{"tipo3"}\NormalTok{]] }\OtherTok{\textless{}{-}}\NormalTok{ distractores\_tipo3}

\CommentTok{\# SELECCIÓN GARANTIZADA DE EQUIPOS ÚNICOS}
\CommentTok{\# Seleccionar exactamente un distractor de cada tipo para garantizar diversidad}
\NormalTok{distractor\_final1 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(distractores\_por\_tipo[[}\StringTok{"tipo1"}\NormalTok{]], }\DecValTok{1}\NormalTok{)}
\NormalTok{distractor\_final2 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(distractores\_por\_tipo[[}\StringTok{"tipo2"}\NormalTok{]], }\DecValTok{1}\NormalTok{)}
\NormalTok{distractor\_final3 }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(distractores\_por\_tipo[[}\StringTok{"tipo3"}\NormalTok{]], }\DecValTok{1}\NormalTok{)}

\CommentTok{\# Crear lista de distractores únicos garantizados}
\NormalTok{distractores\_unicos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractor\_final1, distractor\_final2, distractor\_final3)}

\CommentTok{\# AÑADIR DISTRACTORES DE RESPALDO SI NO HAY SUFICIENTES}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(distractores\_unicos) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}} \FunctionTok{generar\_distractores\_respaldo}\NormalTok{(valor\_correcto, tamano\_muestra, poblacion\_total,}
\NormalTok{                                                        equipo\_correcto, contexto, termino\_usuarios)}
\NormalTok{  distractores\_respaldo }\OtherTok{\textless{}{-}}\NormalTok{ distractores\_respaldo[}\SpecialCharTok{!}\NormalTok{(distractores\_respaldo }\SpecialCharTok{\%in\%} \FunctionTok{c}\NormalTok{(respuesta\_correcta, distractores\_unicos))]}
\NormalTok{  distractores\_unicos }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_unicos, distractores\_respaldo)}
\NormalTok{  distractores\_unicos }\OtherTok{\textless{}{-}} \FunctionTok{unique}\NormalTok{(distractores\_unicos)}
\NormalTok{\}}

\CommentTok{\# SELECCIONAR EXACTAMENTE 3 DISTRACTORES ÚNICOS}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(distractores\_unicos) }\SpecialCharTok{\textgreater{}=} \DecValTok{3}\NormalTok{) \{}
\NormalTok{  distractores\_finales }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(distractores\_unicos, }\DecValTok{3}\NormalTok{)}
\NormalTok{\} }\ControlFlowTok{else}\NormalTok{ \{}
  \CommentTok{\# Caso extremo: usar todos los disponibles y completar con modificaciones}
\NormalTok{  distractores\_finales }\OtherTok{\textless{}{-}}\NormalTok{ distractores\_unicos}
  \ControlFlowTok{while}\NormalTok{ (}\FunctionTok{length}\NormalTok{(distractores\_finales) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
    \CommentTok{\# Generar distractor adicional modificando ligeramente uno existente}
\NormalTok{    base\_distractor }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(distractores\_finales, }\DecValTok{1}\NormalTok{)}
\NormalTok{    nuevo\_distractor }\OtherTok{\textless{}{-}} \FunctionTok{gsub}\NormalTok{(}\StringTok{"alrededor de"}\NormalTok{, }\StringTok{"aproximadamente"}\NormalTok{, base\_distractor)}
    \ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\NormalTok{(nuevo\_distractor }\SpecialCharTok{\%in\%} \FunctionTok{c}\NormalTok{(respuesta\_correcta, distractores\_finales))) \{}
\NormalTok{      distractores\_finales }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_finales, nuevo\_distractor)}
\NormalTok{    \} }\ControlFlowTok{else}\NormalTok{ \{}
      \CommentTok{\# Último recurso: modificar número ligeramente}
\NormalTok{      nuevo\_distractor }\OtherTok{\textless{}{-}} \FunctionTok{gsub}\NormalTok{(valor\_correcto\_fmt, }\FunctionTok{formatear\_entero}\NormalTok{(valor\_correcto }\SpecialCharTok{+} \DecValTok{1}\NormalTok{), base\_distractor)}
      \ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\NormalTok{(nuevo\_distractor }\SpecialCharTok{\%in\%} \FunctionTok{c}\NormalTok{(respuesta\_correcta, distractores\_finales))) \{}
\NormalTok{        distractores\_finales }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(distractores\_finales, nuevo\_distractor)}
\NormalTok{      \}}
\NormalTok{    \}}
\NormalTok{  \}}
\NormalTok{\}}

\CommentTok{\# CREAR OPCIONES FINALES Y MEZCLAR}
\NormalTok{opciones }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(respuesta\_correcta, distractores\_finales[}\DecValTok{1}\NormalTok{], distractores\_finales[}\DecValTok{2}\NormalTok{], distractores\_finales[}\DecValTok{3}\NormalTok{])}
\NormalTok{opciones\_mezcladas }\OtherTok{\textless{}{-}} \FunctionTok{sample}\NormalTok{(opciones)}
\NormalTok{indice\_correcto }\OtherTok{\textless{}{-}} \FunctionTok{which}\NormalTok{(opciones\_mezcladas }\SpecialCharTok{==}\NormalTok{ respuesta\_correcta)}

\CommentTok{\# Vector de solución para r{-}exams}
\NormalTok{solucion }\OtherTok{\textless{}{-}} \FunctionTok{rep}\NormalTok{(}\DecValTok{0}\NormalTok{, }\DecValTok{4}\NormalTok{)}
\NormalTok{solucion[indice\_correcto] }\OtherTok{\textless{}{-}} \DecValTok{1}

\CommentTok{\# VALIDACIONES FINALES ROBUSTAS}
\CommentTok{\# Verificar que hay exactamente 4 opciones únicas}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(}\FunctionTok{unique}\NormalTok{(opciones)) }\SpecialCharTok{!=} \DecValTok{4}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error crítico: No se pudieron generar 4 opciones únicas. Opciones: "}\NormalTok{, }\FunctionTok{paste}\NormalTok{(opciones, }\AttributeTok{collapse=}\StringTok{" | "}\NormalTok{))}
\NormalTok{\}}

\CommentTok{\# Verificar que la respuesta correcta está incluida}
\ControlFlowTok{if}\NormalTok{ (}\SpecialCharTok{!}\NormalTok{(respuesta\_correcta }\SpecialCharTok{\%in\%}\NormalTok{ opciones)) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error crítico: La respuesta correcta no está en las opciones finales"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Verificar coherencia matemática de la fracción reducida}
\ControlFlowTok{if}\NormalTok{ (numerador\_reducido }\SpecialCharTok{*}\NormalTok{ tamano\_muestra }\SpecialCharTok{!=}\NormalTok{ denominador\_reducido }\SpecialCharTok{*}\NormalTok{ valor\_correcto) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: La fracción reducida no es matemáticamente equivalente"}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Verificar que todas las opciones son diferentes}
\ControlFlowTok{for}\NormalTok{ (i }\ControlFlowTok{in} \DecValTok{1}\SpecialCharTok{:}\DecValTok{4}\NormalTok{) \{}
  \ControlFlowTok{for}\NormalTok{ (j }\ControlFlowTok{in} \DecValTok{1}\SpecialCharTok{:}\DecValTok{4}\NormalTok{) \{}
    \ControlFlowTok{if}\NormalTok{ (i }\SpecialCharTok{!=}\NormalTok{ j }\SpecialCharTok{\&\&}\NormalTok{ opciones[i] }\SpecialCharTok{==}\NormalTok{ opciones[j]) \{}
      \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Opciones duplicadas detectadas en posiciones "}\NormalTok{, i, }\StringTok{" y "}\NormalTok{, j)}
\NormalTok{    \}}
\NormalTok{  \}}
\NormalTok{\}}

\CommentTok{\# VALIDACIÓN ANTI{-}PATRÓN: Verificar diversidad de equipos mencionados}
\NormalTok{equipos\_mencionados }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{()}
\ControlFlowTok{for}\NormalTok{ (opcion }\ControlFlowTok{in}\NormalTok{ opciones) \{}
  \CommentTok{\# Extraer equipos mencionados en cada opción}
  \ControlFlowTok{for}\NormalTok{ (equipo }\ControlFlowTok{in}\NormalTok{ equipos\_seleccionados) \{}
    \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{grepl}\NormalTok{(equipo, opcion, }\AttributeTok{fixed =} \ConstantTok{TRUE}\NormalTok{)) \{}
\NormalTok{      equipos\_mencionados }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(equipos\_mencionados, equipo)}
\NormalTok{    \}}
\NormalTok{  \}}
  \CommentTok{\# También verificar equipos no incluidos}
  \ControlFlowTok{for}\NormalTok{ (equipo }\ControlFlowTok{in} \FunctionTok{setdiff}\NormalTok{(equipos\_disponibles, equipos\_seleccionados)) \{}
    \ControlFlowTok{if}\NormalTok{ (}\FunctionTok{grepl}\NormalTok{(equipo, opcion, }\AttributeTok{fixed =} \ConstantTok{TRUE}\NormalTok{)) \{}
\NormalTok{      equipos\_mencionados }\OtherTok{\textless{}{-}} \FunctionTok{c}\NormalTok{(equipos\_mencionados, equipo)}
\NormalTok{    \}}
\NormalTok{  \}}
\NormalTok{\}}

\CommentTok{\# Verificar que no hay más de una opción por equipo específico}
\NormalTok{tabla\_equipos }\OtherTok{\textless{}{-}} \FunctionTok{table}\NormalTok{(equipos\_mencionados)}
\NormalTok{equipos\_repetidos }\OtherTok{\textless{}{-}} \FunctionTok{names}\NormalTok{(tabla\_equipos)[tabla\_equipos }\SpecialCharTok{\textgreater{}} \DecValTok{1}\NormalTok{]}

\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(equipos\_repetidos) }\SpecialCharTok{\textgreater{}} \DecValTok{0}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error anti{-}patrón: Los siguientes equipos aparecen en múltiples opciones: "}\NormalTok{,}
       \FunctionTok{paste}\NormalTok{(equipos\_repetidos, }\AttributeTok{collapse=}\StringTok{", "}\NormalTok{),}
       \StringTok{". Esto crea un patrón detectable para estudiantes."}\NormalTok{)}
\NormalTok{\}}

\CommentTok{\# Verificar que hay suficiente diversidad (al menos 3 equipos diferentes mencionados)}
\ControlFlowTok{if}\NormalTok{ (}\FunctionTok{length}\NormalTok{(}\FunctionTok{unique}\NormalTok{(equipos\_mencionados)) }\SpecialCharTok{\textless{}} \DecValTok{3}\NormalTok{) \{}
  \FunctionTok{stop}\NormalTok{(}\StringTok{"Error: Insuficiente diversidad de equipos. Se requieren al menos 3 equipos diferentes mencionados."}\NormalTok{)}
\NormalTok{\}}
\end{Highlighting}
\end{Shaded}

\begin{Shaded}
\begin{Highlighting}[]
\FunctionTok{options}\NormalTok{(}\AttributeTok{OutDec =} \StringTok{"."}\NormalTok{)}

\CommentTok{\# Código Python para generar el gráfico de barras horizontal}
\NormalTok{codigo\_python }\OtherTok{\textless{}{-}} \FunctionTok{paste0}\NormalTok{(}\StringTok{"}
\StringTok{import matplotlib}
\StringTok{matplotlib.use(\textquotesingle{}Agg\textquotesingle{})}
\StringTok{import matplotlib.pyplot as plt}
\StringTok{import numpy as np}

\StringTok{\# Datos para el gráfico}
\StringTok{equipos = [\textquotesingle{}"}\NormalTok{, equipo1, }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, equipo2, }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, equipo3, }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, equipo4, }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, equipo5, }\StringTok{"\textquotesingle{}]}
\StringTok{valores = ["}\NormalTok{, valor1, }\StringTok{", "}\NormalTok{, valor2, }\StringTok{", "}\NormalTok{, valor3, }\StringTok{", "}\NormalTok{, valor4, }\StringTok{", "}\NormalTok{, valor5, }\StringTok{"]}
\StringTok{colores\_grafico = [\textquotesingle{}"}\NormalTok{, colores[}\DecValTok{1}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, colores[}\DecValTok{2}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, colores[}\DecValTok{3}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, colores[}\DecValTok{4}\NormalTok{], }\StringTok{"\textquotesingle{}, \textquotesingle{}"}\NormalTok{, colores[}\DecValTok{5}\NormalTok{], }\StringTok{"\textquotesingle{}]}

\StringTok{\# Crear figura}
\StringTok{fig, ax = plt.subplots(figsize=(8, 6))}

\StringTok{\# Crear gráfico de barras horizontal}
\StringTok{y\_pos = np.arange(len(equipos))}
\StringTok{barras = ax.barh(y\_pos, valores, color=colores\_grafico, edgecolor=\textquotesingle{}white\textquotesingle{}, linewidth=1)}

\StringTok{\# Configurar etiquetas y título}
\StringTok{ax.set\_yticks(y\_pos)}
\StringTok{ax.set\_yticklabels(equipos, fontsize=10, fontweight=\textquotesingle{}bold\textquotesingle{})}
\StringTok{ax.set\_xlabel(\textquotesingle{}Número de "}\NormalTok{, termino\_usuarios, }\StringTok{"\textquotesingle{}, fontsize=12, fontweight=\textquotesingle{}bold\textquotesingle{})}
\StringTok{ax.set\_title(\textquotesingle{}Equipos favoritos para ganar la "}\NormalTok{, competicion, }\StringTok{"\textquotesingle{}, fontsize=14, fontweight=\textquotesingle{}bold\textquotesingle{}, pad=20)}

\StringTok{\# Añadir valores en las barras}
\StringTok{for i, (barra, valor) in enumerate(zip(barras, valores)):}
\StringTok{    width = barra.get\_width()}
\StringTok{    ax.text(width + 0.5, barra.get\_y() + barra.get\_height()/2,}
\StringTok{            str(valor), ha=\textquotesingle{}left\textquotesingle{}, va=\textquotesingle{}center\textquotesingle{}, fontweight=\textquotesingle{}bold\textquotesingle{}, fontsize=10)}

\StringTok{\# Configurar límites del eje x}
\StringTok{ax.set\_xlim(0, max(valores) + 5)}

\StringTok{\# Añadir grilla sutil}
\StringTok{ax.grid(axis=\textquotesingle{}x\textquotesingle{}, alpha=0.3, linestyle=\textquotesingle{}{-}{-}\textquotesingle{})}
\StringTok{ax.set\_axisbelow(True)}

\StringTok{\# Ajustar diseño}
\StringTok{plt.tight\_layout()}

\StringTok{\# Guardar en múltiples formatos}
\StringTok{plt.savefig(\textquotesingle{}grafico\_barras.png\textquotesingle{}, dpi=150, bbox\_inches=\textquotesingle{}tight\textquotesingle{},}
\StringTok{           transparent=True, format=\textquotesingle{}png\textquotesingle{})}
\StringTok{plt.savefig(\textquotesingle{}grafico\_barras.pdf\textquotesingle{}, dpi=150, bbox\_inches=\textquotesingle{}tight\textquotesingle{},}
\StringTok{           transparent=True, format=\textquotesingle{}pdf\textquotesingle{})}
\StringTok{plt.close()}
\StringTok{"}\NormalTok{)}

\CommentTok{\# Ejecutar código Python}
\FunctionTok{py\_run\_string}\NormalTok{(codigo\_python)}
\end{Highlighting}
\end{Shaded}

\section{Question}\label{question}

Un canal de deportes realizó la encuesta a un grupo de sus 60000
suscriptores sobre la preferencia de su equipo favorito para ganar la
Copa del Mundo. Para esto escogió al azar a 100 suscriptores y les
preguntó sobre su equipo favorito para ganar dicha competición. Los
resultados se muestran en la gráfica.

\includegraphics[width=0.8\linewidth,height=\textheight,keepaspectratio]{grafico_barras.png}

De acuerdo con los datos obtenidos en la encuesta, es correcto afirmar
que

\subsection{Answerlist}\label{answerlist}

\begin{itemize}
\tightlist
\item
  alrededor de 20 de cada 60000 suscriptores del canal de deportes da
  por favorito al Países Bajos.
\item
  ninguno de los 60000 suscriptores del canal de deportes da por
  favorito al Brasil.
\item
  alrededor de 26 de cada 60000 suscriptores del canal de deportes da
  por favorito al Inglaterra.
\item
  alrededor de 17 de cada 100 suscriptores del canal de deportes da por
  favorito al Argentina.
\end{itemize}

\section{Solution}\label{solution}

Para resolver este problema, necesitamos interpretar correctamente los
datos del gráfico de barras y entender la diferencia entre muestra y
población total.

\subsubsection{Paso 1: Identificar los datos
conocidos}\label{paso-1-identificar-los-datos-conocidos}

\begin{itemize}
\tightlist
\item
  Un canal de deportes tiene un total de 60000 suscriptores.
\item
  Se realizó un(a) encuesta a una muestra de 100 suscriptores
  seleccionados al azar.
\item
  Según el gráfico, 17 suscriptores de la muestra prefieren al
  Argentina.
\end{itemize}

\subsubsection{Paso 2: Interpretar correctamente las
proporciones}\label{paso-2-interpretar-correctamente-las-proporciones}

Los datos del gráfico representan únicamente la muestra de 100
suscriptores, no la población total de 60000 suscriptores.

\subsubsection{Paso 3: Analizar cada
opción}\label{paso-3-analizar-cada-opciuxf3n}

\textbf{Opción correcta}: ``alrededor de 17 de cada 100 suscriptores del
canal de deportes da por favorito al Argentina.'' Esta opción es
correcta porque interpreta adecuadamente que 17 de cada 100 suscriptores
\textbf{de la muestra} prefieren al Argentina. Esto representa una
proporción del 17\% en la muestra.

\textbf{Análisis de distractores mejorados}:

\begin{itemize}
\item
  \textbf{Distractores con fracciones reducidas}: Opciones como ``17 de
  cada 100'' son matemáticamente equivalentes a la respuesta correcta
  (17 de cada 100), pero pueden confundir a estudiantes que no reconocen
  la equivalencia de fracciones. Aunque matemáticamente correctas, estas
  opciones son \textbf{más sutiles y desafiantes}.
\item
  \textbf{Distractores de confusión muestra-población}: Las opciones que
  mencionan proporciones sobre la población total de 60000 suscriptores
  son incorrectas, ya que la encuesta solo se realizó a 100 personas.
\item
  \textbf{Distractores de equipos incorrectos}: Las opciones que usan
  datos de otros equipos de la muestra malinterpretan cuál es el equipo
  de referencia en la pregunta.
\item
  \textbf{Distractores de generalización indebida}: Las opciones que
  afirman que ``ninguno'' prefiere cierto equipo son incorrectas porque
  no podemos hacer esa generalización a partir de una muestra limitada.
\item
  \textbf{Distractores de confusión conceptual}: Las opciones que
  confunden el tamaño de la muestra con las preferencias totales
  malinterpretan fundamentalmente los datos.
\end{itemize}

\subsubsection{Paso 4: Verificación
matemática}\label{paso-4-verificaciuxf3n-matemuxe1tica}

En la muestra de 100 suscriptores:

\begin{itemize}
\tightlist
\item
  Argentina: 17 suscriptores (17\%)
\item
  Países Bajos: 20 suscriptores (20\%)
\item
  Uruguay: 14 suscriptores (14\%)
\item
  Italia: 23 suscriptores (23\%)
\item
  Inglaterra: 26 suscriptores (26\%)
\end{itemize}

Total: 100 suscriptores = 100 suscriptores (correcto)

\subsubsection{Paso 5: Verificación de equivalencia de fracciones (para
distractores
desafiantes)}\label{paso-5-verificaciuxf3n-de-equivalencia-de-fracciones-para-distractores-desafiantes}

Es importante reconocer que las fracciones pueden expresarse de
diferentes formas equivalentes:

\begin{itemize}
\tightlist
\item
  \textbf{Fracción original}: 17 de cada 100 = 17/100
\item
  \textbf{Fracción reducida}: 17 de cada 100 = 17/100
\end{itemize}

\textbf{Verificación matemática de equivalencia}: 17/100 = 0.17 17/100 =
0.17

Ambas fracciones son \textbf{matemáticamente equivalentes}, pero la
fracción reducida puede ser más difícil de reconocer como correcta, lo
que aumenta el desafío del problema.

\subsubsection{Conclusión}\label{conclusiuxf3n}

La respuesta correcta interpreta adecuadamente que los datos del gráfico
se refieren a la muestra de 100 suscriptores, no a la población total.
Los distractores mejorados incluyen opciones con fracciones equivalentes
que requieren mayor análisis matemático para ser identificadas
correctamente.

\subsection{Answerlist}\label{answerlist-1}

\begin{itemize}
\tightlist
\item
  Falso
\item
  Falso
\item
  Falso
\item
  Verdadero
\end{itemize}

\section{Meta-information}\label{meta-information}

exname: proporciones\_encuesta\_deportiva extype: schoice exsolution:
0001 exshuffle: TRUE exsection:
Estadística\textbar Proporciones\textbar Interpretación de
gráficos\textbar Muestreo

\end{document}
