```{r generar_codigo_python}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Generar el código Python para la figura
codigo_python <- sprintf("
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.path import Path
import matplotlib

# Configuración general
matplotlib.rcParams['font.size'] = 10
plt.figure(figsize=(4, 4))

# Crear el cuadrado (marco) externo
square = patches.Rectangle((0, 0), 10, 10, linewidth=1, edgecolor='black', facecolor='none')
plt.gca().add_patch(square)

# Crear el rombo sombreado rotado dentro del cuadrado
rombo_x = [5, 10, 5, 0, 5]  # Coordenadas x del rombo (centro, derecha, centro, izquierda, centro)
rombo_y = [10, 5, 0, 5, 10]  # Coordenadas y del rombo (arriba, derecha, abajo, izquierda, arriba)

# Crear el polígono sombreado
rombo = patches.Polygon(xy=list(zip(rombo_x, rombo_y)), closed=True, facecolor='%s', alpha=0.8, linewidth=1, edgecolor='black')
plt.gca().add_patch(rombo)

# Líneas punteadas dentro del rombo (diagonales)
plt.plot([5, 5], [0, 10], 'k--', alpha=0.5)  # Línea vertical
plt.plot([0, 10], [5, 5], 'k--', alpha=0.5)  # Línea horizontal

# Añadir etiquetas de medidas
plt.text(-0.5, 5, '%d cm', ha='right', va='center')
plt.text(5, -0.5, '%d cm', ha='center', va='top')
plt.text(10.5, 5, '%d cm', ha='left', va='center', rotation=90)
plt.text(5, 10.5, '%d cm', ha='center', va='bottom')

# Añadir etiqueta h en el rombo
plt.text(6.5, 2.5, 'h', fontsize=12)

# Configuración de los ejes
plt.axis('equal')
plt.axis('off')
plt.xlim(-1, 11)
plt.ylim(-1, 11)
plt.tight_layout()

# Guardar la figura
plt.savefig('figura_geometrica.png', dpi=150, bbox_inches='tight')
plt.close()
", 
color_region, a, b, a, b)

# Ejecutar el código Python
py_run_string(codigo_python)
---
output:
  word_document: default
  html_document: default
  pdf_document: default
---
```{r setup, include=FALSE}
# Configuración para todos los formatos de salida
Sys.setlocale(category = "LC_NUMERIC", locale = "C")
options(OutDec = ".")

# Configurar el motor LaTeX globalmente
options(tikzLatex = "pdflatex")
options(tikzXelatex = FALSE)
options(tikzLatexPackages = c(
  "\\usepackage{tikz}",
  "\\usepackage{colortbl}",
  "\\usetikzlibrary{patterns,shapes,arrows}"
))

library(exams)
library(reticulate)
library(digest)
library(testthat)

typ <- match_exams_device()
options(scipen = 999)
knitr::opts_chunk$set(
  warning = FALSE,
  message = FALSE,
  fig.showtext = FALSE,
  fig.cap = "",
  fig.keep = 'all',
  dev = c("png", "pdf"),
  dpi = 150
)

# Configuración para chunks de Python
knitr::knit_engines$set(python = function(options) {
  knitr::engine_output(options, options$code, '')
})

# Asegurar que Python esté correctamente configurado
use_python(Sys.which("python"), required = TRUE)
```

```{r DefinicionDeVariables, message=FALSE, warning=FALSE, results='asis'}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Establecer semilla aleatoria
set.seed(sample(1:10000, 1))

# Aleatorizar nombres para el problema
nombres <- c("Samuel", "Ana", "Carlos", "Elena", "Javier", "Sofía", "Miguel", 
             "Laura", "Daniel", "Valeria", "Pablo", "Isabella", "Alejandro", 
             "Natalia", "Gabriel", "Valentina", "Eduardo", "Camila", "Diego", "Julia")
nombre_estudiante <- sample(nombres, 1)

# Aleatorizar verbos y sustantivos para la consigna
verbos_calculo <- c("calcular", "determinar", "hallar", "encontrar", "obtener")
verbo_calculo <- sample(verbos_calculo, 1)

# Aleatorizar tamaños para los catetos del triángulo
# Se aleatoriza a pero manteniendo relación matemática correcta para números enteros
lista_a <- c(3, 5, 6, 8, 9, 12, 15)
lista_parejas <- list(
  c(3, 4), c(5, 12), c(6, 8), c(8, 15), c(9, 12), c(12, 16), c(15, 20)
)
indice <- sample(1:length(lista_a), 1)
a <- lista_parejas[[indice]][1]
b <- lista_parejas[[indice]][2]

# Cálculos matemáticos
hipotenusa <- sqrt(a^2 + b^2)  # Teorema de Pitágoras
area_rombo <- hipotenusa^2     # Fórmula del área del rombo (h×h)

# Verificar que la hipotenusa sea un número entero (triángulo pitagórico)
if (hipotenusa == round(hipotenusa)) {
  hipotenusa_valor <- round(hipotenusa)
} else {
  hipotenusa_valor <- paste0("\\sqrt{", a^2+b^2, "} = ", round(hipotenusa, 2))
}

# Aleatorizar las opciones incorrectas cercanas al valor correcto
area_correcta <- area_rombo
variantes_incorrectas <- c(
  a * b,                   # Error común: usar el producto de los catetos
  (a * b) / 2,             # Error común: usar área del triángulo rectángulo
  (a + b)^2,               # Error común: suma al cuadrado
  a^2 + b^2                # Error común: suma de cuadrados sin raíz
)

# Aleatorizar el orden de las opciones
opciones <- c(area_correcta, sample(variantes_incorrectas, 3))
opciones <- round(opciones)  # Redondear todas las opciones a enteros
opciones <- unique(opciones) # Eliminar duplicados si los hay

# Si hay menos de 4 opciones después de eliminar duplicados, añadir nuevas
while(length(opciones) < 4) {
  nueva_opcion <- round(area_correcta * sample(c(0.8, 1.2, 1.5, 0.75), 1))
  if(!(nueva_opcion %in% opciones)) {
    opciones <- c(opciones, nueva_opcion)
  }
}

# Mezclar las opciones para que no siempre la correcta esté en la misma posición
opciones <- sample(opciones, 4)

# Vector de solución para r-exams (1 para la correcta, 0 para las incorrectas)
solucion <- as.integer(opciones == area_correcta)

# Aleatorizar elementos de presentación
colores_region <- c("gray", "lightgray", "darkgray", "lightblue", "lightgreen", 
                    "pink", "lavender", "lightyellow", "cyan", "beige")
color_region <- sample(colores_region, 1)

# Aleatorizar términos para la descripción
terminos_region <- c("región sombreada", "área sombreada", "superficie sombreada", 
                     "figura sombreada", "polígono sombreado")
termino_region <- sample(terminos_region, 1)

# Aleatorizar términos para el procedimiento
terminos_procedimiento <- c("procedimiento", "proceso", "método", "algoritmo", "secuencia")
termino_procedimiento <- sample(terminos_procedimiento, 1)

# Aleatorizar descripción de los pasos
descripciones_paso1 <- c(
  paste0("Determinar la medida de la hipotenusa del triángulo rectángulo de catetos a = ", a, " y b = ", b, 
         ", utilizando el teorema de Pitágoras."),
  paste0("Calcular la hipotenusa del triángulo rectángulo usando los catetos a = ", a, " y b = ", b, 
         " con el teorema de Pitágoras."),
  paste0("Hallar el valor de la hipotenusa del triángulo rectángulo cuyos catetos miden a = ", a, " y b = ", b, 
         " aplicando el teorema de Pitágoras.")
)
descripcion_paso1 <- sample(descripciones_paso1, 1)

descripciones_paso2 <- c(
  "Encontrar el área de la región sombreada, multiplicando h × h.",
  "Calcular el área sombreada como el producto h × h.",
  "Determinar el área de la región sombreada con la fórmula h × h."
)
descripcion_paso2 <- sample(descripciones_paso2, 1)
```

```{r generar_codigo_python}
options(OutDec = ".")  # Asegurar punto decimal en este chunk

# Generar el código Python para la figura
codigo_python <- sprintf("
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import numpy as np
from matplotlib.path import Path
import matplotlib

# Configuración general
matplotlib.rcParams['font.size'] = 10
plt.figure(figsize=(4, 4))

# Crear el cuadrado (marco) externo
square = patches.Rectangle((0, 0), 10, 10, linewidth=1, edgecolor='black', facecolor='none')
plt.gca().add_patch(square)

# Crear el rombo sombreado rotado dentro del cuadrado
rombo_x = [5, 10, 5, 0, 5]  # Coordenadas x del rombo (centro, derecha, centro, izquierda, centro)
rombo_y = [10, 5, 0, 5, 10]  # Coordenadas y del rombo (arriba, derecha, abajo, izquierda, arriba)

# Crear el polígono sombreado
rombo = patches.Polygon(xy=list(zip(rombo_x, rombo_y)), closed=True, facecolor='%s', alpha=0.8, linewidth=1, edgecolor='black')
plt.gca().add_patch(rombo)

# Líneas punteadas dentro del rombo (diagonales)
plt.plot([5, 5], [0, 10], 'k--', alpha=0.5)  # Línea vertical
plt.plot([0, 10], [5, 5], 'k--', alpha=0.5)  # Línea horizontal

# Añadir etiquetas de medidas
plt.text(-0.5, 5, '%d cm', ha='right', va='center')
plt.text(5, -0.5, '%d cm', ha='center', va='top')
plt.text(10.5, 5, '%d cm', ha='left', va='center', rotation=90)
plt.text(5, 10.5, '%d cm', ha='center', va='bottom')

# Añadir etiqueta h en el rombo
plt.text(6.5, 2.5, 'h', fontsize=12)

# Configuración de los ejes
plt.axis('equal')
plt.axis('off')
plt.xlim(-1, 11)
plt.ylim(-1, 11)
plt.tight_layout()

# Guardar la figura
plt.savefig('figura_geometrica.png', dpi=150, bbox_inches='tight')
plt.close()
", 
color_region, a, b, a, b)

# Generar el código Python para el diagrama de pasos
codigo_python_diagrama <- sprintf("
import matplotlib.pyplot as plt
import matplotlib.patches as patches

# Configuración general
plt.figure(figsize=(6, 2))

# Crear el recuadro del diagrama
recuadro = patches.Rectangle((0, 0), 10, 4, linewidth=1, edgecolor='black', facecolor='none')
plt.gca().add_patch(recuadro)

# Línea divisoria entre pasos
plt.plot([0, 10], [2, 2], 'k-', linewidth=0.5)

# Añadir texto para los pasos
plt.text(0.2, 3.3, 'Paso 1.', fontweight='bold')
plt.text(0.2, 2.8, '%s')

plt.text(0.2, 1.3, 'Paso 2.', fontweight='bold')
plt.text(0.2, 0.8, '%s')

# Añadir título del diagrama
plt.text(5, 4.2, 'Diagrama', fontweight='bold', ha='center')

# Configuración de los ejes
plt.axis('off')
plt.xlim(-0.5, 10.5)
plt.ylim(-0.5, 4.5)

# Guardar el diagrama
plt.savefig('diagrama_procedimiento.png', dpi=150, bbox_inches='tight')
plt.close()
", descripcion_paso1, descripcion_paso2)

# Ejecutar los códigos Python
py_run_string(codigo_python)
py_run_string(codigo_python_diagrama)
```

Question
========

`r nombre_estudiante` debe `r verbo_calculo` el área correspondiencat("![](figura_geometrica.png)")
te a la `r termino_region` en la figura. Para ello, efectúa el `r termino_procedimiento` que se muestra en el diagrama.

```{r figura, echo=FALSE, results='asis', fig.align='center'}
cat("![](figura_geometrica.png)")
```

```{r diagrama, echo=FALSE, results='asis', fig.align='center'}
cat("![](diagrama_procedimiento.png)")
```

Teniendo en cuenta la información anterior, ¿cuál es el área de la `r termino_region`?

Answerlist
----------
- `r opciones[1]` cm²
- `r opciones[2]` cm²  
- `r opciones[3]` cm²
- `r opciones[4]` cm²

Solution
========

Para resolver este problema, seguimos los pasos indicados en el diagrama:

**Paso 1**: 

Determinar la medida de la hipotenusa del triángulo rectángulo con catetos a = `r a` y b = `r b` usando el teorema de Pitágoras.

Aplicando el teorema de Pitágoras:

h² = a² + b²
h² = `r a`² + `r b`²
h² = `r a^2` + `r b^2`
h² = `r a^2 + b^2`
h = `r hipotenusa_valor`

**Paso 2**: 

Encontrar el área de la `r termino_region`, multiplicando h × h.

Área = h × h = `r hipotenusa`² = `r area_rombo`

Por lo tanto, el área de la `r termino_region` es `r area_correcta` cm².

Answerlist
----------
- `r if(solucion[1] == 1) "Verdadero" else "Falso"`
- `r if(solucion[2] == 1) "Verdadero" else "Falso"`
- `r if(solucion[3] == 1) "Verdadero" else "Falso"`
- `r if(solucion[4] == 1) "Verdadero" else "Falso"`

Meta-information
================
exname: area_region_sombreada_v1
extype: schoice
exsolution: `r paste(solucion, collapse="")`
exshuffle: TRUE
exsection: Geometría
